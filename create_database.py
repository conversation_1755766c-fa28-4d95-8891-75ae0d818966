import sqlite3
import os

DATABASE_NAME = 'eve_trader.db'

def create_connection(db_file):
    """ create a database connection to a SQLite database """
    conn = None
    try:
        conn = sqlite3.connect(db_file)
        print(f"SQLite version: {sqlite3.sqlite_version}")
        print(f"Successfully connected to {db_file}")
        return conn
    except sqlite3.Error as e:
        print(e)
    return conn

def create_table(conn, create_table_sql):
    """ create a table from the create_table_sql statement """
    try:
        c = conn.cursor()
        c.execute(create_table_sql)
        print(f"Table created successfully: {create_table_sql.split('(')[0].split(' ')[-1]}")
    except sqlite3.Error as e:
        print(f"Error creating table: {e}")

def main():
    # Create database if it doesn't exist
    conn = create_connection(DATABASE_NAME)

    if conn is not None:
        # Create api_tokens table
        sql_create_api_tokens_table = """
        CREATE TABLE IF NOT EXISTS api_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            character_id INTEGER NOT NULL UNIQUE,
            character_name TEXT NOT NULL,
            access_token TEXT NOT NULL,
            refresh_token TEXT NOT NULL,
            token_type TEXT NOT NULL,
            expires_at DATETIME NOT NULL,
            scopes TEXT NOT NULL,
            last_refreshed DATETIME NOT NULL
        );
        """

        # Create api_cache table
        sql_create_api_cache_table = """
        CREATE TABLE IF NOT EXISTS api_cache (
            cache_key TEXT PRIMARY KEY,
            endpoint TEXT NOT NULL,
            parameters TEXT NOT NULL,
            response_data BLOB NOT NULL,
            cached_at DATETIME NOT NULL,
            expires_at DATETIME NOT NULL,
            etag TEXT
        );
        """

        # Create transactions table
        sql_create_transactions_table = """
        CREATE TABLE IF NOT EXISTS transactions (
            tx_id INTEGER PRIMARY KEY,
            timestamp DATETIME NOT NULL,
            item_id INTEGER NOT NULL,
            item_name TEXT NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price DECIMAL(16,2) NOT NULL,
            total_price DECIMAL(20,2) NOT NULL,
            transaction_type TEXT NOT NULL CHECK(transaction_type IN ('BUY','SELL')),
            order_id INTEGER,
            broker_fee DECIMAL(16,2) DEFAULT 0,
            sales_tax DECIMAL(16,2) DEFAULT 0,
            net_amount DECIMAL(20,2) NOT NULL,
            strategy TEXT,
            profit DECIMAL(20,2),
            location_id INTEGER NOT NULL DEFAULT 60003760,
            is_buy_order BOOLEAN,
            client_id INTEGER,
            journal_ref_id INTEGER UNIQUE,
            notes TEXT
        );
        """

        # Create orders table
        sql_create_orders_table = """
        CREATE TABLE IF NOT EXISTS orders (
            order_id INTEGER PRIMARY KEY,
            item_id INTEGER NOT NULL,
            item_name TEXT NOT NULL,
            order_type TEXT NOT NULL CHECK(order_type IN ('BUY','SELL')),
            quantity_original INTEGER NOT NULL,
            quantity_remaining INTEGER NOT NULL,
            price DECIMAL(16,2) NOT NULL,
            placed_time DATETIME NOT NULL,
            fulfilled_time DATETIME,
            expired BOOLEAN DEFAULT FALSE,
            canceled BOOLEAN DEFAULT FALSE,
            broker_fee DECIMAL(16,2) DEFAULT 0,
            relist_count INTEGER DEFAULT 0,
            relist_fees DECIMAL(16,2) DEFAULT 0,
            strategy TEXT,
            location_id INTEGER NOT NULL DEFAULT 60003760,
            range TEXT,
            escrow DECIMAL(20,2),
            is_corporation BOOLEAN DEFAULT FALSE,
            esi_expires DATETIME,
            last_updated DATETIME
        );
        """



        # Create eve_items table for item ID to name mapping
        sql_create_eve_items_table = """
        CREATE TABLE IF NOT EXISTS eve_items (
            item_id INTEGER PRIMARY KEY,
            item_name TEXT NOT NULL,
            group_id INTEGER,
            group_name TEXT,
            category_id INTEGER,
            category_name TEXT,
            published BOOLEAN DEFAULT 1,
            market_group_id INTEGER,
            volume REAL,
            last_updated DATETIME NOT NULL
        );
        """

        # Execute table creation
        create_table(conn, sql_create_api_tokens_table)
        create_table(conn, sql_create_api_cache_table)
        create_table(conn, sql_create_transactions_table)
        create_table(conn, sql_create_orders_table)
        create_table(conn, sql_create_eve_items_table)

        # Create indexes
        print("Creating indexes...")
        try:
            cursor = conn.cursor()
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON transactions (timestamp);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_transactions_item_id ON transactions (item_id);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_transactions_strategy ON transactions (strategy);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_orders_item_id ON orders (item_id);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_orders_fulfilled_time ON orders (fulfilled_time);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_api_cache_expires_at ON api_cache (expires_at);")

            # Indexes for eve_items table
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_eve_items_name ON eve_items (item_name);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_eve_items_group ON eve_items (group_id);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_eve_items_category ON eve_items (category_id);")
            print("Indexes created successfully.")
        except sqlite3.Error as e:
            print(f"Error creating indexes: {e}")

        conn.close()
        print("Database setup complete.")
    else:
        print("Error! cannot create the database connection.")

if __name__ == '__main__':
    main()
