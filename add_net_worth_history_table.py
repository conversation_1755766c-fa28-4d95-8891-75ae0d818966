#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add the net_worth_history table to the database.

This script:
1. Creates the net_worth_history table if it doesn't exist
2. Creates indexes for faster lookups
3. Creates a view for latest net worth data per character
"""

import os
import sqlite3
import sys

# Use absolute path for database to avoid relative path issues
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATABASE_NAME = os.path.join(BASE_DIR, 'eve_trader.db')

def create_connection(db_file):
    """ Create a database connection to a SQLite database """
    conn = None
    try:
        conn = sqlite3.connect(db_file)
        print(f"SQLite version: {sqlite3.sqlite_version}")
        print(f"Successfully connected to {db_file}")
        return conn
    except sqlite3.Error as e:
        print(e)
    return conn

def create_table(conn, create_table_sql):
    """ Create a table from the create_table_sql statement """
    try:
        c = conn.cursor()
        c.execute(create_table_sql)
        print(f"Table created successfully: {create_table_sql.split('(')[0].split(' ')[-1]}")
    except sqlite3.Error as e:
        print(f"Error creating table: {e}")

def create_index(conn, create_index_sql):
    """ Create an index from the create_index_sql statement """
    try:
        c = conn.cursor()
        c.execute(create_index_sql)
        print(f"Index created successfully: {create_index_sql.split('(')[0].split(' ')[-1]}")
    except sqlite3.Error as e:
        print(f"Error creating index: {e}")

def create_view(conn, create_view_sql):
    """ Create a view from the create_view_sql statement """
    try:
        c = conn.cursor()
        c.execute(create_view_sql)
        print(f"View created successfully: {create_view_sql.split(' ')[2]}")
    except sqlite3.Error as e:
        print(f"Error creating view: {e}")

def main():
    # Create database connection
    conn = create_connection(DATABASE_NAME)
    if conn is None:
        print("Error! Cannot create the database connection.")
        return

    # Create net_worth_history table
    sql_create_net_worth_history_table = """
    CREATE TABLE IF NOT EXISTS net_worth_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        character_id INTEGER NOT NULL,
        timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        wallet_balance DECIMAL(20,2) NOT NULL,
        escrow_total DECIMAL(20,2) NOT NULL,
        sell_orders_total DECIMAL(20,2) NOT NULL,
        net_worth_total DECIMAL(20,2) NOT NULL,
        UNIQUE(character_id, timestamp)
    );
    """
    create_table(conn, sql_create_net_worth_history_table)

    # Create indexes
    sql_create_character_id_index = """
    CREATE INDEX IF NOT EXISTS idx_net_worth_history_character_id ON net_worth_history(character_id);
    """
    create_index(conn, sql_create_character_id_index)

    sql_create_timestamp_index = """
    CREATE INDEX IF NOT EXISTS idx_net_worth_history_timestamp ON net_worth_history(timestamp);
    """
    create_index(conn, sql_create_timestamp_index)

    # Create view for latest net worth data
    sql_create_latest_net_worth_view = """
    CREATE VIEW IF NOT EXISTS latest_net_worth AS
    SELECT n.*
    FROM net_worth_history n
    INNER JOIN (
        SELECT character_id, MAX(timestamp) as max_timestamp
        FROM net_worth_history
        GROUP BY character_id
    ) latest ON n.character_id = latest.character_id 
        AND n.timestamp = latest.max_timestamp;
    """
    create_view(conn, sql_create_latest_net_worth_view)

    # Close connection
    conn.close()
    print("Database migration completed successfully.")

if __name__ == '__main__':
    main()
