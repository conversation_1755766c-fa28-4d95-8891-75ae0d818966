-- Add location_id column to market_data table
-- This script adds the location_id column to the market_data table
-- and updates the latest_market_data view to include location_id in grouping

-- Add location_id column to market_data table if it doesn't exist
ALTER TABLE market_data ADD COLUMN location_id INTEGER DEFAULT 60003760; -- Default to JITA_STATION_ID

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_market_data_location_id ON market_data(location_id);

-- Drop the existing view
DROP VIEW IF EXISTS latest_market_data;

-- Recreate the view to include location_id in grouping
CREATE VIEW latest_market_data AS
SELECT m.*
FROM market_data m
INNER JOIN (
    SELECT item_id, region_id, location_id, MAX(timestamp) as max_timestamp
    FROM market_data
    GROUP BY item_id, region_id, location_id
) latest ON m.item_id = latest.item_id 
    AND m.region_id = latest.region_id 
    AND m.location_id = latest.location_id
    AND m.timestamp = latest.max_timestamp;

-- Update the UNIQUE constraint (this requires recreating the table in SQLite)
-- We'll create a temporary table with the new schema
CREATE TABLE market_data_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    region_id INTEGER NOT NULL DEFAULT 10000002, -- Default to The Forge (Jita)
    sell_price REAL,
    buy_price REAL,
    daily_volume INTEGER,
    weekly_volume INTEGER,
    price_volatility REAL,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    location_id INTEGER DEFAULT 60003760, -- Default to JITA_STATION_ID
    UNIQUE(item_id, region_id, location_id, timestamp)
);

-- Copy data from the old table to the new one
INSERT INTO market_data_new (
    id, item_id, region_id, sell_price, buy_price, 
    daily_volume, weekly_volume, price_volatility, timestamp, location_id
)
SELECT 
    id, item_id, region_id, sell_price, buy_price, 
    daily_volume, weekly_volume, price_volatility, timestamp, location_id
FROM market_data;

-- Drop the old table
DROP TABLE market_data;

-- Rename the new table to the original name
ALTER TABLE market_data_new RENAME TO market_data;

-- Recreate the indexes
CREATE INDEX IF NOT EXISTS idx_market_data_item_id ON market_data(item_id);
CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp);
CREATE INDEX IF NOT EXISTS idx_market_data_location_id ON market_data(location_id);

-- Insert a migration record
INSERT OR IGNORE INTO migration_status (migration_name, completed, completed_date) 
VALUES ('add_location_id_to_market_data', 1, datetime('now'));
