"""
Check the contents of the database to debug item name issues.
"""

import sqlite3
import os

def main():
    """Main function to check the database."""
    db_path = "eve_trader.db"
    
    if not os.path.exists(db_path):
        print(f"Database file not found: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    
    try:
        # Check transactions table
        cursor = conn.cursor()
        cursor.execute("SELECT tx_id, item_id, item_name FROM transactions LIMIT 10")
        rows = cursor.fetchall()
        
        print("Transactions table:")
        for row in rows:
            print(f"  TX ID: {row['tx_id']}, Item ID: {row['item_id']}, Item Name: {row['item_name']}")
        
        # Check if eve_items table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='eve_items'")
        if cursor.fetchone():
            # Check eve_items table
            cursor.execute("SELECT COUNT(*) as count FROM eve_items")
            count = cursor.fetchone()['count']
            print(f"\nEve Items table contains {count} items")
            
            # Check a few specific items
            test_ids = [19079, 34, 35, 36, 37]  # Include the problematic ID 19079
            for item_id in test_ids:
                cursor.execute("SELECT item_id, item_name FROM eve_items WHERE item_id = ?", (item_id,))
                row = cursor.fetchone()
                if row:
                    print(f"  Item ID: {row['item_id']}, Item Name: {row['item_name']}")
                else:
                    print(f"  Item ID: {item_id} not found in eve_items table")
        else:
            print("\nEve Items table does not exist!")
    
    except sqlite3.Error as e:
        print(f"Database error: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    main()
