from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from typing import Optional

@dataclass
class Order:
    """ Represents a market order corresponding to the 'orders' table. """
    order_id: int
    item_id: int
    item_name: str
    order_type: str # 'BUY' or 'SELL'
    quantity_original: int
    quantity_remaining: int
    price: Decimal
    placed_time: datetime
    location_id: int
    character_id: int  # Added to track which character the order belongs to

    # Optional/Defaulted fields
    fulfilled_time: Optional[datetime] = None
    expired: bool = False
    canceled: bool = False
    broker_fee: Decimal = Decimal('0') # Can be calculated/updated later
    relist_count: int = 0
    relist_fees: Decimal = Decimal('0')
    strategy: Optional[str] = None
    range: Optional[str] = None # ESI field
    escrow: Optional[Decimal] = None # ESI field
    is_corporation: bool = False # ESI field
    esi_expires: Optional[datetime] = None # ESI cache expiry
    last_updated: Optional[datetime] = None # Local update timestamp

    def __post_init__(self):
        # Basic validation or type coercion
        if not isinstance(self.price, Decimal):
            self.price = Decimal(str(self.price))
        if not isinstance(self.broker_fee, Decimal):
            self.broker_fee = Decimal(str(self.broker_fee))
        if not isinstance(self.relist_fees, Decimal):
            self.relist_fees = Decimal(str(self.relist_fees))
        if self.escrow is not None and not isinstance(self.escrow, Decimal):
            self.escrow = Decimal(str(self.escrow))
        if self.order_type not in ('BUY', 'SELL'):
            raise ValueError("order_type must be 'BUY' or 'SELL'")

    @classmethod
    def from_dict(cls, data: dict):
        """ Creates an Order object from a dictionary (e.g., DB row or ESI response). """
        # Map ESI fields if necessary, handle type conversions
        # Example: ESI might use 'type_id' instead of 'item_id'
        if 'type_id' in data and 'item_id' not in data:
            data['item_id'] = data.pop('type_id')
        # Add more mappings as needed based on ESI structure vs DB structure
        return cls(**data)

    def to_dict(self):
        """ Converts the Order object to a dictionary for DB insertion/update. """
        d = self.__dict__.copy()
        for key, value in d.items():
            if isinstance(value, Decimal):
                d[key] = str(value)
            elif isinstance(value, datetime):
                d[key] = value.isoformat()
            elif isinstance(value, bool):
                # Convert boolean to integer for SQLite compatibility if needed
                # (Though SQLite often handles BOOLEAN as 0/1 implicitly)
                d[key] = int(value)
        return d

    def is_active(self):
        """ Checks if the order is likely still active based on status fields. """
        return self.quantity_remaining > 0 and not self.expired and not self.canceled and self.fulfilled_time is None
