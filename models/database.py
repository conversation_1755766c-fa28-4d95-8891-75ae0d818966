import sqlite3
from contextlib import contextmanager
import os
from decimal import Decimal # Use Decimal for financial calculations
from datetime import datetime, timezone

DATABASE_NAME = 'eve_trader.db'
# Determine the absolute path to the database file relative to this script's location
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # Moves up one level from models/
DB_PATH = os.path.join(BASE_DIR, DATABASE_NAME)

# --- Type Adapters/Converters for SQLite ---
# SQLite doesn't have native DECIMAL or DATETIME types, so we need converters.

def adapt_decimal(d):
    """Convert Decimal to string for SQLite."""
    return str(d)

def convert_decimal(s):
    """Convert string from SQLite back to Decimal."""
    return Decimal(s.decode('utf-8'))

def adapt_datetime(dt):
    """Convert datetime object to ISO 8601 string."""
    return dt.isoformat()

def convert_datetime(ts):
    """Convert ISO 8601 string from SQLite back to datetime object."""
    # Handle potential timezone info if stored, otherwise assume UTC or naive
    try:
        # Attempt to parse with timezone offset
        return datetime.fromisoformat(ts.decode('utf-8'))
    except ValueError:
        # Fallback for formats without timezone (or handle as needed)
        # This might need adjustment based on how datetimes are stored (UTC vs local)
        return datetime.fromisoformat(ts.decode('utf-8'))


# Register the adapters and converters
sqlite3.register_adapter(Decimal, adapt_decimal)
sqlite3.register_converter("DECIMAL", convert_decimal)
# Note: DATETIME conversion is often handled implicitly by the sqlite3 module
# if detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES is used.
# However, explicit registration can be more robust.
# sqlite3.register_adapter(datetime, adapt_datetime)
# sqlite3.register_converter("DATETIME", convert_datetime) # Use DATETIME as the type name

class DatabaseManager:
    """
    Manages connections and operations for the SQLite database.
    Uses context managers for safe connection handling.
    """
    def __init__(self, db_path=DB_PATH):
        self.db_path = db_path
        if not os.path.exists(self.db_path):
            print(f"Warning: Database file not found at {self.db_path}. Run setup_database.py first.")
            # Consider raising an error or attempting setup here

    @contextmanager
    def get_connection(self):
        """Provides a database connection using a context manager."""
        # PARSE_DECLTYPES: Parse based on declared column type
        # PARSE_COLNAMES: Parse based on column name (e.g., "col [datetime]")
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES)
            # Use Row factory for dict-like access to columns
            conn.row_factory = sqlite3.Row
            print(f"Database connection opened to {self.db_path}")
            yield conn
        except sqlite3.Error as e:
            print(f"Database Error: {e}")
            # Optionally re-raise the error or handle it
            raise # Re-raise to signal connection failure
        finally:
            if conn:
                conn.close()
                print(f"Database connection closed to {self.db_path}")

    def execute_query(self, query, params=(), fetch_one=False, fetch_all=False):
        """Executes a read query (SELECT) and returns results."""
        with self.get_connection() as conn:
            try:
                cursor = conn.cursor()
                cursor.execute(query, params)
                if fetch_one:
                    return cursor.fetchone()
                if fetch_all:
                    return cursor.fetchall()
                # If neither fetch_one nor fetch_all is True, maybe just checking existence?
                # Or perhaps this method should always fetch something?
                # For now, return None if no fetch specified.
                return None
            except sqlite3.Error as e:
                print(f"Error executing query: {e}")
                print(f"Query: {query}")
                print(f"Params: {params}")
                return None # Indicate error

    def execute_script(self, script):
        """Executes a SQL script (potentially multiple statements)."""
        with self.get_connection() as conn:
            try:
                cursor = conn.cursor()
                cursor.executescript(script)
                conn.commit()
                print("Script executed successfully.")
                return True
            except sqlite3.Error as e:
                print(f"Error executing script: {e}")
                conn.rollback()
                return False

    def execute_update(self, query, params=()):
        """Executes an update/insert/delete query."""
        with self.get_connection() as conn:
            try:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                print(f"Update executed successfully. Rows affected: {cursor.rowcount}")
                return cursor.lastrowid # Return the ID of the inserted row, if applicable
            except sqlite3.Error as e:
                print(f"Error executing update: {e}")
                print(f"Query: {query}")
                print(f"Params: {params}")
                conn.rollback()
                return None # Indicate error

# Example Usage (Optional)
if __name__ == '__main__':
    print(f"Database path: {DB_PATH}")
    db_manager = DatabaseManager()

    # Example: Try to fetch from a table (assuming setup_database.py ran)
    print("\n--- Testing Connection & Fetch ---")
    try:
        tokens = db_manager.execute_query("SELECT * FROM api_tokens", fetch_all=True)
        if tokens is not None:
            if tokens:
                print(f"Found {len(tokens)} token(s) in api_tokens table.")
                # print(tokens[0]) # Print first row if exists
            else:
                print("api_tokens table is empty.")
        else:
            print("Failed to query api_tokens table (maybe it doesn't exist yet?).")

        # Example: Insert dummy fee rate (demonstrates Decimal usage)
        print("\n--- Testing Insert with Decimal ---")
        dummy_date = datetime.now(timezone.utc)
        insert_id = db_manager.execute_update("""
            INSERT OR IGNORE INTO fee_rates
            (effective_date, broker_fee_base, sales_tax_base, broker_fee_effective, sales_tax_effective, relisting_fee_modifier, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (dummy_date, Decimal('0.03'), Decimal('0.075'), Decimal('0.01'), Decimal('0.033'), Decimal('0.5'), 'Test Rate'))

        if insert_id is not None:
             print(f"Inserted dummy fee rate, ID (or 0 if ignored): {insert_id}")
             # Verify insertion
             rate = db_manager.execute_query("SELECT * FROM fee_rates WHERE effective_date = ?", (dummy_date,), fetch_one=True)
             if rate:
                 print("Verified insertion:")
                 print(f"  Effective Date: {rate['effective_date']} (Type: {type(rate['effective_date'])})")
                 print(f"  Broker Fee Base: {rate['broker_fee_base']} (Type: {type(rate['broker_fee_base'])})")
                 print(f"  Sales Tax Base: {rate['sales_tax_base']} (Type: {type(rate['sales_tax_base'])})")
             else:
                 print("Could not verify insertion (maybe ignored due to existing date).")

    except Exception as e:
        print(f"An error occurred during database testing: {e}")
