from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from typing import Optional

@dataclass
class Transaction:
    """ Represents a single market transaction corresponding to the 'transactions' table. """
    tx_id: int
    timestamp: datetime
    item_id: int
    item_name: str
    quantity: int
    unit_price: Decimal
    total_price: Decimal
    transaction_type: str # 'BUY' or 'SELL'
    net_amount: Decimal
    location_id: int
    journal_ref_id: int # Should be unique
    character_id: int  # Added to track which character the transaction belongs to

    # Optional fields
    order_id: Optional[int] = None
    broker_fee: Optional[Decimal] = Decimal('0') # Default to 0 if not provided
    sales_tax: Optional[Decimal] = Decimal('0') # Default to 0 if not provided
    strategy: Optional[str] = None
    profit: Optional[Decimal] = None
    is_buy_order: Optional[bool] = None # ESI provides this
    client_id: Optional[int] = None # ESI provides this
    notes: Optional[str] = None

    def __post_init__(self):
        # Basic validation or type coercion if needed
        if not isinstance(self.unit_price, Decimal):
            self.unit_price = Decimal(str(self.unit_price))
        if not isinstance(self.total_price, Decimal):
            self.total_price = Decimal(str(self.total_price))
        if not isinstance(self.net_amount, Decimal):
            self.net_amount = Decimal(str(self.net_amount))
        if self.broker_fee is not None and not isinstance(self.broker_fee, Decimal):
            self.broker_fee = Decimal(str(self.broker_fee))
        if self.sales_tax is not None and not isinstance(self.sales_tax, Decimal):
            self.sales_tax = Decimal(str(self.sales_tax))
        if self.profit is not None and not isinstance(self.profit, Decimal):
            self.profit = Decimal(str(self.profit))
        if self.transaction_type not in ('BUY', 'SELL'):
            raise ValueError("transaction_type must be 'BUY' or 'SELL'")

    @classmethod
    def from_dict(cls, data: dict):
        """ Creates a Transaction object from a dictionary (e.g., DB row). """
        # Handle potential type differences from DB if necessary
        data_copy = data.copy()

        # Handle timestamp conversion if it's a string
        if 'timestamp' in data_copy and isinstance(data_copy['timestamp'], str):
            try:
                # Handle ISO format with or without timezone
                data_copy['timestamp'] = datetime.fromisoformat(data_copy['timestamp'].replace('Z', '+00:00'))
            except ValueError:
                # If that fails, try a more generic approach
                data_copy['timestamp'] = datetime.strptime(data_copy['timestamp'], '%Y-%m-%d %H:%M:%S')

        return cls(**data_copy)

    def to_dict(self):
        """ Converts the Transaction object to a dictionary for DB insertion/update. """
        # Convert Decimal back to string if adapter isn't automatically handling it everywhere
        d = self.__dict__.copy()
        for key, value in d.items():
            if isinstance(value, Decimal):
                d[key] = str(value)
            elif isinstance(value, datetime):
                d[key] = value.isoformat() # Ensure consistent ISO format
        return d

    def get_batch_links(self, db_manager):
        """
        Get the inventory batch links for this transaction.
        Only applicable for SELL transactions.

        Args:
            db_manager: Database manager instance

        Returns:
            List of batch link records, or empty list if none found
        """
        if self.transaction_type != 'SELL':
            return []

        rows = db_manager.execute_query(
            """
            SELECT l.*, b.item_name, b.purchase_date, b.strategy_intent, b.order_id, b.trade_execution_id
            FROM transaction_batch_links l
            JOIN inventory_batches b ON l.batch_id = b.batch_id
            WHERE l.transaction_id = ?
            """,
            (self.tx_id,),
            fetch_all=True
        )

        return [dict(row) for row in rows] if rows else []
