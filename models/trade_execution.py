"""
Trade Execution Models for TinyTrader

This module defines models related to trade execution, including:
- ItemCandidate: Represents an item that has been analyzed for trading
- TradeExecution: Represents a trade execution attempt
"""

from dataclasses import dataclass, field
from decimal import Decimal
from typing import Optional, List, Dict, Any
from datetime import datetime

@dataclass
class ItemCandidate:
    """
    Represents an item that has been analyzed for trading.

    Attributes:
        item_id: EVE Online item ID
        item_name: Name of the item
        best_buy_price: Current highest buy price
        best_sell_price: Current lowest sell price
        daily_volume: Estimated daily volume
        margin_percent: Calculated margin percentage
        expected_profit: Expected profit per unit
        total_expected_profit: Expected profit for all units that could be purchased
        region_id: Region ID where the market data was fetched
        timestamp: When this analysis was performed
    """
    item_id: int
    item_name: str
    best_buy_price: Decimal
    best_sell_price: Decimal
    daily_volume: int
    margin_percent: Decimal
    expected_profit: Decimal
    total_expected_profit: Decimal
    region_id: int
    timestamp: datetime = field(default_factory=lambda: datetime.now())

    def __post_init__(self):
        """Validate and convert types after initialization."""
        # Convert to Decimal if needed
        for attr in ['best_buy_price', 'best_sell_price', 'margin_percent',
                     'expected_profit', 'total_expected_profit']:
            value = getattr(self, attr)
            if not isinstance(value, Decimal):
                setattr(self, attr, Decimal(str(value)))

@dataclass
class TradeExecution:
    """
    Represents a trade execution attempt.

    Attributes:
        execution_id: Unique identifier for this execution
        config_id: ID of the trade configuration used
        item_id: EVE Online item ID of the selected item
        item_name: Name of the selected item
        buy_price: Price at which buy orders were placed
        quantity: Number of units ordered
        total_cost: Total cost of the order
        expected_profit: Expected profit for this trade
        expected_profit_percent: Expected profit percentage
        order_id: ID of the placed order (if successful)
        timestamp: When this execution was performed
        status: Status of the execution (success, failed, etc.)
        error_message: Error message if the execution failed
        character_id: ID of the character who executed the trade
        notes: Optional notes about this execution
    """
    execution_id: Optional[int] = None
    config_id: Optional[int] = None
    item_id: int = 0
    item_name: str = ""
    buy_price: Decimal = Decimal('0')
    quantity: int = 0
    total_cost: Decimal = Decimal('0')
    expected_profit: Decimal = Decimal('0')
    expected_profit_percent: Decimal = Decimal('0')
    order_id: Optional[int] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now())
    status: str = "pending"  # pending, success, failed
    error_message: Optional[str] = None
    character_id: Optional[int] = None
    notes: Optional[str] = None

    def __post_init__(self):
        """Validate and convert types after initialization."""
        # Convert to Decimal if needed
        for attr in ['buy_price', 'total_cost', 'expected_profit', 'expected_profit_percent']:
            value = getattr(self, attr)
            if not isinstance(value, Decimal):
                setattr(self, attr, Decimal(str(value)))

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TradeExecution':
        """Create a TradeExecution from a dictionary."""
        # Handle datetime fields
        if 'timestamp' in data and isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])

        return cls(**data)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the TradeExecution to a dictionary for DB storage."""
        result = self.__dict__.copy()

        # Convert Decimal to string
        for decimal_field in ['buy_price', 'total_cost', 'expected_profit', 'expected_profit_percent']:
            if decimal_field in result:
                result[decimal_field] = str(result[decimal_field])

        # Convert datetime objects to ISO format strings
        if result['timestamp'] and isinstance(result['timestamp'], datetime):
            result['timestamp'] = result['timestamp'].isoformat()

        return result
