"""
Trade Configuration Model for TinyTrader

This module defines the TradeConfig class which represents configuration
settings for automated trade execution.
"""

from dataclasses import dataclass, field
from decimal import Decimal
from typing import Optional, List, Dict, Any
from datetime import datetime

@dataclass
class TradeConfig:
    """
    Configuration settings for automated trade execution.
    
    Attributes:
        config_id: Unique identifier for this configuration
        name: User-friendly name for this configuration
        min_daily_volume: Minimum daily volume for an item to be considered
        min_profit_percent: Minimum profit percentage for an item to be considered
        max_unit_price: Maximum unit price for an item to be considered
        isk_to_spend: Amount of ISK to spend on buy orders
        max_items_to_scan: Maximum number of items to scan (for performance)
        item_whitelist: Optional list of item IDs to consider (if None, all items are considered)
        created_at: When this configuration was created
        last_used: When this configuration was last used
        notes: Optional notes about this configuration
    """
    config_id: Optional[int] = None
    name: str = "Default Trade Config"
    min_daily_volume: int = 300
    min_profit_percent: Decimal = Decimal('10.0')
    max_unit_price: Decimal = Decimal('15000000')
    isk_to_spend: Decimal = Decimal('500000000')
    max_items_to_scan: int = 200
    item_whitelist: Optional[List[int]] = None
    created_at: datetime = field(default_factory=lambda: datetime.now())
    last_used: Optional[datetime] = None
    notes: Optional[str] = None
    
    def __post_init__(self):
        """Validate and convert types after initialization."""
        # Convert to Decimal if needed
        if not isinstance(self.min_profit_percent, Decimal):
            self.min_profit_percent = Decimal(str(self.min_profit_percent))
        if not isinstance(self.max_unit_price, Decimal):
            self.max_unit_price = Decimal(str(self.max_unit_price))
        if not isinstance(self.isk_to_spend, Decimal):
            self.isk_to_spend = Decimal(str(self.isk_to_spend))
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TradeConfig':
        """Create a TradeConfig from a dictionary."""
        # Handle item_whitelist which might be stored as a string
        if 'item_whitelist' in data and isinstance(data['item_whitelist'], str):
            if data['item_whitelist']:
                data['item_whitelist'] = [int(item_id.strip()) for item_id in data['item_whitelist'].split(',')]
            else:
                data['item_whitelist'] = None
                
        # Handle datetime fields
        for date_field in ['created_at', 'last_used']:
            if date_field in data and isinstance(data[date_field], str):
                data[date_field] = datetime.fromisoformat(data[date_field])
        
        return cls(**data)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the TradeConfig to a dictionary for DB storage."""
        result = self.__dict__.copy()
        
        # Convert Decimal to string
        for decimal_field in ['min_profit_percent', 'max_unit_price', 'isk_to_spend']:
            if decimal_field in result:
                result[decimal_field] = str(result[decimal_field])
        
        # Convert item_whitelist to comma-separated string if it exists
        if result['item_whitelist']:
            result['item_whitelist'] = ','.join(str(item_id) for item_id in result['item_whitelist'])
        
        # Convert datetime objects to ISO format strings
        for date_field in ['created_at', 'last_used']:
            if result[date_field]:
                result[date_field] = result[date_field].isoformat()
        
        return result
