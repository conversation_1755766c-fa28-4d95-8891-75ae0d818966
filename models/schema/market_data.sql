-- Market Data Table Schema
-- Stores market data for items including prices and volume

CREATE TABLE IF NOT EXISTS market_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    region_id INTEGER NOT NULL DEFAULT 10000002, -- Default to The Forge (Jita)
    sell_price REAL,
    buy_price REAL,
    daily_volume INTEGER,
    weekly_volume INTEGER,
    price_volatility REAL,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(item_id, region_id, timestamp)
);

-- Index for faster lookups
CREATE INDEX IF NOT EXISTS idx_market_data_item_id ON market_data(item_id);
CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp);

-- View for latest market data per item
CREATE VIEW IF NOT EXISTS latest_market_data AS
SELECT m.*
FROM market_data m
INNER JOIN (
    SELECT item_id, region_id, MAX(timestamp) as max_timestamp
    FROM market_data
    GROUP BY item_id, region_id
) latest ON m.item_id = latest.item_id 
    AND m.region_id = latest.region_id 
    AND m.timestamp = latest.max_timestamp;
