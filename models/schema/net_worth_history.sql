-- Net Worth History Table Schema
-- Stores historical net worth data for tracking player's liquid assets

CREATE TABLE IF NOT EXISTS net_worth_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    character_id INTEGER NOT NULL,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    wallet_balance DECIMAL(20,2) NOT NULL,
    escrow_total DECIMAL(20,2) NOT NULL,
    sell_orders_total DECIMAL(20,2) NOT NULL,
    net_worth_total DECIMAL(20,2) NOT NULL,
    UNIQUE(character_id, timestamp)
);

-- Index for faster lookups
CREATE INDEX IF NOT EXISTS idx_net_worth_history_character_id ON net_worth_history(character_id);
CREATE INDEX IF NOT EXISTS idx_net_worth_history_timestamp ON net_worth_history(timestamp);

-- View for latest net worth data per character
CREATE VIEW IF NOT EXISTS latest_net_worth AS
SELECT n.*
FROM net_worth_history n
INNER JOIN (
    SELECT character_id, MAX(timestamp) as max_timestamp
    FROM net_worth_history
    GROUP BY character_id
) latest ON n.character_id = latest.character_id 
    AND n.timestamp = latest.max_timestamp;
