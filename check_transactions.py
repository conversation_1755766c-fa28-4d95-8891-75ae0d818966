"""
Check the contents of the transactions table in the database.
"""

import sqlite3
import os
from datetime import datetime

def main():
    """Main function to check the transactions table."""
    db_path = "eve_trader.db"
    
    if not os.path.exists(db_path):
        print(f"Database file not found: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    
    try:
        # Check transactions table
        cursor = conn.cursor()
        
        # Get count of transactions
        cursor.execute("SELECT COUNT(*) as count FROM transactions")
        count = cursor.fetchone()['count']
        print(f"Transactions table contains {count} entries")
        
        # Get transaction types count
        cursor.execute("""
            SELECT transaction_type, COUNT(*) as count 
            FROM transactions 
            GROUP BY transaction_type
        """)
        type_counts = cursor.fetchall()
        print("\nTransaction types:")
        for row in type_counts:
            print(f"  {row['transaction_type']}: {row['count']}")
        
        # Get recent transactions
        cursor.execute("""
            SELECT tx_id, timestamp, item_name, quantity, unit_price, 
                   total_price, transaction_type, net_amount, strategy
            FROM transactions
            ORDER BY timestamp DESC
            LIMIT 10
        """)
        rows = cursor.fetchall()
        
        print("\nMost recent transactions:")
        print("-" * 100)
        print(f"{'ID':<8} {'Timestamp':<20} {'Type':<6} {'Item Name':<30} {'Quantity':<10} {'Unit Price':<15} {'Total':<15} {'Strategy':<15}")
        print("-" * 100)
        
        for row in rows:
            print(f"{row['tx_id']:<8} {row['timestamp']:<20} {row['transaction_type']:<6} {row['item_name']:<30} {row['quantity']:<10} {row['unit_price']:<15} {row['total_price']:<15} {row['strategy'] or 'None':<15}")
        
        # Check for items in transactions that are not in inventory
        cursor.execute("""
            SELECT DISTINCT t.item_id, t.item_name
            FROM transactions t
            LEFT JOIN inventory i ON t.item_id = i.item_id
            WHERE i.item_id IS NULL AND t.transaction_type = 'BUY'
            LIMIT 20
        """)
        missing_items = cursor.fetchall()
        
        if missing_items:
            print(f"\nFound {len(missing_items)} items in BUY transactions that are not in inventory:")
            for row in missing_items:
                print(f"  Item ID: {row['item_id']}, Name: {row['item_name']}")
        
        # Check for items in inventory that are not in transactions
        cursor.execute("""
            SELECT i.item_id, i.item_name
            FROM inventory i
            LEFT JOIN transactions t ON i.item_id = t.item_id
            WHERE t.item_id IS NULL
            LIMIT 20
        """)
        extra_items = cursor.fetchall()
        
        if extra_items:
            print(f"\nFound {len(extra_items)} items in inventory that are not in transactions:")
            for row in extra_items:
                print(f"  Item ID: {row['item_id']}, Name: {row['item_name']}")
        
    except sqlite3.Error as e:
        print(f"Error querying database: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
