#!/usr/bin/env python3
"""
Create the eve_items table in the database.
"""

import sqlite3
import os
import sys
from datetime import datetime

def main():
    """Create the eve_items table in the database."""
    db_path = 'eve_trader.db'
    
    if not os.path.exists(db_path):
        print(f"Error: Database file not found at {db_path}")
        return 1
    
    print(f"Using database at {db_path}")
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if the table already exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='eve_items'")
        if cursor.fetchone():
            print("eve_items table already exists.")
            conn.close()
            return 0
        
        # Create the eve_items table
        print("Creating eve_items table...")
        cursor.execute("""
        CREATE TABLE eve_items (
            item_id INTEGER PRIMARY KEY,
            item_name TEXT NOT NULL,
            group_id INTEGER,
            group_name TEXT,
            category_id INTEGER,
            category_name TEXT,
            published BOOLEAN DEFAULT 1,
            market_group_id INTEGER,
            volume REAL,
            last_updated DATETIME NOT NULL
        )
        """)
        
        # Create index for faster lookups
        cursor.execute("CREATE INDEX idx_eve_items_name ON eve_items(item_name)")
        
        # Add a few basic items for testing
        basic_items = [
            (34, "Tritanium", 18, "Mineral", 4, "Material", 1, 1857, 0.01, datetime.now().isoformat()),
            (35, "Pyerite", 18, "Mineral", 4, "Material", 1, 1857, 0.01, datetime.now().isoformat()),
            (36, "Mexallon", 18, "Mineral", 4, "Material", 1, 1857, 0.01, datetime.now().isoformat()),
            (37, "Isogen", 18, "Mineral", 4, "Material", 1, 1857, 0.01, datetime.now().isoformat()),
            (38, "Nocxium", 18, "Mineral", 4, "Material", 1, 1857, 0.01, datetime.now().isoformat()),
            (39, "Zydrine", 18, "Mineral", 4, "Material", 1, 1857, 0.01, datetime.now().isoformat()),
            (40, "Megacyte", 18, "Mineral", 4, "Material", 1, 1857, 0.01, datetime.now().isoformat()),
            (11399, "Morphite", 18, "Mineral", 4, "Material", 1, 1857, 0.01, datetime.now().isoformat())
        ]
        
        cursor.executemany("""
        INSERT INTO eve_items (
            item_id, item_name, group_id, group_name, category_id, category_name, 
            published, market_group_id, volume, last_updated
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, basic_items)
        
        # Commit the changes
        conn.commit()
        print(f"Added {len(basic_items)} basic items to the eve_items table.")
        
        # Close the connection
        conn.close()
        print("eve_items table created successfully.")
        return 0
        
    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
