"""
Daily Profit Widget

This module provides a widget for displaying daily profit data in a line graph.
"""

import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal

from PyQt5 import QtWidgets, QtCore
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QButtonGroup,
    QSizePolicy, QFrame
)
from PyQt5.QtCore import pyqtSignal, pyqtSlot, Qt, QRectF, QPointF

import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
import matplotlib.pyplot as plt
from matplotlib.widgets import SpanSelector

from controllers.daily_profit_controller import DailyProfitController


class DailyProfitWidget(QWidget):
    """Widget for displaying daily profit data in a line graph."""

    # Signal to request a refresh of the data
    refresh_requested = pyqtSignal()

    # Signal to notify when a date range is selected
    date_range_selected = pyqtSignal(datetime, datetime)

    def __init__(self, parent=None):
        """Initialize the daily profit widget."""
        super().__init__(parent)

        # Initialize controller to None (will be set later)
        self.controller = None

        # Initialize UI
        self._init_ui()

        # Connect signals
        self._connect_signals()

        # Initialize data
        self.profit_data = []
        self.selected_range = "7d"  # Default to 7 days

        # Initialize span selector
        self.span_selector = None

    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Title
        title_label = QLabel("Daily Profit")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Figure for the plot
        self.figure = Figure(figsize=(8, 6), dpi=100)  # Increased height in the figure itself
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.canvas.setMinimumHeight(500)  # Set an even taller minimum height
        main_layout.addWidget(self.canvas)

        # Time range buttons
        time_range_layout = QHBoxLayout()

        # Create button group for time range selection
        self.time_range_group = QButtonGroup(self)

        # Create time range buttons
        time_ranges = [
            ("7d", "7 Days"),
            ("30d", "30 Days"),
            ("90d", "90 Days"),
            ("180d", "180 Days"),
            ("1y", "1 Year"),
            ("all", "All Time")
        ]

        for range_id, range_text in time_ranges:
            button = QPushButton(range_text)
            button.setCheckable(True)
            button.setProperty("range_id", range_id)
            self.time_range_group.addButton(button)
            time_range_layout.addWidget(button)

            # Set 7d as default selected
            if range_id == "7d":
                button.setChecked(True)

        main_layout.addLayout(time_range_layout)

        # Add a horizontal line
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)

    def _connect_signals(self):
        """Connect signals to slots."""
        # Connect time range button group
        self.time_range_group.buttonClicked.connect(self._on_time_range_changed)

    def set_controller(self, controller: DailyProfitController):
        """Set the controller for this widget."""
        self.controller = controller

    def refresh_data(self):
        """Refresh the data and update the plot."""
        if not self.controller:
            return

        # Get data from controller based on selected range
        self.profit_data = self.controller.get_daily_profit_data(self.selected_range)

        # Update the plot
        self._update_plot()

    def _update_plot(self):
        """Update the plot with the current data."""
        # Clear the figure
        self.figure.clear()

        # Create subplot
        ax = self.figure.add_subplot(111)

        # Check if we have data
        if not self.profit_data:
            ax.text(0.5, 0.5, "No profit data available\nTry making some transactions first",
                    horizontalalignment='center', verticalalignment='center',
                    transform=ax.transAxes)
            self.canvas.draw()
            return

        # Extract dates and profits
        dates = [item['date'] for item in self.profit_data]
        profits = [float(item['profit']) for item in self.profit_data]

        # Check if all profits are zero
        if all(profit == 0 for profit in profits):
            ax.text(0.5, 0.5, "No profit data available\nAll transactions have zero profit",
                    horizontalalignment='center', verticalalignment='center',
                    transform=ax.transAxes)
            self.canvas.draw()
            return

        # Plot the data
        line, = ax.plot(dates, profits, '-o', color='#4CAF50', linewidth=2, markersize=4)

        # Format the plot
        ax.set_xlabel('Date')
        ax.set_ylabel('Profit (ISK)')
        ax.grid(True, linestyle='--', alpha=0.7)

        # Format x-axis dates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

        # Format y-axis with ISK values
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f"{x:,.0f}"))

        # Adjust layout
        self.figure.tight_layout()

        # Create span selector for date range selection
        self.span_selector = SpanSelector(
            ax, self._on_span_select, 'horizontal', useblit=True,
            props=dict(alpha=0.3, facecolor='#6495ED')
        )

        # Draw the canvas
        self.canvas.draw()

    def _on_time_range_changed(self, button):
        """Handle time range button clicks."""
        # Get the range ID from the button
        range_id = button.property("range_id")

        # Update selected range
        self.selected_range = range_id

        # Refresh data
        self.refresh_data()

    def _on_span_select(self, xmin, xmax):
        """Handle span selection on the plot."""
        # Convert x values to dates
        ax = self.figure.axes[0]

        # Convert from matplotlib's internal float representation to datetime
        start_date = mdates.num2date(xmin).replace(tzinfo=None)
        end_date = mdates.num2date(xmax).replace(tzinfo=None)

        # Emit signal with selected date range
        self.date_range_selected.emit(start_date, end_date)
