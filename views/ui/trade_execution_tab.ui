<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TradeExecutionTab</class>
 <widget class="QWidget" name="TradeExecutionTab">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Trade Execution</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QGroupBox" name="configGroupBox">
      <property name="title">
       <string>Trade Configuration</string>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>250</height>
       </size>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="configLabel">
         <property name="text">
          <string>Configuration:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="0" column="1" colspan="2">
        <widget class="QComboBox" name="configComboBox"/>
       </item>
       <item row="0" column="3">
        <widget class="QPushButton" name="newConfigButton">
         <property name="text">
          <string>New</string>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="QPushButton" name="saveConfigButton">
         <property name="text">
          <string>Save</string>
         </property>
        </widget>
       </item>
       <item row="0" column="5">
        <widget class="QPushButton" name="deleteConfigButton">
         <property name="text">
          <string>Delete</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="nameLabel">
         <property name="text">
          <string>Name:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="1" colspan="5">
        <widget class="QLineEdit" name="nameLineEdit">
         <property name="placeholderText">
          <string>Configuration Name</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="minVolumeLabel">
         <property name="text">
          <string>Min Daily Volume:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QSpinBox" name="minVolumeSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>10000</number>
         </property>
         <property name="value">
          <number>300</number>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="QLabel" name="minProfitLabel">
         <property name="text">
          <string>Min Profit %:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="3">
        <widget class="QDoubleSpinBox" name="minProfitSpinBox">
         <property name="suffix">
          <string>%</string>
         </property>
         <property name="minimum">
          <double>1.000000000000000</double>
         </property>
         <property name="maximum">
          <double>100.000000000000000</double>
         </property>
         <property name="value">
          <double>10.000000000000000</double>
         </property>
        </widget>
       </item>


       <item row="3" column="3">
        <widget class="QLabel" name="maxItemsLabel">
         <property name="text">
          <string>Max Items to Scan:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="3" column="4" colspan="2">
        <widget class="QSpinBox" name="maxItemsSpinBox">
         <property name="minimum">
          <number>10</number>
         </property>
         <property name="maximum">
          <number>1000</number>
         </property>
         <property name="value">
          <number>200</number>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="itemWhitelistLabel">
         <property name="text">
          <string>Item Whitelist:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item row="4" column="1" colspan="4">
        <widget class="QLineEdit" name="itemWhitelistLineEdit">
         <property name="placeholderText">
          <string>Comma-separated list of item IDs (optional)</string>
         </property>
        </widget>
       </item>
       <item row="4" column="5">
        <widget class="QPushButton" name="itemLookupButton">
         <property name="text">
          <string>Lookup</string>
         </property>
        </widget>
       </item>
       <item row="5" column="0" colspan="6">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QPushButton" name="analyzeButton">
           <property name="text">
            <string>Scan Market</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="scanMoreButton">
           <property name="text">
            <string>Scan More</string>
           </property>
           <property name="enabled">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="analyzeItemButton">
           <property name="text">
            <string>Analyze Item</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QTabWidget" name="resultsTabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>800</height>
       </size>
      </property>
      <widget class="QWidget" name="candidatesTab">
       <attribute name="title">
        <string>Market Analysis</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <widget class="QGroupBox" name="analysisFilterGroupBox">
          <property name="title">
           <string>Hide items tagged with:</string>
          </property>
          <layout class="QVBoxLayout" name="analysisFilterLayout">

           <item>
            <widget class="QScrollArea" name="analysisFilterScrollArea">
             <property name="widgetResizable">
              <bool>true</bool>
             </property>
             <widget class="QWidget" name="analysisFilterScrollContent">
              <layout class="QVBoxLayout" name="analysisFilterCheckboxLayout"/>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QTableView" name="candidatesTableView"/>
        </item>
       </layout>
      </widget>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QProgressBar" name="progressBar">
     <property name="value">
      <number>0</number>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="statusLabel">
     <property name="text">
      <string>Ready</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
