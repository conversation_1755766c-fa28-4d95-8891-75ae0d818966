<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>TinyTrader - EVE Trading Companion</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="marketTab">
       <attribute name="title">
        <string>Market</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_7">
        <item>
         <widget class="QLabel" name="marketPlaceholderLabel">
          <property name="text">
           <string>Market tab will be loaded programmatically</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </widget>

      <widget class="QWidget" name="dataManagementTab">
       <attribute name="title">
        <string>Monitor</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <widget class="QScrollArea" name="monitorScrollArea">
          <property name="widgetResizable">
           <bool>true</bool>
          </property>
          <property name="verticalScrollBarPolicy">
           <enum>Qt::ScrollBarAsNeeded</enum>
          </property>
          <property name="horizontalScrollBarPolicy">
           <enum>Qt::ScrollBarAsNeeded</enum>
          </property>
          <widget class="QWidget" name="monitorScrollAreaContents">
           <layout class="QVBoxLayout" name="monitorScrollAreaLayout">
            <item>
             <widget class="QGroupBox" name="syncGroupBox">
              <property name="title">
               <string>Data Synchronization</string>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_2">
               <item>
                <widget class="QPushButton" name="updateDataButton">
                 <property name="text">
                  <string>Update Data from ESI</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QProgressBar" name="syncProgressBar">
                 <property name="value">
                  <number>0</number>
                 </property>
                 <property name="visible">
                  <bool>false</bool>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="syncStatusLabel">
                 <property name="text">
                  <string></string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_2">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QTabWidget" name="dataViewTabWidget">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>800</height>
               </size>
              </property>
              <widget class="QWidget" name="ordersTab">
               <attribute name="title">
                <string>Active</string>
               </attribute>
               <layout class="QVBoxLayout" name="verticalLayout_4">
                <item>
                 <widget class="QTableView" name="ordersTableView"/>
                </item>
               </layout>
              </widget>
              <widget class="QWidget" name="transactionsTab">
               <attribute name="title">
                <string>History</string>
               </attribute>
               <layout class="QVBoxLayout" name="verticalLayout_3">
                <item>
                 <widget class="QTableView" name="transactionsTableView"/>
                </item>
               </layout>
              </widget>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="settingsTab">
       <attribute name="title">
        <string>Configure</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_6">
        <item>
         <widget class="QGroupBox" name="authGroupBox">
          <property name="title">
           <string>Authentication</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QLabel" name="authStatusLabel">
             <property name="text">
              <string>Status: Not Authenticated</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="authenticateButton">
             <property name="text">
              <string>Authenticate with EVE SSO</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="settingsLabel">
          <property name="text">
           <string>Additional settings will go here (Fee Preferences, etc.)</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
