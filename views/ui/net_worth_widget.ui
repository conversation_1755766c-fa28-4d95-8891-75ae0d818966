<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NetWorthWidget</class>
 <widget class="QWidget" name="NetWorthWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Net Worth Tracking</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="netWorthGroupBox">
     <property name="title">
      <string>Net Worth Tracking</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <layout class="QGridLayout" name="netWorthGridLayout">
        <item row="0" column="0">
         <widget class="QLabel" name="walletBalanceLabel">
          <property name="text">
           <string>Wallet Balance:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="walletBalanceValueLabel">
          <property name="text">
           <string>0.00 ISK</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="escrowTotalLabel">
          <property name="text">
           <string>ISK in Escrow (Buy Orders):</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLabel" name="escrowTotalValueLabel">
          <property name="text">
           <string>0.00 ISK</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="sellOrdersTotalLabel">
          <property name="text">
           <string>Sell Orders Value:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLabel" name="sellOrdersTotalValueLabel">
          <property name="text">
           <string>0.00 ISK</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="netWorthTotalLabel">
          <property name="font">
           <font>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="text">
           <string>Total Net Worth:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLabel" name="netWorthTotalValueLabel">
          <property name="font">
           <font>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="text">
           <string>0.00 ISK</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QGroupBox" name="netWorthHistoryGroupBox">
        <property name="title">
         <string>Net Worth History</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <layout class="QHBoxLayout" name="timeRangeLayout">
           <item>
            <widget class="QLabel" name="timeRangeLabel">
             <property name="text">
              <string>Time Range:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="sevenDaysButton">
             <property name="text">
              <string>7 Days</string>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="oneMonthButton">
             <property name="text">
              <string>1 Month</string>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="oneYearButton">
             <property name="text">
              <string>1 Year</string>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="allTimeButton">
             <property name="text">
              <string>All Time</string>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="refreshNetWorthButton">
             <property name="text">
              <string>Refresh</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QWidget" name="chartWidget" native="true">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>600</height>
            </size>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
