<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MarketTab</class>
 <widget class="QWidget" name="MarketTab">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="syncGroupBox">
     <property name="title">
      <string>Data Synchronization</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QPushButton" name="updateDataButton">
        <property name="text">
         <string>Update Data from ESI</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QProgressBar" name="syncProgressBar">
        <property name="value">
         <number>0</number>
        </property>
        <property name="visible">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="syncStatusLabel">
        <property name="text">
         <string></string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="searchGroupBox">
     <property name="title">
      <string>Item Search</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QLabel" name="searchLabel">
        <property name="text">
         <string>Search:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="itemSearchComboBox">
        <property name="editable">
         <bool>true</bool>
        </property>
        <property name="placeholderText">
         <string>Type to search for items...</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="fetchButton">
        <property name="text">
         <string>Fetch</string>
        </property>
        <property name="toolTip">
         <string>Fetch market data for the selected item</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="addToExecuteButton">
        <property name="text">
         <string>Add to Execute</string>
        </property>
        <property name="toolTip">
         <string>Add this item to the execution tab</string>
        </property>
        <property name="enabled">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QScrollArea" name="mainScrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <property name="verticalScrollBarPolicy">
      <enum>Qt::ScrollBarAsNeeded</enum>
     </property>
     <property name="horizontalScrollBarPolicy">
      <enum>Qt::ScrollBarAsNeeded</enum>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents_main">
      <layout class="QVBoxLayout" name="scrollAreaLayout_main">
       <item>
        <widget class="QGroupBox" name="itemDetailsGroupBox">
         <property name="title">
          <string>Item Details</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QLabel" name="itemNameLabel">
        <property name="font">
         <font>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>Select an item above to view details</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSplitter" name="marketDataSplitter">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <widget class="QWidget" name="marketDataWidget">
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <widget class="QLabel" name="marketDataLabel">
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>Market Data</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="priceInfoGroupBox">
            <property name="title">
             <string>Price Information</string>
            </property>
            <layout class="QGridLayout" name="gridLayout">
             <item row="0" column="0">
              <widget class="QLabel" name="sellPriceLabel">
               <property name="text">
                <string>Best Sell Price:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="sellPriceValueLabel">
               <property name="text">
                <string>-</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="buyPriceLabel">
               <property name="text">
                <string>Best Buy Price:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="buyPriceValueLabel">
               <property name="text">
                <string>-</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="spreadLabel">
               <property name="text">
                <string>Market Spread:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="spreadValueLabel">
               <property name="text">
                <string>-</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="volumeGroupBox">
            <property name="title">
             <string>Volume Information</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_2">
             <item row="0" column="0">
              <widget class="QLabel" name="currentVolumeLabel">
               <property name="text">
                <string>Current Volume:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="dailyVolumeValueLabel">
               <property name="text">
                <string>-</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="historicalVolumeLabel">
               <property name="text">
                <string>Historical Daily Volume:</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLabel" name="historicalVolumeValueLabel">
               <property name="text">
                <string>-</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="avgVolumeLabel">
               <property name="text">
                <string>7-Day Avg Volume:</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QLabel" name="avgVolumeValueLabel">
               <property name="text">
                <string>-</string>
               </property>
              </widget>
             </item>
             <item row="3" column="0">
              <widget class="QLabel" name="fillRateLabel">
               <property name="text">
                <string>Fill Rate:</string>
               </property>
              </widget>
             </item>
             <item row="3" column="1">
              <widget class="QLabel" name="fillRateValueLabel">
               <property name="text">
                <string>-</string>
               </property>
              </widget>
             </item>
             <item row="4" column="0">
              <widget class="QLabel" name="recommendedQtyLabel">
               <property name="text">
                <string>Recommended Qty:</string>
               </property>
              </widget>
             </item>
             <item row="4" column="1">
              <widget class="QLabel" name="recommendedQtyValueLabel">
               <property name="text">
                <string>-</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="profitGroupBox">
            <property name="title">
             <string>Profit Information</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_3">
             <item row="0" column="0">
              <widget class="QLabel" name="totalProfitLabel">
               <property name="text">
                <string>Total Profit:</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QLabel" name="totalProfitValueLabel">
               <property name="text">
                <string>-</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="analyzeMarketButton">
            <property name="text">
             <string>Scan for Issues</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="analysisResultsGroupBox">
            <property name="title">
             <string>Market Pattern Analysis</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_analysis">
             <item>
              <widget class="QTextEdit" name="analysisResultsTextEdit">
               <property name="readOnly">
                <bool>true</bool>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>100</height> <!-- Give it some initial height -->
                </size>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="transactionsWidget">
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <widget class="QLabel" name="transactionsLabel">
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>Your Transactions</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QTableView" name="transactionsTableView"/>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="marketOrdersGroupBox">
     <property name="title">
      <string>Market Orders</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_5">
      <item>
       <widget class="QLabel" name="marketOrdersLabel">
        <property name="font">
         <font>
          <pointsize>10</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>Buy and Sell Orders</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QScrollArea" name="chartScrollArea">
        <property name="widgetResizable">
         <bool>true</bool>
        </property>
        <property name="verticalScrollBarPolicy">
         <enum>Qt::ScrollBarAsNeeded</enum>
        </property>
        <property name="horizontalScrollBarPolicy">
         <enum>Qt::ScrollBarAsNeeded</enum>
        </property>
        <widget class="QWidget" name="scrollAreaWidgetContents">
         <layout class="QVBoxLayout" name="scrollAreaLayout">
          <item>
           <widget class="QWidget" name="chartWidget" native="true">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>800</height>
             </size>
            </property>
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
