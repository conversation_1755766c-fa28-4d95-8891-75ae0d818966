from decimal import Decimal
from typing import Dict, List, Optional
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QPushButton, QButtonGroup,
    QSizePolicy, QGroupBox, QAbstractButton
)
from PyQt5.QtCore import pyqtSignal, pyqtSlot, Qt, QMargins
from PyQt5.QtGui import QColor, QPainter, QFont, QPen
from PyQt5.QtChart import (
    QChart, QChartView, QLineSeries, QValueAxis, QDateTimeAxis,
    QSplineSeries, QAreaSeries
)

from controllers.net_worth_controller import NetWorthController


class NetWorthWidget(QWidget):
    """
    Widget for displaying net worth information and history.
    """

    # Signal to request refresh of net worth data
    refresh_requested = pyqtSignal()

    def __init__(self, parent=None):
        """Initialize the widget."""
        super().__init__(parent)
        self.controller = None
        self._init_ui()
        self._setup_chart()
        self._connect_signals()

    def _init_ui(self):
        """Initialize UI components."""
        # Load UI from file
        from PyQt5 import uic
        uic.loadUi('views/ui/net_worth_widget.ui', self)

        # Get references to UI elements
        self.wallet_balance_value_label = self.findChild(QLabel, 'walletBalanceValueLabel')
        self.escrow_total_value_label = self.findChild(QLabel, 'escrowTotalValueLabel')
        self.sell_orders_total_value_label = self.findChild(QLabel, 'sellOrdersTotalValueLabel')
        self.net_worth_total_value_label = self.findChild(QLabel, 'netWorthTotalValueLabel')

        self.seven_days_button = self.findChild(QPushButton, 'sevenDaysButton')
        self.one_month_button = self.findChild(QPushButton, 'oneMonthButton')
        self.one_year_button = self.findChild(QPushButton, 'oneYearButton')
        self.all_time_button = self.findChild(QPushButton, 'allTimeButton')
        self.refresh_button = self.findChild(QPushButton, 'refreshNetWorthButton')

        self.chart_widget = self.findChild(QWidget, 'chartWidget')

        # Create button group for time range buttons
        self.time_range_button_group = QButtonGroup(self)
        self.time_range_button_group.addButton(self.seven_days_button)
        self.time_range_button_group.addButton(self.one_month_button)
        self.time_range_button_group.addButton(self.one_year_button)
        self.time_range_button_group.addButton(self.all_time_button)

        # Set default time range
        self.current_time_range = 7  # 7 days

    def _connect_signals(self):
        """Connect signals to slots."""
        self.refresh_button.clicked.connect(self._on_refresh_clicked)
        # Use a lambda to convert QAbstractButton to QPushButton
        self.time_range_button_group.buttonClicked.connect(
            lambda button: self._on_time_range_changed(button)
        )

    def _setup_chart(self):
        """Initialize the chart for displaying net worth history."""
        # Create a chart view
        self.chart_view = QChartView()
        self.chart_view.setRenderHint(QPainter.Antialiasing)

        # Create a chart
        self.chart = QChart()
        self.chart.setTitle("Net Worth History")
        self.chart.setAnimationOptions(QChart.SeriesAnimations)
        self.chart.setTheme(QChart.ChartThemeDark)  # Use dark theme

        # Create a small font for all chart elements
        small_font = QFont()
        small_font.setPointSize(8)  # Reduced font size
        self.chart.setTitleFont(small_font)

        # Set the chart on the chart view
        self.chart_view.setChart(self.chart)

        # Add the chart view to the chart widget
        layout = QVBoxLayout(self.chart_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.chart_view)

        # Set the chart widget to expand
        self.chart_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Set the chart to use the full available space
        self.chart.setBackgroundVisible(False)
        self.chart.setMargins(QMargins(10, 10, 10, 10))
        self.chart.layout().setContentsMargins(0, 0, 0, 0)

        # Add legend with small font
        self.chart.legend().setVisible(True)
        self.chart.legend().setAlignment(Qt.AlignBottom)
        self.chart.legend().setFont(small_font)

    def set_controller(self, controller: NetWorthController):
        """Set the controller for this widget."""
        self.controller = controller
        self.refresh_data()

    @pyqtSlot()
    def refresh_data(self):
        """Refresh the net worth data and update the UI."""
        if not self.controller:
            print("Error: Controller not set for NetWorthWidget")
            return

        # Get current net worth
        net_worth = self.controller.get_current_net_worth()

        # Update UI
        self.wallet_balance_value_label.setText(f"{float(net_worth['wallet_balance']):,.2f} ISK")
        self.escrow_total_value_label.setText(f"{float(net_worth['escrow_total']):,.2f} ISK")
        self.sell_orders_total_value_label.setText(f"{float(net_worth['sell_orders_total']):,.2f} ISK")
        self.net_worth_total_value_label.setText(f"{float(net_worth['net_worth_total']):,.2f} ISK")

        # Record snapshot
        self.controller.record_net_worth_snapshot()

        # Update chart
        self._update_chart()

    @pyqtSlot()
    def _on_refresh_clicked(self):
        """Handle refresh button click."""
        self.refresh_data()
        self.refresh_requested.emit()

    @pyqtSlot(QAbstractButton)
    def _on_time_range_changed(self, button):
        """Handle time range button click."""
        if button == self.seven_days_button:
            self.current_time_range = 7
        elif button == self.one_month_button:
            self.current_time_range = 30
        elif button == self.one_year_button:
            self.current_time_range = 365
        elif button == self.all_time_button:
            self.current_time_range = 0  # 0 means all time

        # Update chart with new time range
        self._update_chart()

    def _update_chart(self):
        """Update the chart with net worth history data."""
        if not self.controller:
            print("Error: Controller not set for NetWorthWidget")
            return

        # Get history data
        history = self.controller.get_net_worth_history(days=self.current_time_range)

        # Clear existing series
        self.chart.removeAllSeries()

        # Remove old axes if they exist
        for axis in self.chart.axes():
            self.chart.removeAxis(axis)

        if not history:
            self.chart.setTitle("Net Worth History - No data available")
            return

        # Create series for net worth
        net_worth_series = QSplineSeries()
        net_worth_series.setName("Net Worth")
        net_worth_series.setPen(QPen(QColor(0, 200, 0), 2))  # Green line

        # Create series for wallet balance
        wallet_series = QLineSeries()
        wallet_series.setName("Wallet Balance")
        wallet_series.setPen(QPen(QColor(0, 120, 200), 1))  # Blue line

        # Create series for escrow
        escrow_series = QLineSeries()
        escrow_series.setName("Escrow")
        escrow_series.setPen(QPen(QColor(200, 120, 0), 1))  # Orange line

        # Create series for sell orders
        sell_orders_series = QLineSeries()
        sell_orders_series.setName("Sell Orders")
        sell_orders_series.setPen(QPen(QColor(200, 0, 0), 1))  # Red line

        # Add data points to series
        min_value = float('inf')
        max_value = 0
        min_date = None
        max_date = None

        for entry in history:
            timestamp = datetime.fromisoformat(entry['timestamp'])
            timestamp_ms = int(timestamp.timestamp() * 1000)  # Convert to milliseconds

            wallet_balance = float(entry['wallet_balance'])
            escrow_total = float(entry['escrow_total'])
            sell_orders_total = float(entry['sell_orders_total'])
            net_worth_total = float(entry['net_worth_total'])

            net_worth_series.append(timestamp_ms, net_worth_total)
            wallet_series.append(timestamp_ms, wallet_balance)
            escrow_series.append(timestamp_ms, escrow_total)
            sell_orders_series.append(timestamp_ms, sell_orders_total)

            # Track min/max values for axis scaling
            min_value = min(min_value, wallet_balance, escrow_total, sell_orders_total, net_worth_total)
            max_value = max(max_value, wallet_balance, escrow_total, sell_orders_total, net_worth_total)

            if min_date is None or timestamp < min_date:
                min_date = timestamp
            if max_date is None or timestamp > max_date:
                max_date = timestamp

        # Add series to chart
        self.chart.addSeries(net_worth_series)
        self.chart.addSeries(wallet_series)
        self.chart.addSeries(escrow_series)
        self.chart.addSeries(sell_orders_series)

        # Create axes
        axis_x = QDateTimeAxis()
        axis_x.setFormat("MMM dd")
        axis_x.setTitleText("Date")

        if min_date and max_date:
            min_date_ms = int(min_date.timestamp() * 1000)
            max_date_ms = int(max_date.timestamp() * 1000)
            axis_x.setRange(min_date, max_date)

        axis_y = QValueAxis()
        axis_y.setLabelFormat("%,.0f")
        axis_y.setTitleText("ISK")

        # Add some padding to the y-axis range
        if min_value != float('inf') and max_value > 0:
            padding = (max_value - min_value) * 0.1
            axis_y.setRange(max(0, min_value - padding), max_value + padding)
        else:
            axis_y.setRange(0, 100000)  # Default range if no data

        # Add axes to chart
        self.chart.addAxis(axis_x, Qt.AlignBottom)
        self.chart.addAxis(axis_y, Qt.AlignLeft)

        # Attach series to axes
        net_worth_series.attachAxis(axis_x)
        net_worth_series.attachAxis(axis_y)
        wallet_series.attachAxis(axis_x)
        wallet_series.attachAxis(axis_y)
        escrow_series.attachAxis(axis_x)
        escrow_series.attachAxis(axis_y)
        sell_orders_series.attachAxis(axis_x)
        sell_orders_series.attachAxis(axis_y)

        # Update chart title
        time_range_text = "7 Days"
        if self.current_time_range == 30:
            time_range_text = "1 Month"
        elif self.current_time_range == 365:
            time_range_text = "1 Year"
        elif self.current_time_range == 0:
            time_range_text = "All Time"

        self.chart.setTitle(f"Net Worth History - {time_range_text}")
