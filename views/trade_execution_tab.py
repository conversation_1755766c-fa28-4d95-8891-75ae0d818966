"""
Trade Execution Tab for TinyTrader

This module provides the UI for the trade execution functionality.
"""

import os
import sys
from decimal import Decimal
from typing import List, Dict, Any, Optional, Set
from PyQt5 import uic
from PyQt5.QtWidgets import (
    QWidget, QMessageBox, QProgressBar, QLabel, QComboBox, QLineEdit,
    QSpinBox, QDoubleSpinBox, QTextEdit, QPushButton, QTableView, QApplication,
    QScrollArea, QVBoxLayout, QHBoxLayout, QTabWidget, QCheckBox, QGroupBox, QMenu, QAction
)
from PyQt5.QtCore import pyqtSignal, pyqtSlot, Qt, QItemSelectionModel

from models.trade_config import TradeConfig
from models.trade_execution import ItemCandidate
from controllers.trade_execution_controller import TradeExecutionController
from views.table_models import ItemCandidatesTableModel
from views.market_tab import MarketTab

# Get the directory of this script
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# Import MainWindow for type hinting only to avoid circular import at runtime
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from views.main_window import MainWindow

# Path to the UI file
UI_FILE = os.path.join(SCRIPT_DIR, 'ui', 'trade_execution_tab.ui')

class TradeExecutionTab(QWidget):
    """
    Widget for the Trade Execution tab.

    This tab allows users to:
    - Configure trade parameters
    - Analyze market data
    - Execute trades (now "Scan Market")
    - Analyze a specific item on the Market Tab
    """

    # Signal to update progress
    progress_updated = pyqtSignal(int, str)
    # Signal to request market tab to analyze an item after loading
    request_market_tab_analysis = pyqtSignal(int)


    def __init__(self, controller: TradeExecutionController, main_window: Optional['MainWindow'] = None, parent=None):
        """
        Initialize the Trade Execution tab.

        Args:
            controller: Trade execution controller
            main_window: Reference to the main application window
            parent: Parent widget
        """
        super().__init__(parent)
        self.controller = controller
        self.main_window = main_window

        # Dictionary to store analysis results for each item
        self.item_analysis_results = {}
        # Set of active analysis filters
        self.active_analysis_filters = set()

        # Flag to track if we've scanned at least once
        self.has_scanned = False

        # List to track scanned items in this session
        self.scanned_items = []

        # Create a scroll area to make the tab scrollable
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a widget to hold the content
        self.content_widget = QWidget()

        # Set up the main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.scroll_area)

        # Load the UI into the content widget
        uic.loadUi(UI_FILE, self.content_widget)

        # Set the content widget as the scroll area's widget
        self.scroll_area.setWidget(self.content_widget)

        # Initialize UI elements
        self._init_ui()

        # Connect signals and slots
        self._connect_signals()

        # Load configurations
        self._load_configs()

        # Initialize table models
        self._init_table_models()

        # Initial state for analyzeItemButton
        self.analyzeItemButton.setEnabled(False)

        # Get reference to the Scan More button
        self.scan_more_button = self.content_widget.findChild(QPushButton, 'scanMoreButton')
        if self.scan_more_button:
            self.scan_more_button.clicked.connect(self._on_scan_more)
        else:
            print("Warning: Could not find scanMoreButton in the UI")

        # Add slot to handle item selected for analysis signal
        if self.main_window:
            self.main_window.market_tab = None
            # Try to find MarketTab instance in main window's tabs
            for i in range(self.main_window.tabWidget.count()):
                widget = self.main_window.tabWidget.widget(i)
                if widget.__class__.__name__ == 'MarketTab':
                    self.main_window.market_tab = widget
                    break
            if self.main_window.market_tab:
                self.main_window.market_tab.item_selected_for_analysis.connect(self._on_item_selected_for_analysis)

    def _on_item_selected_for_analysis(self, item_id: int):
        """
        Slot to handle item selected for analysis signal from MarketTab.
        Enables the analyzeItemButton if the item_id matches a candidate.
        """
        # Check if we have a valid candidates model
        if not hasattr(self, 'candidates_model') or not self.candidates_model:
            print("Warning: candidates_model not available in _on_item_selected_for_analysis")
            self.analyzeItemButton.setEnabled(False)
            return

        # Check if the candidates model has the item_id
        for row in range(self.candidates_model.rowCount()):
            candidate = self.candidates_model.get_data_at_row(row)
            if candidate and candidate.get('item_id') == item_id:
                # Select the row in the table view
                self.candidates_table_view.selectRow(row)
                # Enable the analyzeItemButton
                self.analyzeItemButton.setEnabled(True)
                print(f"Item {item_id} found in candidates, enabling Analyze Item button")
                return

        # If item_id not found in candidates, disable the button
        print(f"Item {item_id} not found in candidates, disabling Analyze Item button")
        self.analyzeItemButton.setEnabled(False)


    def _init_ui(self):
        """Initialize UI elements."""
        # Get references to UI elements from the content widget
        self.config_combo_box = self.content_widget.findChild(QComboBox, 'configComboBox')
        self.name_line_edit = self.content_widget.findChild(QLineEdit, 'nameLineEdit')
        self.min_volume_spin_box = self.content_widget.findChild(QSpinBox, 'minVolumeSpinBox')
        self.min_profit_spin_box = self.content_widget.findChild(QDoubleSpinBox, 'minProfitSpinBox')
        self.max_items_spin_box = self.content_widget.findChild(QSpinBox, 'maxItemsSpinBox')
        self.item_whitelist_line_edit = self.content_widget.findChild(QLineEdit, 'itemWhitelistLineEdit')

        self.new_config_button = self.content_widget.findChild(QPushButton, 'newConfigButton')
        self.save_config_button = self.content_widget.findChild(QPushButton, 'saveConfigButton')
        self.delete_config_button = self.content_widget.findChild(QPushButton, 'deleteConfigButton')
        self.analyze_button = self.content_widget.findChild(QPushButton, 'analyzeButton') # Now "Scan Market"
        self.analyzeItemButton = self.content_widget.findChild(QPushButton, 'analyzeItemButton') # New button
        self.item_lookup_button = self.content_widget.findChild(QPushButton, 'itemLookupButton')

        self.candidates_table_view = self.content_widget.findChild(QTableView, 'candidatesTableView')
        # self.executions_table_view removed

        self.progress_bar = self.content_widget.findChild(QProgressBar, 'progressBar')
        self.status_label = self.content_widget.findChild(QLabel, 'statusLabel')

        # Get reference to the results tab widget
        self.resultsTabWidget = self.content_widget.findChild(QTabWidget, 'resultsTabWidget')

        # Analysis filter UI elements
        self.analysis_filter_group_box = self.content_widget.findChild(QGroupBox, 'analysisFilterGroupBox')
        # analyze_all_button removed
        self.analysis_filter_scroll_area = self.content_widget.findChild(QScrollArea, 'analysisFilterScrollArea')
        self.analysis_filter_scroll_content = self.content_widget.findChild(QWidget, 'analysisFilterScrollContent')
        self.analysis_filter_checkbox_layout = self.analysis_filter_scroll_content.layout()

        # Add "Add all to execution" button
        self.add_all_to_execution_button = QPushButton("Add all to execution")
        self.add_all_to_execution_button.clicked.connect(self._on_add_all_to_execution)
        # Add the button to the layout below the filter group box
        self.content_widget.findChild(QVBoxLayout, 'verticalLayout_2').insertWidget(1, self.add_all_to_execution_button)

        # Initialize analysis filter checkboxes
        self.analysis_tag_checkboxes = {}
        self.possible_analysis_tags = [
            "(Alert) Fake margin detected",
            "(Warning) Low turnover trap",  # This tag remains the same, but the criteria has changed
            "(Anomaly) Price anomaly / possible manipulation",
            "(Info) Dead market",
            "(Info) Stale orders",
            "(OK) Liquid and safe",
            "(Warning) Insufficient data",
            "(Warning) Volume collapse",
            "(Info) Sell wall pressure",
            "(Info) Buy wall support",
            "(Info) Already listed"  # New tag for items that already have active orders
        ]

        # Create "Check All" checkbox at the top
        self.check_all_checkbox = QCheckBox("Check All")
        self.check_all_checkbox.setEnabled(False)  # Initially disabled
        self.check_all_checkbox.stateChanged.connect(self._on_check_all_changed)
        self.analysis_filter_checkbox_layout.addWidget(self.check_all_checkbox)

        # Create checkboxes for each analysis tag
        for tag in self.possible_analysis_tags:
            checkbox = QCheckBox(tag)
            checkbox.setEnabled(False)  # Initially disabled
            checkbox.stateChanged.connect(self._on_analysis_filter_changed)
            self.analysis_filter_checkbox_layout.addWidget(checkbox)
            self.analysis_tag_checkboxes[tag] = checkbox

    def _connect_signals(self):
        """Connect signals and slots."""
        # Config combo box
        self.config_combo_box.currentIndexChanged.connect(self._on_config_selected)

        # Buttons
        self.new_config_button.clicked.connect(self._on_new_config)
        self.save_config_button.clicked.connect(self._on_save_config)
        self.delete_config_button.clicked.connect(self._on_delete_config)
        self.analyze_button.clicked.connect(self._on_analyze) # "Scan Market"
        self.analyzeItemButton.clicked.connect(self._on_analyze_item_clicked) # New button
        self.item_lookup_button.clicked.connect(self._on_item_lookup)
        # analyze_all_button connection removed

        # Progress signal
        self.progress_updated.connect(self._on_progress_updated)

        # Note: Selection model connection is now done in _connect_selection_model_signals
        # after the model is set on the table view in _init_table_models

        self.request_market_tab_analysis.connect(self._handle_market_tab_analysis_request)


    def _init_table_models(self):
        """Initialize table models."""
        # Candidates table
        self.candidates_model = ItemCandidatesTableModel([])
        if self.candidates_table_view: # Check if the view object exists
            self.candidates_table_view.setModel(self.candidates_model)
            self.candidates_table_view.setSelectionBehavior(QTableView.SelectRows)
            self.candidates_table_view.setSelectionMode(QTableView.SingleSelection)

            # Enable context menu
            self.candidates_table_view.setContextMenuPolicy(Qt.CustomContextMenu)
            self.candidates_table_view.customContextMenuRequested.connect(self._show_candidates_context_menu)

            # Set column widths for candidates_table_view
            header = self.candidates_table_view.horizontalHeader()
            if header: # Check if header exists
                # Ensure model has columns before trying to access columnCount or resize
                if self.candidates_model and self.candidates_model.columnCount() > 0:
                    # Check if column 1 exists before setting stretch
                    if self.candidates_model.columnCount() > 1:
                        header.setSectionResizeMode(1, header.Stretch)  # Item name column

                    for i in range(self.candidates_model.columnCount()):
                        if i != 1:  # Skip item name column
                            self.candidates_table_view.setColumnWidth(i, 100)
                else:
                    print(f"Warning: {self.objectName()} - candidates_model is None or has no columns in _init_table_models.")

            # Now that the model is set, connect the selection model signals
            self._connect_selection_model_signals()
        else:
            print(f"Warning: {self.objectName()} - candidates_table_view is None in _init_table_models.")
        # Executions table model removed

    def _show_candidates_context_menu(self, position):
        """
        Show context menu for the candidates table.

        Args:
            position: Position where the context menu was requested
        """
        # Get the selected row
        selected_rows = self.candidates_table_view.selectionModel().selectedRows()
        if not selected_rows:
            return

        # Create context menu
        context_menu = QMenu(self)

        # Add actions
        add_to_execute_action = QAction("Add to Execute List", self)
        add_to_execute_action.triggered.connect(self._on_add_to_execute_list)
        context_menu.addAction(add_to_execute_action)

        analyze_action = QAction("Analyze Item", self)
        analyze_action.triggered.connect(self._on_analyze_item_clicked)
        context_menu.addAction(analyze_action)

        # Show the menu
        context_menu.exec_(self.candidates_table_view.mapToGlobal(position))

    def _on_add_to_execute_list(self):
        """Handle 'Add to Execute List' action from context menu."""
        # Get the selected row
        selected_rows = self.candidates_table_view.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Selection Error", "Please select an item to add to the execute list.")
            return

        # Get the item data
        selected_row_index = selected_rows[0].row()
        item_data = self.candidates_model.get_data_at_row(selected_row_index)

        if not item_data:
            QMessageBox.warning(self, "Data Error", "Could not retrieve item data.")
            return

        # Get the main window instance
        main_win = self._get_main_window()
        if not main_win:
            QMessageBox.critical(self, "Error", "Could not access the main application window.")
            return

        # Check if execute tab is available
        if not hasattr(main_win, 'execute_tab') or not main_win.execute_tab:
            QMessageBox.critical(self, "Error", "Execute tab not found.")
            return

        # Add the item to the execute list
        main_win.execute_tab.add_item(item_data)


    def _load_configs(self):
        """Load configurations from the database."""
        configs = self.controller.get_all_configs()

        # Clear combo box
        self.config_combo_box.clear()

        # Add configs to combo box
        for config in configs:
            self.config_combo_box.addItem(config.name, config.config_id)

        # Add a default config if none exist
        if not configs:
            self._add_default_config()

    def _add_default_config(self):
        """Add a default configuration."""
        default_config = TradeConfig()
        config_id = self.controller.save_config(default_config)
        default_config.config_id = config_id

        # Add to combo box
        self.config_combo_box.addItem(default_config.name, default_config.config_id)

        # Select it
        self.config_combo_box.setCurrentIndex(0)

        # Update UI
        self._update_config_ui(default_config)

    # _load_recent_executions removed

    def _on_config_selected(self, index):
        """Handle config selection."""
        if index < 0:
            return

        config_id = self.config_combo_box.itemData(index)
        config = self.controller.get_config(config_id)

        if config:
            self._update_config_ui(config)

    def _update_config_ui(self, config: TradeConfig):
        """Update UI with config values."""
        self.name_line_edit.setText(config.name)
        self.min_volume_spin_box.setValue(config.min_daily_volume)
        self.min_profit_spin_box.setValue(float(config.min_profit_percent))
        self.max_items_spin_box.setValue(config.max_items_to_scan)

        # Item whitelist
        if config.item_whitelist:
            self.item_whitelist_line_edit.setText(','.join(str(item_id) for item_id in config.item_whitelist))
        else:
            self.item_whitelist_line_edit.clear()

        # Notes field removed

    def _get_config_from_ui(self) -> TradeConfig:
        """Get config from UI values."""
        # Get current config ID if any
        config_id = None
        if self.config_combo_box.currentIndex() >= 0:
            config_id = self.config_combo_box.currentData()

        # Parse item whitelist
        item_whitelist_text = self.item_whitelist_line_edit.text().strip()
        item_whitelist = None
        if item_whitelist_text:
            try:
                item_whitelist = [int(item_id.strip()) for item_id in item_whitelist_text.split(',')]
            except ValueError:
                QMessageBox.warning(self, "Invalid Input", "Item whitelist must be comma-separated integers.")
                return None

        # Create config
        config = TradeConfig(
            config_id=config_id,
            name=self.name_line_edit.text(),
            min_daily_volume=self.min_volume_spin_box.value(),
            min_profit_percent=Decimal(str(self.min_profit_spin_box.value())),
            max_unit_price=Decimal('1000000000'),  # Set to a very high value to effectively remove the limit
            isk_to_spend=Decimal('10000000000'),   # Set to a very high value to effectively remove the limit
            max_items_to_scan=self.max_items_spin_box.value(),
            item_whitelist=item_whitelist,
            notes=None  # Notes field removed from UI
        )

        return config

    def _on_new_config(self):
        """Handle new config button click."""
        # Create a new default config
        default_config = TradeConfig(
            name=f"New Config {self.config_combo_box.count() + 1}"
        )

        # Save it
        config_id = self.controller.save_config(default_config)
        default_config.config_id = config_id

        # Add to combo box
        self.config_combo_box.addItem(default_config.name, default_config.config_id)

        # Select it
        self.config_combo_box.setCurrentIndex(self.config_combo_box.count() - 1)

        # Update UI
        self._update_config_ui(default_config)

        # Set focus to name field
        self.name_line_edit.setFocus()
        self.name_line_edit.selectAll()

    def _on_save_config(self):
        """Handle save config button click."""
        config = self._get_config_from_ui()
        if not config:
            return

        # Save config
        config_id = self.controller.save_config(config)

        # Update combo box if name changed
        current_index = self.config_combo_box.currentIndex()
        if current_index >= 0 and self.config_combo_box.itemText(current_index) != config.name:
            self.config_combo_box.setItemText(current_index, config.name)

        # Show success message
        self.status_label.setText(f"Configuration '{config.name}' saved.")

    def _on_delete_config(self):
        """Handle delete config button click."""
        current_index = self.config_combo_box.currentIndex()
        if current_index < 0:
            return

        config_id = self.config_combo_box.itemData(current_index)
        config_name = self.config_combo_box.itemText(current_index)

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete the configuration '{config_name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # Delete config
        success = self.controller.delete_config(config_id)

        if success:
            # Remove from combo box
            self.config_combo_box.removeItem(current_index)

            # Show success message
            self.status_label.setText(f"Configuration '{config_name}' deleted.")

            # Add a default config if none left
            if self.config_combo_box.count() == 0:
                self._add_default_config()
        else:
            # Show error message
            QMessageBox.warning(
                self,
                "Error",
                f"Failed to delete configuration '{config_name}'."
            )

    def _on_analyze(self):
        """Handle analyze button click."""
        config = self._get_config_from_ui()
        if not config:
            return

        # Disable buttons during analysis
        self._set_buttons_enabled(False)

        # Reset progress
        self.progress_bar.setValue(0)
        self.status_label.setText("Analyzing market data...")

        # Start analysis in a separate thread
        from PyQt5.QtCore import QThread

        class AnalysisThread(QThread):
            def __init__(self, controller, config, progress_callback):
                super().__init__()
                self.controller = controller
                self.config = config
                self.progress_callback = progress_callback
                self.result = None

            def run(self):
                self.result = self.controller.analyze_market_data(
                    self.config,
                    self.progress_callback
                )

        self.analysis_thread = AnalysisThread(
            self.controller,
            config,
            self.progress_updated.emit
        )

        # Connect signals
        self.analysis_thread.finished.connect(self._on_analysis_complete)

        # Start thread
        self.analysis_thread.start()

    def _on_scan_more(self):
        """Handle scan more button click."""
        config = self._get_config_from_ui()
        if not config:
            return

        # Disable buttons during analysis
        self._set_buttons_enabled(False)

        # Reset progress
        self.progress_bar.setValue(0)
        self.status_label.setText("Scanning for more items...")

        # Start analysis in a separate thread
        from PyQt5.QtCore import QThread

        class RandomAnalysisThread(QThread):
            def __init__(self, controller, config, progress_callback, scanned_items):
                super().__init__()
                self.controller = controller
                self.config = config
                self.progress_callback = progress_callback
                self.scanned_items = scanned_items
                self.result = None

            def run(self):
                self.result = self.controller.analyze_random_market_data(
                    self.config,
                    self.progress_callback,
                    self.scanned_items
                )

        self.analysis_thread = RandomAnalysisThread(
            self.controller,
            config,
            self.progress_updated.emit,
            self.scanned_items
        )

        # Connect signals
        self.analysis_thread.finished.connect(self._on_analysis_complete)

        # Start thread
        self.analysis_thread.start()

    def _on_analysis_complete(self):
        """Handle analysis completion."""
        # Get results
        candidates = self.analysis_thread.result

        # Update table
        self.candidates_model.update_data([c.__dict__ for c in candidates])

        # Reconnect selection model signals after model update
        self._connect_selection_model_signals()

        # Show intermediate message
        self.status_label.setText(f"Market scan complete. Found {len(candidates)} profitable items. Starting analysis...")

        # Switch to candidates tab (which is the only tab now in resultsTabWidget)
        self.resultsTabWidget.setCurrentIndex(0)

        # Reset analysis filters
        self.item_analysis_results = {}
        self.active_analysis_filters.clear()

        # Reset and disable checkboxes
        self.check_all_checkbox.setChecked(False)
        self.check_all_checkbox.setEnabled(False)
        for checkbox in self.analysis_tag_checkboxes.values():
            checkbox.setChecked(False)
            checkbox.setEnabled(False)

        # Track that we've scanned at least once and enable the "Scan More" button
        self.has_scanned = True
        self.scan_more_button.setEnabled(True)

        # Track scanned items
        for candidate in candidates:
            if hasattr(candidate, 'item_id') and candidate.item_id not in self.scanned_items:
                self.scanned_items.append(candidate.item_id)

        # Automatically run the analyze all logic
        if candidates and len(candidates) > 0:
            # We don't need to check if candidates exist since we just got them
            # Call the analyze all logic directly
            self._run_analyze_all()


    def _connect_selection_model_signals(self):
        """Connect signals from the selection model after the model has been set."""
        if self.candidates_table_view:
            selection_model = self.candidates_table_view.selectionModel()
            if selection_model:
                # Disconnect first to avoid duplicate connections
                try:
                    selection_model.selectionChanged.disconnect(self._on_candidate_selection_changed)
                except:
                    pass  # It's okay if it wasn't connected

                # Connect the signal
                selection_model.selectionChanged.connect(self._on_candidate_selection_changed)
                print("Connected selection model signals successfully")
            else:
                print(f"Warning: {self.objectName()} - candidates_table_view has no selectionModel() in _connect_selection_model_signals.")
        else:
            print(f"Warning: {self.objectName()} - candidates_table_view is None in _connect_selection_model_signals.")

    def _on_candidate_selection_changed(self, _=None, __=None):
        """
        Handle selection changes in the candidates table view.

        Args:
            _: First argument from the selectionChanged signal (selected)
            __: Second argument from the selectionChanged signal (deselected)
                We don't use these directly, just check the current selection.
        """
        selected_rows = self.candidates_table_view.selectionModel().selectedRows()
        self.analyzeItemButton.setEnabled(len(selected_rows) == 1)

    def _get_main_window(self) -> Optional['MainWindow']:
        """Helper to get the main window instance."""
        return self.main_window

    def _on_analyze_item_clicked(self):
        """Handle Analyze Item button click."""
        selected_rows = self.candidates_table_view.selectionModel().selectedRows()
        if len(selected_rows) != 1:
            QMessageBox.warning(self, "Selection Error", "Please select exactly one item to analyze.")
            return

        selected_row_index = selected_rows[0].row()
        item_candidate_dict = self.candidates_model.get_data_at_row(selected_row_index)

        if not item_candidate_dict:
            QMessageBox.warning(self, "Data Error", "Could not retrieve item data.")
            return

        item_name = item_candidate_dict.get('item_name')
        item_id = item_candidate_dict.get('item_id')

        if not item_name or not item_id:
            QMessageBox.warning(self, "Data Error", "Item name or ID is missing.")
            return

        main_win = self._get_main_window()
        if not main_win:
            QMessageBox.critical(self, "Error", "Could not access the main application window.")
            return

        # Find the MarketTab instance
        market_tab_instance = None
        for i in range(main_win.tabWidget.count()):
            widget = main_win.tabWidget.widget(i)
            if isinstance(widget, MarketTab):
                market_tab_instance = widget
                break

        if not market_tab_instance:
            QMessageBox.critical(self, "Error", "Market tab not found.")
            return

        # Switch to Market Tab (assuming it's at index 0, as per current main_window.py)
        main_win.tabWidget.setCurrentIndex(0) # TODO: Make this more robust if tab order changes

        # Set item name in search combo and trigger load/fetch
        market_tab_instance.item_search_combo.setCurrentText(item_name)

        # Connect to the market tab's data loaded signal or use a direct call chain
        # For simplicity, we'll assume _load_item_details is synchronous enough for now,
        # or that _on_analyze_market_clicked can handle being called when data might still be loading.
        # A more robust solution would use signals/slots for async operations.

        # Ensure the market tab is ready to receive the analysis request
        # We use a signal to decouple and ensure MarketTab processes it in its own event loop turn
        market_tab_instance.analysis_requested_for_item_id = item_id # Store it for market tab to pick up
        self.request_market_tab_analysis.emit(item_id)


    @pyqtSlot(int)
    def _handle_market_tab_analysis_request(self, item_id: int):
        """
        This slot is triggered after setting the item in MarketTab.
        It ensures that MarketTab's _on_analyze_market_clicked (now Scan for Issues)
        is called after its UI has potentially updated from _load_item_details.
        """
        main_win = self._get_main_window()
        if not main_win: return

        market_tab_instance = None
        for i in range(main_win.tabWidget.count()):
            widget = main_win.tabWidget.widget(i)
            if isinstance(widget, MarketTab):
                market_tab_instance = widget
                break

        if market_tab_instance and hasattr(market_tab_instance, '_on_analyze_market_clicked'):
            # First, ensure data for the item_id is loaded/refreshed
            # _load_item_details should handle fetching and then making analyze button visible
            market_tab_instance._load_item_details(item_id)

            # Now, if the analyze button on market tab is visible (meaning data is ready), click it.
            # This relies on _load_item_details making the button visible.
            if market_tab_instance.analyze_market_button.isVisible():
                 market_tab_instance._on_analyze_market_clicked()
            else:
                # If not visible, it might be still loading. This part might need a signal from MarketTab
                # indicating data is ready for analysis. For now, we'll show a message.
                print(f"MarketTab: Analyze button for item {item_id} not yet visible. Analysis might be deferred or need manual click.")
                QMessageBox.information(self, "Info", f"Market data for {market_tab_instance.current_item_name} is loading. "
                                                     "Once loaded, 'Scan for Issues' will be available on the Market tab.")


    # _on_execute_trade and _on_execute_analysis_complete removed
    # _on_execution_complete removed

    def _on_item_lookup(self):
        """Handle item lookup button click."""
        # TODO: Implement item lookup dialog
        QMessageBox.information(
            self,
            "Item Lookup",
            "Item lookup functionality not yet implemented."
        )

    def _on_progress_updated(self, progress: int, message: str):
        """Handle progress updates."""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def _set_buttons_enabled(self, enabled: bool):
        """Enable or disable buttons."""
        self.new_config_button.setEnabled(enabled)
        self.save_config_button.setEnabled(enabled)
        self.delete_config_button.setEnabled(enabled)
        self.analyze_button.setEnabled(enabled) # "Scan Market"
        # Only enable the scan more button if we've scanned at least once
        self.scan_more_button.setEnabled(enabled and self.has_scanned)
        # self.execute_trade_button removed
        self.item_lookup_button.setEnabled(enabled)
        # analyze_all_button removed

        # Enable/disable the add all to execution button
        self.add_all_to_execution_button.setEnabled(enabled)

        # analyzeItemButton's state is handled by _on_candidate_selection_changed
        # but we can include it here if a global enable/disable is needed during operations.
        if enabled:
            # Re-evaluate based on selection if enabling globally
            self._on_candidate_selection_changed(None, None)
        else:
            self.analyzeItemButton.setEnabled(False)

    def _run_analyze_all(self):
        """Run analysis on all items in the candidates model."""
        # Disable buttons during analysis
        self._set_buttons_enabled(False)

        # Reset progress
        self.progress_bar.setValue(0)
        self.status_label.setText("Analyzing all items...")

        # Clear previous analysis results
        self.item_analysis_results = {}

        # Get the main window instance
        main_win = self._get_main_window()
        if not main_win:
            QMessageBox.critical(self, "Error", "Could not access the main application window.")
            self._set_buttons_enabled(True)
            return

        # Find the MarketTab instance
        market_tab_instance = None
        for i in range(main_win.tabWidget.count()):
            widget = main_win.tabWidget.widget(i)
            if isinstance(widget, MarketTab):
                market_tab_instance = widget
                break

        if not market_tab_instance:
            QMessageBox.critical(self, "Error", "Market tab not found.")
            self._set_buttons_enabled(True)
            return

        # Start analysis in a separate thread
        from PyQt5.QtCore import QThread, pyqtSignal
        from api.esi_client import ESIClient, JITA_REGION_ID, JITA_STATION_ID

        class AnalyzeAllThread(QThread):
            # Define a signal to request data for an item
            fetch_data_for_item = pyqtSignal(int, str)

            def __init__(self, parent, candidates_model, progress_callback):
                super().__init__()
                self.parent = parent
                self.candidates_model = candidates_model
                self.progress_callback = progress_callback
                self.results = {}
                self.esi_client = ESIClient()  # Create a new ESI client for the thread
                self.db = parent.controller.db  # Get database connection from parent

            def check_if_already_listed(self, item_id):
                """
                Check if the item already has active buy or sell orders.

                Args:
                    item_id: The item ID to check

                Returns:
                    True if the item has active orders, False otherwise
                """
                # Query the database for active orders for this item
                query = """
                SELECT COUNT(*) as count
                FROM orders
                WHERE item_id = ?
                AND quantity_remaining > 0
                AND NOT expired
                AND NOT canceled
                """
                result = self.db.execute_query(query, (item_id,), fetch_one=True)

                # If there are any active orders, return True
                return result and result['count'] > 0

            def run(self):
                total_items = self.candidates_model.rowCount()
                for row in range(total_items):
                    # Update progress
                    progress = int((row / total_items) * 100)
                    self.progress_callback(progress, f"Analyzing item {row+1} of {total_items}...")

                    # Get item data
                    item_data = self.candidates_model.get_data_at_row(row)
                    if not item_data:
                        continue

                    item_id = item_data.get('item_id')
                    item_name = item_data.get('item_name')

                    if not item_id or not item_name:
                        continue

                    # Initialize analysis tags list for this item
                    analysis_tags = []

                    # Check if the item is already listed
                    if self.check_if_already_listed(item_id):
                        # Add the "Already listed" tag
                        analysis_tags.append({
                            "tag": "(Info) Already listed",
                            "description": f"You already have active buy or sell orders for {item_name}."
                        })

                    # Fetch market data directly using ESI client instead of accessing UI
                    try:
                        # Fetch buy orders
                        buy_orders = self.esi_client.get_market_orders(
                            region_id=JITA_REGION_ID,
                            item_id=item_id,
                            order_type='buy',
                            force_refresh=True
                        )

                        # Fetch sell orders
                        sell_orders = self.esi_client.get_market_orders(
                            region_id=JITA_REGION_ID,
                            item_id=item_id,
                            order_type='sell',
                            force_refresh=True
                        )

                        # Filter orders to only include those from JITA station
                        jita_buy_orders = [order for order in buy_orders if order.get('location_id') == JITA_STATION_ID]
                        jita_sell_orders = [order for order in sell_orders if order.get('location_id') == JITA_STATION_ID]

                        # Fetch historical data
                        historical_data = self.esi_client.get_market_history(
                            region_id=JITA_REGION_ID,
                            item_id=item_id,
                            force_refresh=True
                        )

                        # If data loaded, analyze the item
                        if jita_buy_orders and jita_sell_orders and historical_data:
                            # Create the market orders structure expected by analyze_item_market
                            current_orders = {
                                'buy': jita_buy_orders,
                                'sell': jita_sell_orders
                            }

                            # Call the analyze_item_market function directly
                            from controllers.market_analyzer import analyze_item_market
                            market_analysis_tags = analyze_item_market(
                                current_orders=current_orders,
                                historical_data=historical_data,
                                item_id=item_id,
                                item_name=item_name
                            )

                            # Add market analysis tags to our analysis tags
                            analysis_tags.extend(market_analysis_tags)

                        # Store the results (even if only the "Already listed" tag)
                        if analysis_tags:
                            self.results[item_id] = analysis_tags
                    except Exception as e:
                        print(f"Error analyzing item {item_id} ({item_name}): {e}")
                        import traceback
                        print(traceback.format_exc())

                # Final progress update
                self.progress_callback(100, "Analysis complete!")

        # Create and start the thread
        self.analyze_all_thread = AnalyzeAllThread(
            self,
            self.candidates_model,
            self.progress_updated.emit
        )

        # Connect signals
        self.analyze_all_thread.finished.connect(self._on_analyze_all_complete)

        # Start thread
        self.analyze_all_thread.start()

    def _on_analyze_all_clicked(self):
        """Handle Analyze All button click."""
        # This method is kept for backward compatibility but is no longer connected to any UI element
        # Check if we have items to analyze
        if not hasattr(self, 'candidates_model') or not self.candidates_model or self.candidates_model.rowCount() == 0:
            QMessageBox.warning(self, "No Items", "There are no items to analyze. Run 'Scan Market' first.")
            return

        # Run the analysis
        self._run_analyze_all()

    def _on_analyze_all_complete(self):
        """Handle completion of analyze all thread."""
        # Store the results
        self.item_analysis_results = self.analyze_all_thread.results

        # Enable the checkboxes
        self.check_all_checkbox.setEnabled(True)
        for checkbox in self.analysis_tag_checkboxes.values():
            checkbox.setEnabled(True)

        # Enable buttons
        self._set_buttons_enabled(True)

        # Show success message
        total_analyzed = len(self.item_analysis_results)
        self.status_label.setText(f"Analysis complete. Analyzed {total_analyzed} items.")

    def refresh_analysis_results(self):
        """
        Refresh the analysis results for all items currently in the candidates table.
        This is called when orders are synced or new orders are placed to ensure
        the "Already Listed" status is up-to-date.
        """
        # Check if we have items to analyze
        if not hasattr(self, 'candidates_model') or not self.candidates_model or self.candidates_model.rowCount() == 0:
            print("No items to refresh analysis for")
            return

        # Only refresh if we have existing analysis results
        if not hasattr(self, 'item_analysis_results') or not self.item_analysis_results:
            print("No existing analysis results to refresh")
            return

        print("Refreshing analysis results for items in the candidates table...")

        # Create a database connection
        if not hasattr(self, 'controller') or not self.controller or not hasattr(self.controller, 'db'):
            print("No database connection available for refreshing analysis")
            return

        db = self.controller.db

        # Check each item in the candidates table
        updated_count = 0
        for row in range(self.candidates_model.rowCount()):
            item_data = self.candidates_model.get_data_at_row(row)
            if not item_data:
                continue

            item_id = item_data.get('item_id')
            item_name = item_data.get('item_name')

            if not item_id or not item_name:
                continue

            # Check if this item is in our analysis results
            if item_id not in self.item_analysis_results:
                continue

            # Query the database to check if the item is already listed
            query = """
            SELECT COUNT(*) as count
            FROM orders
            WHERE item_id = ?
            AND quantity_remaining > 0
            AND NOT expired
            AND NOT canceled
            """
            result = db.execute_query(query, (item_id,), fetch_one=True)
            is_already_listed = result and result['count'] > 0

            # Check if we need to update the analysis results
            already_listed_tag = {"tag": "(Info) Already listed", "description": f"You already have active buy or sell orders for {item_name}."}

            # Find if the item already has the "Already listed" tag
            has_already_listed_tag = False
            for tag_info in self.item_analysis_results[item_id]:
                if isinstance(tag_info, dict) and tag_info.get("tag") == "(Info) Already listed":
                    has_already_listed_tag = True
                    break

            # Update the analysis results if needed
            if is_already_listed and not has_already_listed_tag:
                # Add the tag
                self.item_analysis_results[item_id].append(already_listed_tag)
                updated_count += 1
                print(f"Added 'Already listed' tag to {item_name}")
            elif not is_already_listed and has_already_listed_tag:
                # Remove the tag
                self.item_analysis_results[item_id] = [
                    tag for tag in self.item_analysis_results[item_id]
                    if not (isinstance(tag, dict) and tag.get("tag") == "(Info) Already listed")
                ]
                updated_count += 1
                print(f"Removed 'Already listed' tag from {item_name}")

        # If we updated any items, reapply the filters
        if updated_count > 0:
            print(f"Updated 'Already listed' status for {updated_count} items")
            self._apply_analysis_filters()
        else:
            print("No items needed 'Already listed' status update")

    def _on_add_all_to_execution(self):
        """Handle 'Add all to execution' button click."""
        # Check if we have a valid candidates model
        if not hasattr(self, 'candidates_model') or not self.candidates_model:
            QMessageBox.warning(self, "No Items", "There are no items to add to the execution list.")
            return

        # Check if there are any items in the candidates model
        if self.candidates_model.rowCount() == 0:
            QMessageBox.warning(self, "No Items", "There are no items to add to the execution list.")
            return

        # Get the main window instance
        main_win = self._get_main_window()
        if not main_win:
            QMessageBox.critical(self, "Error", "Could not access the main application window.")
            return

        # Check if execute tab is available
        if not hasattr(main_win, 'execute_tab') or not main_win.execute_tab:
            QMessageBox.critical(self, "Error", "Execute tab not found.")
            return

        # Count of items added
        items_added = 0

        # Iterate through all rows in the candidates table
        for row in range(self.candidates_model.rowCount()):
            # Check if the row is hidden (filtered out)
            if self.candidates_table_view.isRowHidden(row):
                continue

            # Get the item data
            item_data = self.candidates_model.get_data_at_row(row)
            if not item_data:
                continue

            # Add the item to the execute list
            main_win.execute_tab.add_item(item_data)
            items_added += 1

        # Show success message
        if items_added > 0:
            self.status_label.setText(f"Added {items_added} items to the execution list.")
        else:
            QMessageBox.information(self, "No Items Added", "No items were added to the execution list. All items may be filtered out or already in the list.")

    def _on_check_all_changed(self, state):
        """
        Handle "Check All" checkbox state change.

        Args:
            state: State from the stateChanged signal
        """
        # Block signals to prevent recursive calls
        for checkbox in self.analysis_tag_checkboxes.values():
            checkbox.blockSignals(True)
            checkbox.setChecked(state == Qt.Checked)
            checkbox.blockSignals(False)

        # Update active filters
        if state == Qt.Checked:
            # Add all tags to active filters
            self.active_analysis_filters = set(self.possible_analysis_tags)
        else:
            # Clear all filters
            self.active_analysis_filters.clear()

        # Apply filters
        self._apply_analysis_filters()

    def _on_analysis_filter_changed(self, _):
        """
        Handle analysis filter checkbox state change.

        Args:
            _: State from the stateChanged signal (not used directly)
        """
        # Get the checkbox that triggered the signal
        checkbox = self.sender()
        if not checkbox:
            return

        # Skip if it's the "Check All" checkbox
        if checkbox == self.check_all_checkbox:
            return

        # Get the tag for this checkbox
        tag = checkbox.text()

        # Update active filters
        if checkbox.isChecked():
            self.active_analysis_filters.add(tag)
        else:
            self.active_analysis_filters.discard(tag)

        # Update "Check All" checkbox state without triggering its signal
        self.check_all_checkbox.blockSignals(True)
        all_checked = all(cb.isChecked() for cb in self.analysis_tag_checkboxes.values())
        self.check_all_checkbox.setChecked(all_checked)
        self.check_all_checkbox.blockSignals(False)

        # Apply filters
        self._apply_analysis_filters()

    def _apply_analysis_filters(self):
        """Apply analysis filters to the candidates table."""
        # If no filters active, show all items
        if not self.active_analysis_filters:
            self.candidates_table_view.clearSelection()
            self.candidates_table_view.setUpdatesEnabled(False)
            for row in range(self.candidates_model.rowCount()):
                self.candidates_table_view.setRowHidden(row, False)
            self.candidates_table_view.setUpdatesEnabled(True)
            return

        # Check if "Already listed" filter is active - we'll handle it specially
        already_listed_filter_active = "(Info) Already listed" in self.active_analysis_filters

        # Hide rows that match any active filter
        self.candidates_table_view.clearSelection()
        self.candidates_table_view.setUpdatesEnabled(False)

        # Get database connection if we need to check "Already listed" status directly
        db = None
        if already_listed_filter_active and hasattr(self, 'controller') and self.controller and hasattr(self.controller, 'db'):
            db = self.controller.db

        for row in range(self.candidates_model.rowCount()):
            item_data = self.candidates_model.get_data_at_row(row)
            if not item_data:
                # If no data, show the row (don't hide it)
                self.candidates_table_view.setRowHidden(row, False)
                continue

            item_id = item_data.get('item_id')
            if not item_id:
                # If no item_id, show the row (don't hide it)
                self.candidates_table_view.setRowHidden(row, False)
                continue

            # Initialize should_hide flag
            should_hide = False

            # Special handling for "Already listed" filter if active
            if already_listed_filter_active and db:
                # Check directly in the database if this item is already listed
                query = """
                SELECT COUNT(*) as count
                FROM orders
                WHERE item_id = ?
                AND quantity_remaining > 0
                AND NOT expired
                AND NOT canceled
                """
                result = db.execute_query(query, (item_id,), fetch_one=True)
                is_already_listed = result and result['count'] > 0

                if is_already_listed:
                    # If the item is already listed and the filter is active, hide it
                    should_hide = True

            # If not already hidden by the "Already listed" filter, check other filters
            if not should_hide and item_id in self.item_analysis_results:
                # Get analysis tags for this item
                analysis_tags = self.item_analysis_results[item_id]

                # Check if any of the active filters (except "Already listed" which we already checked)
                # match this item's tags
                for analysis_result in analysis_tags:
                    if isinstance(analysis_result, dict) and "tag" in analysis_result:
                        tag = analysis_result["tag"]
                        # Skip "Already listed" tag as we've already handled it
                        if tag != "(Info) Already listed" and tag in self.active_analysis_filters:
                            should_hide = True
                            break

            # Hide the row if it should be hidden
            self.candidates_table_view.setRowHidden(row, should_hide)

        self.candidates_table_view.setUpdatesEnabled(True)
