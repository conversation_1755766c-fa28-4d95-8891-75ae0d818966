from PyQt5.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant
from PyQt5.QtGui import QColor
from datetime import datetime
from decimal import Decimal
from typing import List, Dict, Any, Optional

from utils.position_sizing import calculate_recommended_quantity

class TransactionsTableModel(QAbstractTableModel):
    """Table model for displaying transaction data in a QTableView."""

    def __init__(self, transactions: List[Dict[str, Any]], parent=None):
        super().__init__(parent)
        self.transactions = transactions
        self.headers = [
            "ID", "Date", "Item ID", "Item", "Quantity", "Unit Price",
            "Total", "Type", "Net Amount", "Strategy", "Profit"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.transactions)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.headers[section]
        return QVariant()

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self.transactions)):
            return QVariant()

        transaction = self.transactions[index.row()]
        column = index.column()

        # Handle display role (text shown in cells)
        if role == Qt.DisplayRole:
            if column == 0:  # ID
                return str(transaction['tx_id'])
            elif column == 1:  # Date
                # Format datetime for display
                dt = transaction['timestamp']
                if isinstance(dt, str):
                    # Parse ISO format string if needed
                    try:
                        dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
                    except ValueError:
                        return dt  # Return as is if parsing fails
                return dt.strftime('%Y-%m-%d %H:%M')
            elif column == 2:  # Item ID
                return str(transaction['item_id'])
            elif column == 3:  # Item
                return transaction['item_name']
            elif column == 4:  # Quantity
                return str(transaction['quantity'])
            elif column == 5:  # Unit Price
                # Format decimal with commas for thousands
                price = transaction['unit_price']
                if isinstance(price, str):
                    price = Decimal(price)
                return f"{price:,.2f}"
            elif column == 6:  # Total
                # Format decimal with commas for thousands
                total = transaction['total_price']
                if isinstance(total, str):
                    total = Decimal(total)
                return f"{total:,.2f}"
            elif column == 7:  # Type
                return transaction['transaction_type']
            elif column == 8:  # Net Amount
                # Format decimal with commas for thousands
                net = transaction['net_amount']
                if isinstance(net, str):
                    net = Decimal(net)
                return f"{net:,.2f}"
            elif column == 9:  # Strategy
                strategy = transaction['strategy']
                return strategy if strategy else "Unclassified"
            elif column == 10:  # Profit
                # Handle sqlite3.Row objects which don't have .get() method
                try:
                    profit = transaction['profit']
                    if profit is None:
                        return "N/A"
                    if isinstance(profit, str):
                        profit = Decimal(profit)
                    return f"{profit:,.2f}"
                except (IndexError, KeyError):
                    return "N/A"

        # Handle text alignment
        elif role == Qt.TextAlignmentRole:
            if column in [2, 4, 5, 6, 8, 10]:  # Numeric columns
                return Qt.AlignRight | Qt.AlignVCenter
            return Qt.AlignLeft | Qt.AlignVCenter

        # Handle background color
        elif role == Qt.BackgroundRole:
            # Color rows based on transaction type
            if transaction['transaction_type'] == 'BUY':
                return QColor(30, 50, 80)  # Dark blue for buys
            else:  # SELL
                return QColor(80, 30, 50)  # Dark pink for sells

        # Handle text color for all cells
        elif role == Qt.ForegroundRole:
            # Set white text color for all cells for better contrast with dark backgrounds
            if column == 10:  # Profit column - special coloring
                try:
                    profit = transaction['profit']
                    if profit is not None:
                        if isinstance(profit, str):
                            profit = Decimal(profit)
                        if profit > 0:
                            return QColor(0, 255, 0)  # Bright green for profit
                        elif profit < 0:
                            return QColor(255, 0, 0)  # Red for loss
                except (IndexError, KeyError):
                    pass  # No profit data available
            return QColor(255, 255, 255)  # White text for all other cells

        return QVariant()

    def update_data(self, transactions: List[Dict[str, Any]]):
        """Update the model with new transaction data."""
        self.beginResetModel()
        self.transactions = transactions
        self.endResetModel()


class OrdersTableModel(QAbstractTableModel):
    """Table model for displaying order data in a QTableView."""

    def __init__(self, orders: List[Dict[str, Any]], parent=None):
        super().__init__(parent)
        self.orders = orders
        self.headers = [
            "ID", "Item", "Type", "Original Qty", "Remaining Qty",
            "Price", "Placed Date", "Strategy"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.orders)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.headers[section]
        return QVariant()

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self.orders)):
            return QVariant()

        order = self.orders[index.row()]
        column = index.column()

        # Handle display role (text shown in cells)
        if role == Qt.DisplayRole:
            if column == 0:  # ID
                return str(order['order_id'])
            elif column == 1:  # Item
                return order['item_name']
            elif column == 2:  # Type
                return order['order_type']
            elif column == 3:  # Original Qty
                return str(order['quantity_original'])
            elif column == 4:  # Remaining Qty
                return str(order['quantity_remaining'])
            elif column == 5:  # Price
                # Format decimal with commas for thousands
                price = order['price']
                if isinstance(price, str):
                    price = Decimal(price)
                return f"{price:,.2f}"
            elif column == 6:  # Placed Date
                # Format datetime for display
                dt = order['placed_time']
                if isinstance(dt, str):
                    # Parse ISO format string if needed
                    try:
                        dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
                    except ValueError:
                        return dt  # Return as is if parsing fails
                return dt.strftime('%Y-%m-%d %H:%M')
            elif column == 7:  # Strategy
                try:
                    strategy = order['strategy']
                    return strategy if strategy else "Unclassified"
                except (IndexError, KeyError):
                    return "Unclassified"

        # Handle text alignment
        elif role == Qt.TextAlignmentRole:
            if column in [3, 4, 5]:  # Numeric columns
                return Qt.AlignRight | Qt.AlignVCenter
            return Qt.AlignLeft | Qt.AlignVCenter

        # Handle background color
        elif role == Qt.BackgroundRole:
            # Color rows based on order type
            if order['order_type'] == 'BUY':
                return QColor(30, 50, 80)  # Dark blue for buys
            else:  # SELL
                return QColor(80, 30, 50)  # Dark pink for sells

        # Handle text color for all cells
        elif role == Qt.ForegroundRole:
            # Set white text color for all cells for better contrast with dark backgrounds
            return QColor(255, 255, 255)  # White text

        return QVariant()

    def update_data(self, orders: List[Dict[str, Any]]):
        """Update the model with new order data."""
        self.beginResetModel()
        self.orders = orders
        self.endResetModel()


class ItemCandidatesTableModel(QAbstractTableModel):
    """Table model for displaying item candidates in a QTableView."""

    def __init__(self, candidates: List[Dict[str, Any]], parent=None):
        super().__init__(parent)
        self.candidates = candidates
        self.headers = [
            "Item ID", "Item Name", "Buy Price", "Sell Price",
            "Daily Volume", "Margin %", "Profit/Unit", "Total Profit", "Recommended Qty"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.candidates)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.headers[section]
        return QVariant()

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self.candidates)):
            return QVariant()

        candidate = self.candidates[index.row()]
        column = index.column()

        # Handle display role (text shown in cells)
        if role == Qt.DisplayRole:
            if column == 0:  # Item ID
                return str(candidate['item_id'])
            elif column == 1:  # Item Name
                return candidate['item_name']
            elif column == 2:  # Buy Price
                # Format decimal with commas for thousands
                price = candidate['best_buy_price']
                if isinstance(price, str):
                    price = Decimal(price)
                return f"{price:,.2f}"
            elif column == 3:  # Sell Price
                # Format decimal with commas for thousands
                price = candidate['best_sell_price']
                if isinstance(price, str):
                    price = Decimal(price)
                return f"{price:,.2f}"
            elif column == 4:  # Daily Volume
                return str(candidate['daily_volume'])
            elif column == 5:  # Margin %
                margin = candidate['margin_percent']
                if isinstance(margin, str):
                    margin = Decimal(margin)
                return f"{margin:.2f}%"
            elif column == 6:  # Profit/Unit
                profit = candidate['expected_profit']
                if isinstance(profit, str):
                    profit = Decimal(profit)
                return f"{profit:,.2f}"
            elif column == 7:  # Total Profit
                profit = candidate['total_expected_profit']
                if isinstance(profit, str):
                    profit = Decimal(profit)
                return f"{profit:,.2f}"
            elif column == 8:  # Recommended Qty
                # Calculate recommended quantity if not already present
                if 'recommended_quantity' not in candidate:
                    buy_price = candidate['best_buy_price']
                    if isinstance(buy_price, str):
                        buy_price = Decimal(buy_price)

                    daily_volume = candidate['daily_volume']
                    if isinstance(daily_volume, str):
                        daily_volume = int(daily_volume)

                    # Use the position sizing utility to calculate recommended quantity
                    recommended_qty = calculate_recommended_quantity(
                        buy_price=buy_price,
                        daily_volume=daily_volume
                    )
                    # Store it in the candidate data for future reference
                    candidate['recommended_quantity'] = recommended_qty

                return str(candidate['recommended_quantity'])

        # Handle text alignment
        elif role == Qt.TextAlignmentRole:
            if column in [0, 2, 3, 4, 5, 6, 7, 8]:  # Numeric columns
                return Qt.AlignRight | Qt.AlignVCenter
            return Qt.AlignLeft | Qt.AlignVCenter

        # Handle background color for alternating rows
        elif role == Qt.BackgroundRole:
            if index.row() % 2 == 0:
                return QColor(40, 40, 60)  # Dark alternating color
            else:
                return QColor(50, 50, 70)  # Slightly lighter dark color

        # Handle text color for all cells
        elif role == Qt.ForegroundRole:
            # Set white text color for all cells for better contrast with dark backgrounds
            return QColor(255, 255, 255)  # White text

        return QVariant()

    def update_data(self, candidates: List[Dict[str, Any]]):
        """Update the model with new candidate data."""
        self.beginResetModel()
        self.candidates = candidates
        self.endResetModel()

    def get_data_at_row(self, row: int) -> Optional[Dict[str, Any]]:
        """
        Get the data dictionary for a specific row.

        Args:
            row: The row index

        Returns:
            The data dictionary for the row, or None if the row is invalid
        """
        if 0 <= row < len(self.candidates):
            return self.candidates[row]
        return None


class TradeExecutionsTableModel(QAbstractTableModel):
    """Table model for displaying trade executions in a QTableView."""

    def __init__(self, executions: List[Dict[str, Any]], parent=None):
        super().__init__(parent)
        self.executions = executions
        self.headers = [
            "ID", "Date", "Item", "Buy Price", "Quantity",
            "Total Cost", "Expected Profit", "Profit %", "Status"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.executions)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.headers[section]
        return QVariant()

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self.executions)):
            return QVariant()

        execution = self.executions[index.row()]
        column = index.column()

        # Handle display role (text shown in cells)
        if role == Qt.DisplayRole:
            if column == 0:  # ID
                return str(execution['execution_id'])
            elif column == 1:  # Date
                # Format datetime for display
                dt = execution['timestamp']
                if isinstance(dt, str):
                    # Parse ISO format string if needed
                    try:
                        dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
                    except ValueError:
                        return dt  # Return as is if parsing fails
                return dt.strftime('%Y-%m-%d %H:%M')
            elif column == 2:  # Item
                return execution['item_name']
            elif column == 3:  # Buy Price
                # Format decimal with commas for thousands
                price = execution['buy_price']
                if isinstance(price, str):
                    price = Decimal(price)
                return f"{price:,.2f}"
            elif column == 4:  # Quantity
                return str(execution['quantity'])
            elif column == 5:  # Total Cost
                # Format decimal with commas for thousands
                cost = execution['total_cost']
                if isinstance(cost, str):
                    cost = Decimal(cost)
                return f"{cost:,.2f}"
            elif column == 6:  # Expected Profit
                # Format decimal with commas for thousands
                profit = execution['expected_profit']
                if isinstance(profit, str):
                    profit = Decimal(profit)
                return f"{profit:,.2f}"
            elif column == 7:  # Profit %
                percent = execution['expected_profit_percent']
                if isinstance(percent, str):
                    percent = Decimal(percent)
                return f"{percent:.2f}%"
            elif column == 8:  # Status
                return execution['status'].capitalize()

        # Handle text alignment
        elif role == Qt.TextAlignmentRole:
            if column in [0, 3, 4, 5, 6, 7]:  # Numeric columns
                return Qt.AlignRight | Qt.AlignVCenter
            return Qt.AlignLeft | Qt.AlignVCenter

        # Handle background color based on status
        elif role == Qt.BackgroundRole:
            status = execution['status'].lower()
            if status == 'success':
                return QColor(30, 80, 30)  # Dark green for success
            elif status == 'failed':
                return QColor(80, 30, 30)  # Dark red for failed
            elif status == 'pending':
                return QColor(80, 80, 30)  # Dark yellow for pending

        # Handle text color for all cells
        elif role == Qt.ForegroundRole:
            # Set white text color for all cells for better contrast with dark backgrounds
            if column == 8:  # Status column - special coloring
                status = execution['status'].lower()
                if status == 'success':
                    return QColor(0, 255, 0)  # Bright green for success
                elif status == 'failed':
                    return QColor(255, 0, 0)  # Red for failed
                elif status == 'pending':
                    return QColor(255, 255, 0)  # Bright yellow for pending
            return QColor(255, 255, 255)  # White text for all other cells

        return QVariant()

    def update_data(self, executions: List[Dict[str, Any]]):
        """Update the model with new execution data."""
        self.beginResetModel()
        self.executions = executions
        self.endResetModel()


class ExecuteItemsTableModel(QAbstractTableModel):
    """Table model for displaying items in the Execute tab."""

    def __init__(self, items: List[Dict[str, Any]], parent=None):
        super().__init__(parent)
        self.items = items
        self.headers = [
            "Item ID", "Item Name", "Buy Price", "Sell Price",
            "Daily Volume", "Margin %", "Profit/Unit", "Recommended Qty", "Custom Qty"
        ]

    def rowCount(self, parent=QModelIndex()):
        return len(self.items)

    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
            return self.headers[section]
        return QVariant()

    def flags(self, index):
        """Return item flags based on column."""
        if not index.isValid():
            return Qt.ItemIsEnabled

        # Make only the Custom Qty column (column 8) editable
        if index.column() == 8:
            return super().flags(index) | Qt.ItemIsEditable

        return super().flags(index)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self.items)):
            return QVariant()

        item = self.items[index.row()]
        column = index.column()

        # Handle display role (text shown in cells)
        if role == Qt.DisplayRole or role == Qt.EditRole:  # Support both display and edit roles
            if column == 0:  # Item ID
                return str(item['item_id'])
            elif column == 1:  # Item Name
                return item['item_name']
            elif column == 2:  # Buy Price
                # Format decimal with commas for thousands
                price = item['best_buy_price']
                if isinstance(price, str):
                    price = Decimal(price)
                return f"{price:,.2f}"
            elif column == 3:  # Sell Price
                # Format decimal with commas for thousands
                price = item['best_sell_price']
                if isinstance(price, str):
                    price = Decimal(price)
                return f"{price:,.2f}"
            elif column == 4:  # Daily Volume
                return str(item['daily_volume'])
            elif column == 5:  # Margin %
                margin = item['margin_percent']
                if isinstance(margin, str):
                    margin = Decimal(margin)
                return f"{margin:.2f}%"
            elif column == 6:  # Profit/Unit
                profit = item['expected_profit']
                if isinstance(profit, str):
                    profit = Decimal(profit)
                return f"{profit:,.2f}"
            elif column == 7:  # Recommended Qty
                # Calculate recommended quantity if not already present
                if 'recommended_quantity' not in item:
                    buy_price = item['best_buy_price']
                    if isinstance(buy_price, str):
                        buy_price = Decimal(buy_price)

                    daily_volume = item['daily_volume']
                    if isinstance(daily_volume, str):
                        daily_volume = int(daily_volume)

                    # Use the position sizing utility to calculate recommended quantity
                    recommended_qty = calculate_recommended_quantity(
                        buy_price=buy_price,
                        daily_volume=daily_volume
                    )
                    # Store it in the item data for future reference
                    item['recommended_quantity'] = recommended_qty

                return str(item['recommended_quantity'])
            elif column == 8:  # Custom Qty
                # Return custom quantity if set, otherwise empty
                if 'custom_quantity' in item and item['custom_quantity'] is not None:
                    return str(item['custom_quantity'])
                return ""

        # Handle text alignment
        elif role == Qt.TextAlignmentRole:
            if column in [0, 2, 3, 4, 5, 6, 7, 8]:  # Numeric columns
                return Qt.AlignRight | Qt.AlignVCenter
            return Qt.AlignLeft | Qt.AlignVCenter

        # Handle background color for alternating rows
        elif role == Qt.BackgroundRole:
            if index.row() % 2 == 0:
                return QColor(40, 40, 60)  # Dark alternating color
            else:
                return QColor(50, 50, 70)  # Slightly lighter dark color

        # Handle text color for all cells
        elif role == Qt.ForegroundRole:
            # Set white text color for all cells for better contrast with dark backgrounds
            return QColor(255, 255, 255)  # White text

        # Add tooltip for the Custom Qty column
        elif role == Qt.ToolTipRole and column == 8:
            return "Double-click to edit custom quantity"

        return QVariant()

    def setData(self, index, value, role=Qt.EditRole):
        """Set data when user edits a cell."""
        if not index.isValid() or role != Qt.EditRole:
            return False

        row = index.row()
        column = index.column()

        # Only allow editing the Custom Qty column
        if column == 8 and 0 <= row < len(self.items):
            try:
                # Convert the value to an integer
                quantity = int(value)
                if quantity <= 0:
                    return False  # Don't allow negative or zero quantities

                # Update the custom quantity
                self.items[row]['custom_quantity'] = quantity

                # Emit dataChanged signal
                self.dataChanged.emit(index, index, [Qt.DisplayRole])
                return True
            except (ValueError, TypeError):
                # If conversion fails, don't update the data
                return False

        return False

    def update_data(self, items: List[Dict[str, Any]]):
        """Update the model with new item data."""
        self.beginResetModel()
        self.items = items
        self.endResetModel()

    def get_data_at_row(self, row: int) -> Optional[Dict[str, Any]]:
        """
        Get the data dictionary for a specific row.

        Args:
            row: The row index

        Returns:
            The data dictionary for the row, or None if the row is invalid
        """
        if 0 <= row < len(self.items):
            return self.items[row]
        return None

    def set_custom_quantity(self, row: int, quantity: int) -> bool:
        """
        Set a custom quantity for an item.

        Args:
            row: The row index
            quantity: The custom quantity to set

        Returns:
            True if successful, False otherwise
        """
        if 0 <= row < len(self.items):
            self.items[row]['custom_quantity'] = quantity
            # Emit dataChanged signal for the custom quantity cell
            self.dataChanged.emit(
                self.index(row, 8),  # Custom Qty column
                self.index(row, 8),
                [Qt.DisplayRole]
            )
            return True
        return False

    def set_custom_quantity_for_all(self, max_spend: Decimal) -> int:
        """
        Set custom quantities for all items based on max spend.

        Args:
            max_spend: Maximum amount to spend per item

        Returns:
            Number of items updated
        """
        count = 0
        for row, item in enumerate(self.items):
            buy_price = item['best_buy_price']
            if isinstance(buy_price, str):
                buy_price = Decimal(buy_price)

            if buy_price > 0:
                # Calculate max quantity based on max spend
                max_qty = int(max_spend / buy_price)

                # Set custom quantity
                item['custom_quantity'] = max_qty
                count += 1

        # Emit dataChanged signal for all rows
        if count > 0:
            self.dataChanged.emit(
                self.index(0, 8),  # Custom Qty column
                self.index(len(self.items) - 1, 8),
                [Qt.DisplayRole]
            )

        return count

