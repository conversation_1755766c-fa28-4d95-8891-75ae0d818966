"""
Execute Tab for TinyTrader

This module provides the UI for the execute tab functionality.
"""

import os
import logging
import threading
import pyperclip
from typing import List, Dict, Any, Optional
from PyQt5 import uic
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QScrollArea, QTableView, QPushButton,
    QLabel, QHBoxLayout, QHeaderView, QMessageBox, QProgressDialog,
    QLineEdit, QFormLayout, QGroupBox, QSpinBox, QDoubleSpinBox,
    QTextEdit
)
from PyQt5.QtCore import Qt, pyqtSlot, pyqtSignal
from decimal import Decimal

from views.table_models import ExecuteItemsTableModel
from utils.tinygamer_bridge import TinyGamerBridge

class ExecuteTab(QWidget):
    """
    Widget for the Execute tab.

    This tab allows users to:
    - View a list of items added from the Search tab
    - Execute trades for these items using TinyGamer
    """

    # Signals for execution status updates
    execution_status = pyqtSignal(str)
    execution_success = pyqtSignal(str, str)  # title, message
    execution_warning = pyqtSignal(str, str)  # title, message
    execution_error = pyqtSignal(str, str)    # title, message

    # Signals for progress dialog updates
    progress_update = pyqtSignal(int, str)  # value, label_text
    progress_complete = pyqtSignal()

    def __init__(self, parent=None):
        """
        Initialize the Execute tab.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)

        # Set up logging
        self.logger = logging.getLogger("ExecuteTab")

        # Initialize TinyGamer bridge
        self.tinygamer_bridge = None

        # Flag to track if TinyGamer is initialized
        self.tinygamer_initialized = False

        # List to store items
        self.execute_items = []

        # Create a scroll area to make the tab scrollable
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a widget to hold the content
        self.content_widget = QWidget()

        # Set up the main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(self.scroll_area)

        # Create a layout for the content widget
        content_layout = QVBoxLayout(self.content_widget)

        # Add a title label
        title_label = QLabel("Items to Execute")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        content_layout.addWidget(title_label)

        # Add TinyGamer log display
        log_group = QGroupBox("TinyGamer Status")
        log_layout = QVBoxLayout()

        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMaximumHeight(150)
        self.log_display.setStyleSheet("background-color: #333333; color: white;")
        log_layout.addWidget(self.log_display)

        log_group.setLayout(log_layout)
        content_layout.addWidget(log_group)

        # Start a timer to periodically check for TinyGamer logs
        self.log_timer = None

        # Add quantity controls
        controls_group = QGroupBox("Trade Controls")
        controls_layout = QFormLayout()

        # Max spend input
        self.max_spend_input = QDoubleSpinBox()
        self.max_spend_input.setMinimum(1000)
        self.max_spend_input.setMaximum(10000000000)  # 10 billion
        self.max_spend_input.setValue(50000000)  # Default 50M ISK
        self.max_spend_input.setSingleStep(1000000)
        self.max_spend_input.setDecimals(0)
        self.max_spend_input.setSuffix(" ISK")
        controls_layout.addRow("Max Spend per Item:", self.max_spend_input)

        # Apply max spend button
        self.apply_max_spend_button = QPushButton("Apply to All Items")
        self.apply_max_spend_button.clicked.connect(self._on_apply_max_spend_clicked)
        controls_layout.addRow("", self.apply_max_spend_button)

        controls_group.setLayout(controls_layout)
        content_layout.addWidget(controls_group)

        # Add a table view for the items
        self.items_table_view = QTableView()
        self.items_table_view.setSelectionBehavior(QTableView.SelectRows)
        self.items_table_view.setSelectionMode(QTableView.SingleSelection)
        content_layout.addWidget(self.items_table_view)

        # Add buttons
        button_layout = QHBoxLayout()

        self.remove_button = QPushButton("Remove Selected")
        self.remove_button.clicked.connect(self._on_remove_clicked)
        button_layout.addWidget(self.remove_button)

        self.clear_button = QPushButton("Clear All")
        self.clear_button.clicked.connect(self._on_clear_clicked)
        button_layout.addWidget(self.clear_button)

        self.execute_button = QPushButton("Execute Selected Trade")
        self.execute_button.clicked.connect(self._on_execute_clicked)
        button_layout.addWidget(self.execute_button)

        self.execute_all_button = QPushButton("Execute All Trades")
        self.execute_all_button.clicked.connect(self._on_execute_all_clicked)
        button_layout.addWidget(self.execute_all_button)

        # Add Copy to Clipboard button
        self.copy_to_clipboard_button = QPushButton("Copy to Clipboard")
        self.copy_to_clipboard_button.clicked.connect(self._on_copy_to_clipboard_clicked)
        button_layout.addWidget(self.copy_to_clipboard_button)

        # Add Sell Inventory button
        self.sell_inventory_button = QPushButton("Sell Inventory")
        self.sell_inventory_button.clicked.connect(self._on_sell_inventory_clicked)
        button_layout.addWidget(self.sell_inventory_button)

        content_layout.addLayout(button_layout)

        # Set the content widget as the scroll area's widget
        self.scroll_area.setWidget(self.content_widget)

        # Initialize the table model
        self._init_table_model()

        # Connect signals to slots
        self.execution_success.connect(self._show_success_message)
        self.execution_warning.connect(self._show_warning_message)
        self.execution_error.connect(self._show_error_message)

    def _init_table_model(self):
        """Initialize the table model for the items."""
        self.items_model = ExecuteItemsTableModel(self.execute_items)
        self.items_table_view.setModel(self.items_model)

        # Set column widths
        header = self.items_table_view.horizontalHeader()
        if header and self.items_model.columnCount() > 0:
            # Item name column should stretch
            header.setSectionResizeMode(1, QHeaderView.Stretch)

            # Set fixed width for other columns
            for i in range(self.items_model.columnCount()):
                if i != 1:  # Skip item name column
                    self.items_table_view.setColumnWidth(i, 100)

    def add_item(self, item_data: Dict[str, Any]):
        """
        Add an item to the execute list.

        Args:
            item_data: Dictionary containing item data
        """
        # Check if item already exists in the list
        for existing_item in self.execute_items:
            if existing_item.get('item_id') == item_data.get('item_id'):
                # Item already exists, log a message
                self.logger.info(f"The item '{item_data.get('item_name')}' is already in the execute list.")
                return

        # Add the item to the list
        self.execute_items.append(item_data.copy())

        # Update the model
        self.items_model.update_data(self.execute_items)

        # Resize columns
        self.items_table_view.resizeColumnsToContents()

    def _on_remove_clicked(self):
        """Handle remove button click."""
        selected_rows = self.items_table_view.selectionModel().selectedRows()
        if not selected_rows:
            self.logger.info("No item selected for removal.")
            return

        # Get the selected row index
        row_index = selected_rows[0].row()

        # Remove the item from the list
        if 0 <= row_index < len(self.execute_items):
            del self.execute_items[row_index]

            # Update the model
            self.items_model.update_data(self.execute_items)

    def _on_clear_clicked(self):
        """Handle clear button click."""
        if not self.execute_items:
            return

        # Clear the list
        self.execute_items.clear()

        # Update the model
        self.items_model.update_data(self.execute_items)

        # Log the action
        self.logger.info("Cleared all items from the execute list.")

    def _on_execute_clicked(self):
        """Handle execute button click."""
        if not self.execute_items:
            self.logger.info("No items to execute trades for.")
            return

        # Get the selected row
        selected_rows = self.items_table_view.selectionModel().selectedRows()
        if not selected_rows:
            self.logger.info("No item selected for trade execution.")
            return

        # Get the selected row index
        row_index = selected_rows[0].row()

        # Get the item data
        if 0 <= row_index < len(self.execute_items):
            item_data = self.execute_items[row_index]

            # Initialize TinyGamer if not already initialized
            if not self.tinygamer_initialized:
                self._initialize_tinygamer()

            if not self.tinygamer_initialized:
                self.logger.error("Failed to initialize TinyGamer. Please check the logs.")
                return

            # Execute the trade
            self._execute_trade(item_data)
        else:
            self.logger.warning("Invalid selection. Please select a valid item.")

    def _initialize_tinygamer(self):
        """Initialize TinyGamer bridge."""
        try:
            # Create progress dialog
            progress = QProgressDialog("Initializing TinyGamer...", "Cancel", 0, 0, self)
            progress.setWindowTitle("TinyGamer Initialization")
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # Initialize TinyGamer bridge
            self.tinygamer_bridge = TinyGamerBridge()

            # Launch TinyGamer
            if self.tinygamer_bridge.launch_tinygamer():
                self.tinygamer_initialized = True
                self.logger.info("TinyGamer initialized successfully")

                # Start the log timer to periodically check for TinyGamer logs
                self._start_log_timer()
            else:
                self.logger.error("Failed to initialize TinyGamer")

            # Close progress dialog
            progress.close()

        except Exception as e:
            self.logger.error(f"Error initializing TinyGamer: {str(e)}")

    def _execute_trade(self, item_data: Dict[str, Any]):
        """
        Execute a trade for the specified item.

        Args:
            item_data: Dictionary containing item data
        """
        try:
            # Get item name, quantity, and price
            item_name = item_data.get('item_name', '')

            # Use custom quantity if available, otherwise use recommended quantity
            if 'custom_quantity' in item_data and item_data['custom_quantity'] is not None:
                quantity = str(item_data['custom_quantity'])
            else:
                quantity = str(item_data.get('recommended_quantity', 1))

            # Get the buy price - use best_buy_price if available
            # Note: The TinyGamerBridge will add a competitive increment to this price
            price = item_data.get('best_buy_price', 0)
            if isinstance(price, str):
                price_str = price
                try:
                    price_float = float(price)
                    if price_float < 100.0:
                        increment_desc = "+0.01 ISK"
                    elif price_float < 1000.0:
                        increment_desc = "+1 ISK"
                    elif price_float < 100000.0:
                        increment_desc = "+10 ISK"
                    else:
                        increment_desc = "+100 ISK"
                except ValueError:
                    increment_desc = "competitive increment"
            else:
                # Format the price as a string with 2 decimal places
                price_float = float(price)
                price_str = f"{price_float:.2f}"
                if price_float < 100.0:
                    increment_desc = "+0.01 ISK"
                elif price_float < 1000.0:
                    increment_desc = "+1 ISK"
                elif price_float < 100000.0:
                    increment_desc = "+10 ISK"
                else:
                    increment_desc = "+100 ISK"

            self.logger.info(f"Executing trade for {quantity} units of {item_name} at {price_str} ISK (TinyGamer will add {increment_desc})")

            # Execute the trade in a separate thread to avoid blocking the UI
            threading.Thread(
                target=self._execute_trade_thread,
                args=(item_name, quantity, price_str),
                daemon=True
            ).start()

        except Exception as e:
            self.logger.error(f"Error executing trade: {str(e)}")

    def _execute_trade_thread(self, item_name: str, quantity: str, price: str):
        """
        Thread function to execute a trade.

        Args:
            item_name: Name of the item to buy
            quantity: Quantity to buy
            price: Price to set for the buy order
        """
        try:
            # Execute the buy order
            success, error_message = self.tinygamer_bridge.execute_buy_order(item_name, quantity, price)

            if success:
                self.logger.info(f"Successfully initiated buy order for {quantity} units of {item_name}")

                # Show initial success message using signal
                self.execution_success.emit(
                    "Trade Execution",
                    f"Buy order for {quantity} units of {item_name} has been initiated. Waiting for completion..."
                )

                # Wait for task completion (up to 60 seconds)
                status_available, status_data = self.tinygamer_bridge.check_task_status(timeout=60.0, clear_status=True)

                if status_available:
                    task_success = status_data.get('success', False)
                    task_message = status_data.get('message', 'No message provided')

                    if task_success:
                        self.logger.info(f"TinyGamer task completed successfully: {task_message}")
                        # Show success message using signal
                        # Calculate the adjusted price with the appropriate increment
                        try:
                            price_float = float(price)
                            # Determine increment based on price value
                            if price_float < 100.0:
                                increment = 0.01
                                increment_desc = "0.01 ISK"
                            elif price_float < 1000.0:
                                increment = 1.0
                                increment_desc = "1 ISK"
                            elif price_float < 100000.0:
                                increment = 10.0
                                increment_desc = "10 ISK"
                            else:
                                increment = 100.0
                                increment_desc = "100 ISK"

                            adjusted_price = price_float + increment
                            adjusted_price_str = f"{adjusted_price:.2f}"
                        except ValueError:
                            adjusted_price_str = "adjusted price"
                            increment_desc = "competitive increment"

                        self.execution_success.emit(
                            "Trade Execution Complete",
                            f"Buy order for {quantity} units of {item_name} has been placed successfully at {adjusted_price_str} ISK (+{increment_desc})."
                        )

                        # Refresh the analysis results in the Search tab
                        self._refresh_search_tab_analysis_results()
                    else:
                        self.logger.error(f"TinyGamer task failed: {task_message}")
                        # Show warning message using signal
                        self.execution_warning.emit(
                            "Trade Execution Issue",
                            f"There was an issue with the buy order: {task_message}"
                        )
                else:
                    self.logger.warning("No status update received from TinyGamer")
                    # Show warning message using signal
                    self.execution_warning.emit(
                        "Trade Execution Status",
                        f"Buy order for {quantity} units of {item_name} was initiated, but no completion status was received."
                    )
            else:
                self.logger.error(f"Failed to execute buy order for {quantity} units of {item_name}: {error_message}")
                # Show error message using signal
                self.execution_error.emit(
                    "Trade Execution Error",
                    f"Failed to execute buy order for {quantity} units of {item_name}. {error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error in trade execution thread: {str(e)}")
            # Show error message using signal
            self.execution_error.emit(
                "Trade Execution Error",
                f"Error executing trade: {str(e)}"
            )

    def _on_execute_all_clicked(self):
        """Handle execute all button click."""
        if not self.execute_items:
            self.logger.info("No items to execute trades for.")
            return

        # Initialize TinyGamer if not already initialized
        if not self.tinygamer_initialized:
            self._initialize_tinygamer()

        if not self.tinygamer_initialized:
            self.logger.error("Failed to initialize TinyGamer. Please check the logs.")
            return

        # Create a progress dialog in the main thread
        total_items = len(self.execute_items)
        self.progress_dialog = QProgressDialog("Executing trades...", "Cancel", 0, total_items, self)
        self.progress_dialog.setWindowTitle("Trade Execution Progress")
        self.progress_dialog.setWindowModality(Qt.WindowModal)

        # Connect signals for progress updates
        self.progress_update.connect(self._update_progress_dialog)
        self.progress_complete.connect(self._close_progress_dialog)

        # Show the progress dialog
        self.progress_dialog.show()

        # Execute all trades in a separate thread
        threading.Thread(
            target=self._execute_all_trades_thread,
            daemon=True
        ).start()

    def _execute_all_trades_thread(self):
        """Thread function to execute all trades in sequence."""
        try:
            total_items = len(self.execute_items)
            successful_trades = 0
            failed_trades = 0

            for i, item_data in enumerate(self.execute_items):
                # Update progress dialog via signal
                self.progress_update.emit(i, f"Executing trade {i+1} of {total_items}: {item_data.get('item_name', '')}")

                # Check if the user canceled (need to access from main thread)
                if hasattr(self, 'progress_dialog') and self.progress_dialog and self.progress_dialog.wasCanceled():
                    break

                # Get item details
                item_name = item_data.get('item_name', '')

                # Use custom quantity if available, otherwise use recommended quantity
                if 'custom_quantity' in item_data and item_data['custom_quantity'] is not None:
                    quantity = str(item_data['custom_quantity'])
                else:
                    quantity = str(item_data.get('recommended_quantity', 1))

                # Get the buy price
                price = item_data.get('best_buy_price', 0)
                if isinstance(price, str):
                    price_str = price
                else:
                    price_str = f"{float(price):.2f}"

                # Determine the increment description based on price
                try:
                    price_float = float(price_str)
                    if price_float < 100.0:
                        increment_desc = "+0.01 ISK"
                    elif price_float < 1000.0:
                        increment_desc = "+1 ISK"
                    elif price_float < 100000.0:
                        increment_desc = "+10 ISK"
                    else:
                        increment_desc = "+100 ISK"
                except ValueError:
                    increment_desc = "competitive increment"

                # Execute the buy order
                self.logger.info(f"Executing trade {i+1} of {total_items}: {quantity} units of {item_name} at {price_str} ISK (TinyGamer will add {increment_desc})")

                success, error_message = self.tinygamer_bridge.execute_buy_order(item_name, quantity, price_str)

                if success:
                    # Wait for task completion (up to 60 seconds)
                    status_available, status_data = self.tinygamer_bridge.check_task_status(timeout=60.0, clear_status=True)

                    if status_available and status_data.get('success', False):
                        self.logger.info(f"Successfully executed buy order for {quantity} units of {item_name}")
                        successful_trades += 1
                    else:
                        task_message = status_data.get('message', 'Unknown error') if status_available else 'No status update received'
                        self.logger.error(f"Failed to complete buy order for {quantity} units of {item_name}: {task_message}")
                        failed_trades += 1
                else:
                    self.logger.error(f"Failed to initiate buy order for {quantity} units of {item_name}: {error_message}")
                    failed_trades += 1

            # Signal to close the progress dialog
            self.progress_complete.emit()

            # Show summary message
            self.execution_success.emit(
                "Trade Execution Summary",
                f"Completed {successful_trades} of {total_items} trades successfully.\n"
                f"Failed trades: {failed_trades}"
            )

            # If any trades were successful, refresh the analysis results in the Search tab
            if successful_trades > 0:
                self._refresh_search_tab_analysis_results()

        except Exception as e:
            self.logger.error(f"Error executing all trades: {str(e)}")
            self.execution_error.emit(
                "Trade Execution Error",
                f"Error executing trades: {str(e)}"
            )
            # Make sure to close the progress dialog in case of error
            self.progress_complete.emit()

    def _start_log_timer(self):
        """Start a timer to periodically check for TinyGamer logs."""
        from PyQt5.QtCore import QTimer

        # Create a timer if it doesn't exist
        if not self.log_timer:
            self.log_timer = QTimer()
            self.log_timer.timeout.connect(self._update_logs)
            self.log_timer.start(1000)  # Check every second
            self.logger.info("Started TinyGamer log monitoring")

    def _update_logs(self):
        """Update the log display with new TinyGamer logs."""
        if not self.tinygamer_bridge:
            return

        try:
            # Get new log entries
            success, log_lines = self.tinygamer_bridge.get_tinygamer_logs()

            if success and log_lines:
                # Add new log entries to the display
                for line in log_lines:
                    self.log_display.append(line)

                # Auto-scroll to bottom
                scrollbar = self.log_display.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())
        except Exception as e:
            self.logger.error(f"Error updating TinyGamer logs: {str(e)}")

    def cleanup(self):
        """Clean up resources when the tab is closed."""
        # Stop the log timer if it's running
        if self.log_timer and self.log_timer.isActive():
            self.log_timer.stop()
            self.logger.info("Stopped TinyGamer log monitoring")

        # Close progress dialog if it exists
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            try:
                self.progress_dialog.close()
            except Exception as e:
                self.logger.error(f"Error closing progress dialog: {str(e)}")

        if self.tinygamer_bridge:
            try:
                self.tinygamer_bridge.shutdown()
                self.logger.info("TinyGamer bridge shut down")
            except Exception as e:
                self.logger.error(f"Error shutting down TinyGamer bridge: {str(e)}")

    def closeEvent(self, event):
        """Handle close event."""
        self.cleanup()
        super().closeEvent(event)

    @pyqtSlot(str, str)
    def _show_success_message(self, title, message):
        """Log a success message without showing a dialog."""
        self.logger.info(f"Success: {title} - {message}")

    @pyqtSlot(str, str)
    def _show_warning_message(self, title, message):
        """Log a warning message without showing a dialog."""
        self.logger.warning(f"Warning: {title} - {message}")

    @pyqtSlot(str, str)
    def _show_error_message(self, title, message):
        """Log an error message without showing a dialog."""
        self.logger.error(f"Error: {title} - {message}")

    @pyqtSlot(int, str)
    def _update_progress_dialog(self, value, label_text):
        """Update the progress dialog from the worker thread."""
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.setValue(value)
            self.progress_dialog.setLabelText(label_text)

    @pyqtSlot()
    def _close_progress_dialog(self):
        """Close the progress dialog from the worker thread."""
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.setValue(self.progress_dialog.maximum())
            # Let the dialog close itself after a short delay
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(500, self.progress_dialog.close)



    def _on_apply_max_spend_clicked(self):
        """Handle apply max spend button click."""
        if not self.execute_items:
            self.logger.info("No items to apply max spend to.")
            return

        # Get the max spend value
        max_spend = Decimal(str(self.max_spend_input.value()))

        # Apply max spend to all items
        count = self.items_model.set_custom_quantity_for_all(max_spend)

        if count > 0:
            self.logger.info(f"Max spend of {max_spend:,.0f} ISK has been applied to {count} items.")
        else:
            self.logger.warning("Failed to apply max spend. Please try again.")

    def _on_copy_to_clipboard_clicked(self):
        """Handle copy to clipboard button click."""
        # Check if there are any items in the list
        if not self.execute_items:
            self.logger.info("No items to copy to clipboard.")
            return

        # Get the selected row
        selected_rows = self.items_table_view.selectionModel().selectedRows()

        # If a row is selected, copy just that item
        if selected_rows:
            # Get the selected row index
            row_index = selected_rows[0].row()

            # Get the item data
            if 0 <= row_index < len(self.execute_items):
                item_data = self.execute_items[row_index]

                # Format the clipboard text
                clipboard_text = self._format_item_for_clipboard(item_data)

                # Copy to clipboard
                pyperclip.copy(clipboard_text)

                # Log success message
                self.logger.info(f"Item '{item_data.get('item_name')}' has been copied to clipboard.")
            else:
                self.logger.warning("Invalid selection. Please select a valid item.")
        else:
            # No row selected, copy all items
            # Format all items for clipboard
            clipboard_text = ""
            for item_data in self.execute_items:
                clipboard_text += self._format_item_for_clipboard(item_data) + "\n"

            # Remove the last newline
            if clipboard_text.endswith("\n"):
                clipboard_text = clipboard_text[:-1]

            # Copy to clipboard
            pyperclip.copy(clipboard_text)

            # Log success message
            self.logger.info(f"All {len(self.execute_items)} items have been copied to clipboard.")

    def _format_item_for_clipboard(self, item_data: Dict[str, Any]) -> str:
        """
        Format an item for clipboard in the format expected by TinyGamer.

        Args:
            item_data: Dictionary containing item data

        Returns:
            Formatted string in the format "ItemName Quantity"
        """
        # Get item name
        item_name = item_data.get('item_name', '')

        # Use custom quantity if available, otherwise use recommended quantity
        if 'custom_quantity' in item_data and item_data['custom_quantity'] is not None:
            quantity = str(item_data['custom_quantity'])
        else:
            quantity = str(item_data.get('recommended_quantity', 1))

        # Format the clipboard text
        return f"{item_name} {quantity}"

    def _on_sell_inventory_clicked(self):
        """Handle sell inventory button click."""
        # Initialize TinyGamer if not already initialized
        if not self.tinygamer_initialized:
            self._initialize_tinygamer()

        if not self.tinygamer_initialized:
            self.logger.error("Failed to initialize TinyGamer. Please check the logs.")
            return

        # Execute the sell inventory task in a separate thread to avoid blocking the UI
        threading.Thread(
            target=self._execute_sell_inventory_thread,
            daemon=True
        ).start()

    def _execute_sell_inventory_thread(self):
        """Thread function to execute the sell inventory task."""
        try:
            self.logger.info("Executing sell inventory task...")

            # Show initial message
            self.execution_status.emit("Executing sell inventory task...")

            # Execute the sell inventory task
            success, error_message = self.tinygamer_bridge.execute_sell_inventory()

            if success:
                self.logger.info("Successfully initiated sell inventory task")

                # Show initial success message using signal
                self.execution_success.emit(
                    "Sell Inventory",
                    "Sell inventory task has been initiated. Waiting for completion..."
                )

                # Wait for task completion (up to 60 seconds)
                status_available, status_data = self.tinygamer_bridge.check_task_status(timeout=60.0, clear_status=True)

                if status_available:
                    task_success = status_data.get('success', False)
                    task_message = status_data.get('message', 'No message provided')

                    if task_success:
                        self.logger.info(f"TinyGamer sell inventory task completed successfully: {task_message}")
                        # Show success message using signal
                        self.execution_success.emit(
                            "Sell Inventory Complete",
                            f"Inventory items have been sold successfully. {task_message}"
                        )
                    else:
                        self.logger.error(f"TinyGamer sell inventory task failed: {task_message}")
                        # Show warning message using signal
                        self.execution_warning.emit(
                            "Sell Inventory Issue",
                            f"There was an issue with selling inventory: {task_message}"
                        )
                else:
                    self.logger.warning("No status update received from TinyGamer")
                    # Show warning message using signal
                    self.execution_warning.emit(
                        "Sell Inventory Status",
                        "Sell inventory task was initiated, but no completion status was received."
                    )
            else:
                self.logger.error(f"Failed to execute sell inventory task: {error_message}")
                # Show error message using signal
                self.execution_error.emit(
                    "Sell Inventory Error",
                    f"Failed to execute sell inventory task. {error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error in sell inventory execution thread: {str(e)}")
            # Show error message using signal
            self.execution_error.emit(
                "Sell Inventory Error",
                f"Error executing sell inventory task: {str(e)}"
            )

    def _refresh_search_tab_analysis_results(self):
        """
        Refresh the analysis results in the Search tab.

        This method is called after a successful buy order to ensure that
        the "Already Listed" status is updated in the Search tab.
        """
        try:
            # Find the main window
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'tabWidget'):
                    main_window = parent
                    break
                parent = parent.parent()

            if not main_window:
                self.logger.warning("Could not find main window to refresh Search tab analysis results")
                return

            # Find the Search tab (should be at index 1)
            search_tab = main_window.tabWidget.widget(1)
            if search_tab and hasattr(search_tab, 'refresh_analysis_results'):
                self.logger.info("Refreshing analysis results in Search tab...")
                search_tab.refresh_analysis_results()
            else:
                self.logger.warning("Could not find Search tab or it doesn't have refresh_analysis_results method")
        except Exception as e:
            self.logger.error(f"Error refreshing Search tab analysis results: {str(e)}")

    def update_status(self, message: str):
        """
        Update the status display with a message.

        This method is called by the main window when the market request handler
        has a status update, such as processing a price request from TinyGamer.

        Args:
            message: Status message to display
        """
        if not hasattr(self, 'log_display') or not self.log_display:
            return

        try:
            # Add the message to the log display
            self.log_display.append(f"Market Request: {message}")

            # Auto-scroll to bottom
            scrollbar = self.log_display.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # Also log to the logger
            self.logger.info(f"Market Request: {message}")
        except Exception as e:
            self.logger.error(f"Error updating status: {str(e)}")
