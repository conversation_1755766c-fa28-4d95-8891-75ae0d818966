"""
Configure Tab for TinyTrader

This module provides the UI for the configure tab functionality, including
displaying marketing skills, broker's fee, and transaction tax.
"""

import os
from decimal import Decimal
from typing import Dict, Any, List, Optional

from PyQt5 import uic
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, QLabel, QGroupBox,
    QFormLayout, QProgressBar, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView
)
from PyQt5.QtCore import Qt, pyqtSlot, pyqtSignal, QThread, QObject
from PyQt5.QtGui import QColor

from models.database import DatabaseManager
from api.esi_client import ESIClient
from controllers.accounting_controller import AccountingController

# Constants for EVE Online skill IDs and tax rates
ACCOUNTING_SKILL_ID = 16622
BROKER_RELATIONS_SKILL_ID = 3446
ADVANCED_BROKER_RELATIONS_SKILL_ID = 3447  # Was Margin Trading before 2020-03-10
MARKETING_SKILL_ID = 16598
RETAIL_SKILL_ID = 16596
TRADE_SKILL_ID = 3443
WHOLESALE_SKILL_ID = 16597
TYCOON_SKILL_ID = 18580
PROCUREMENT_SKILL_ID = 16594
DAYTRADING_SKILL_ID = 16595
VISIBILITY_SKILL_ID = 3447

# Base tax rates
BASE_SALES_TAX_RATE = Decimal('0.075')  # 7.5%
BASE_BROKER_FEE_RATE = Decimal('0.03')  # 3%


class ProfitRecalculationWorker(QObject):
    """Worker class for recalculating profits in a separate thread."""

    # Signal to update progress
    progress_updated = pyqtSignal(int, str)
    # Signal when recalculation is complete
    recalculation_completed = pyqtSignal(int, int)  # success_count, error_count

    def __init__(self, accounting_controller):
        """Initialize the worker with an accounting controller."""
        super().__init__()
        self.accounting_controller = accounting_controller
        self.running = False

    @pyqtSlot()
    def run(self):
        """Run the profit recalculation."""
        if not self.accounting_controller or self.running:
            self.recalculation_completed.emit(0, 0)
            return

        self.running = True

        # Use the accounting controller to recalculate profits
        success_count, error_count = self.accounting_controller.recalculate_all_profits(
            progress_callback=self.progress_updated.emit
        )

        self.running = False
        self.recalculation_completed.emit(success_count, error_count)

class ConfigureTab(QWidget):
    """
    Widget for the Configure tab.

    This tab allows users to:
    - View their marketing skills and ranks
    - See their broker's fee and transaction tax rates
    - Configure other application settings
    """

    # Signal to notify of refresh request
    refresh_requested = pyqtSignal()
    # Signal to start profit recalculation
    start_recalculation_signal = pyqtSignal()

    def __init__(self, db_manager: DatabaseManager, esi_client: Optional[ESIClient] = None, parent=None):
        """
        Initialize the Configure tab.

        Args:
            db_manager: Database manager instance
            esi_client: ESI client instance (optional)
            parent: Parent widget
        """
        super().__init__(parent)
        self.db = db_manager
        self.esi_client = esi_client
        self.skill_cache = {}  # Cache for skill levels
        self.first_show = True  # Flag to track first show event

        # Create accounting controller for profit recalculation
        self.accounting_controller = None
        if self.db and self.esi_client:
            self.accounting_controller = AccountingController(self.db, self.esi_client)

        # Create worker thread for profit recalculation
        self.worker_thread = QThread()
        self.recalculation_worker = None

        # Ensure thread quits when app closes
        from PyQt5.QtWidgets import QApplication
        QApplication.instance().aboutToQuit.connect(self.cleanup)

        # Create the UI
        self._create_ui()

        # Connect signals
        self._connect_signals()

    def showEvent(self, event):
        """Handle show event to refresh data on first show."""
        super().showEvent(event)

        # Refresh skills data on first show
        if self.first_show:
            self.refresh_skills_data()
            self.first_show = False

    def _create_ui(self):
        """Create the UI elements."""
        # Main layout
        main_layout = QVBoxLayout(self)

        # Create a scroll area for the content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # Authentication Group (preserved from original settingsTab)
        auth_group = QGroupBox("Authentication")
        auth_layout = QHBoxLayout()

        # Authentication status label
        self.authStatusLabel = QLabel("Status: Not Authenticated")
        auth_layout.addWidget(self.authStatusLabel)

        # Add spacer
        auth_layout.addStretch(1)

        # Authentication button
        self.authenticateButton = QPushButton("Authenticate with EVE SSO")
        auth_layout.addWidget(self.authenticateButton)

        # Set the layout for the authentication group
        auth_group.setLayout(auth_layout)

        # Add the authentication group to the scroll layout
        scroll_layout.addWidget(auth_group)

        # Marketing Skills Group
        marketing_group = QGroupBox("Marketing Skills")
        marketing_layout = QVBoxLayout()

        # Add refresh button
        self.refresh_button = QPushButton("Refresh Skills Data")
        marketing_layout.addWidget(self.refresh_button)

        # Create skills table
        self.skills_table = QTableWidget()
        self.skills_table.setColumnCount(2)
        self.skills_table.setHorizontalHeaderLabels(["Skill", "Level"])
        self.skills_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.skills_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        marketing_layout.addWidget(self.skills_table)

        # Add the marketing group to the scroll layout
        marketing_group.setLayout(marketing_layout)
        scroll_layout.addWidget(marketing_group)

        # Tax Rates Group
        tax_group = QGroupBox("Tax Rates")
        tax_layout = QFormLayout()

        # Broker's fee label
        self.broker_fee_label = QLabel("Loading...")
        tax_layout.addRow("Broker's Fee:", self.broker_fee_label)

        # Transaction tax label
        self.transaction_tax_label = QLabel("Loading...")
        tax_layout.addRow("Transaction Tax:", self.transaction_tax_label)

        # Add the tax group to the scroll layout
        tax_group.setLayout(tax_layout)
        scroll_layout.addWidget(tax_group)

        # Profit Calculation Group
        profit_group = QGroupBox("Profit Calculation")
        profit_layout = QVBoxLayout()

        # Add description label
        profit_description = QLabel(
            "Recalculate profits for all transactions to account for broker fees and transaction taxes. "
            "This ensures that all historical profit data correctly reflects market fees."
        )
        profit_description.setWordWrap(True)
        profit_layout.addWidget(profit_description)

        # Add recalculate button
        self.recalculate_profits_button = QPushButton("Recalculate All Profits")
        profit_layout.addWidget(self.recalculate_profits_button)

        # Add progress bar
        self.recalculate_progress_bar = QProgressBar()
        self.recalculate_progress_bar.setVisible(False)
        profit_layout.addWidget(self.recalculate_progress_bar)

        # Add status label
        self.recalculate_status_label = QLabel("")
        profit_layout.addWidget(self.recalculate_status_label)

        # Add the profit group to the scroll layout
        profit_group.setLayout(profit_layout)
        scroll_layout.addWidget(profit_group)

        # Set the scroll content and add to main layout
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

    def _connect_signals(self):
        """Connect signals and slots."""
        # Connect refresh button
        self.refresh_button.clicked.connect(self._on_refresh_clicked)

        # Connect recalculate profits button
        self.recalculate_profits_button.clicked.connect(self._on_recalculate_profits_clicked)

    @pyqtSlot()
    def _on_refresh_clicked(self):
        """Handle refresh button click."""
        self.refresh_skills_data()

    @pyqtSlot()
    def _on_recalculate_profits_clicked(self):
        """Handle recalculate profits button click."""
        if not self.accounting_controller:
            self.recalculate_status_label.setText("Error: No accounting controller available.")
            return

        # Disable the button during recalculation
        self.recalculate_profits_button.setEnabled(False)
        self.recalculate_status_label.setText("Starting profit recalculation...")
        self.recalculate_progress_bar.setValue(0)
        self.recalculate_progress_bar.setVisible(True)

        # Create worker if it doesn't exist
        if not self.recalculation_worker:
            self.recalculation_worker = ProfitRecalculationWorker(self.accounting_controller)
            self.recalculation_worker.moveToThread(self.worker_thread)

            # Connect signals
            self.start_recalculation_signal.connect(self.recalculation_worker.run)
            self.recalculation_worker.progress_updated.connect(self._on_recalculation_progress)
            self.recalculation_worker.recalculation_completed.connect(self._on_recalculation_completed)

            # Start the thread if not running
            if not self.worker_thread.isRunning():
                self.worker_thread.start()

        # Start the recalculation
        self.start_recalculation_signal.emit()

    @pyqtSlot(int, str)
    def _on_recalculation_progress(self, percent: int, message: str):
        """Handle progress updates from the recalculation worker."""
        self.recalculate_progress_bar.setValue(percent)
        self.recalculate_status_label.setText(message)

    @pyqtSlot(int, int)
    def _on_recalculation_completed(self, success_count: int, error_count: int):
        """Handle completion of profit recalculation."""
        self.recalculate_profits_button.setEnabled(True)

        if error_count == 0:
            self.recalculate_status_label.setText(f"Profit recalculation complete. {success_count} transactions updated successfully.")
        else:
            self.recalculate_status_label.setText(f"Profit recalculation complete with errors. {success_count} successful, {error_count} errors.")

        # Hide progress bar after a delay
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(5000, lambda: self.recalculate_progress_bar.setVisible(False))

    def cleanup(self):
        """Clean up resources before application exit."""
        if self.worker_thread.isRunning():
            self.worker_thread.quit()
            self.worker_thread.wait()

    def refresh_skills_data(self):
        """Refresh the skills data from ESI."""
        if not self.esi_client or not self.esi_client.character_id:
            self.broker_fee_label.setText("Error: No authenticated character")
            self.transaction_tax_label.setText("Error: No authenticated character")
            self._populate_skills_table({})
            return

        # Clear the skill cache
        self.skill_cache = {}

        # Fetch skills data
        skills_data = self.esi_client.get_character_skills()
        if not skills_data or 'skills' not in skills_data:
            self.broker_fee_label.setText("Error: Failed to fetch skills data")
            self.transaction_tax_label.setText("Error: Failed to fetch skills data")
            self._populate_skills_table({})
            return

        # Process skills data
        skills_dict = {}
        for skill in skills_data['skills']:
            skill_id = skill.get('skill_id')
            skill_level = skill.get('trained_skill_level', 0)
            skills_dict[skill_id] = skill_level

        # Cache the skills
        self.skill_cache = skills_dict

        # Update the UI
        self._populate_skills_table(skills_dict)
        self._update_tax_rates(skills_dict)

    def _populate_skills_table(self, skills_dict: Dict[int, int]):
        """
        Populate the skills table with the character's marketing skills.

        Args:
            skills_dict: Dictionary mapping skill IDs to skill levels
        """
        # Define the marketing skills to display
        marketing_skills = [
            (ACCOUNTING_SKILL_ID, "Accounting"),
            (BROKER_RELATIONS_SKILL_ID, "Broker Relations"),
            (ADVANCED_BROKER_RELATIONS_SKILL_ID, "Advanced Broker Relations"),
            (MARKETING_SKILL_ID, "Marketing"),
            (RETAIL_SKILL_ID, "Retail"),
            (TRADE_SKILL_ID, "Trade"),
            (WHOLESALE_SKILL_ID, "Wholesale"),
            (TYCOON_SKILL_ID, "Tycoon"),
            (PROCUREMENT_SKILL_ID, "Procurement"),
            (DAYTRADING_SKILL_ID, "Daytrading"),
            (VISIBILITY_SKILL_ID, "Visibility")
        ]

        # Clear the table
        self.skills_table.setRowCount(0)

        # Add rows for each skill
        for i, (skill_id, skill_name) in enumerate(marketing_skills):
            self.skills_table.insertRow(i)

            # Skill name
            name_item = QTableWidgetItem(skill_name)
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)  # Make read-only
            self.skills_table.setItem(i, 0, name_item)

            # Skill level
            level = skills_dict.get(skill_id, 0)
            level_item = QTableWidgetItem(str(level))
            level_item.setFlags(level_item.flags() & ~Qt.ItemIsEditable)  # Make read-only

            # Set consistent dark gray background for all skill levels
            level_item.setBackground(QColor(60, 60, 60))  # Dark gray background

            self.skills_table.setItem(i, 1, level_item)

    def _update_tax_rates(self, skills_dict: Dict[int, int]):
        """
        Update the tax rate displays based on character skills.

        Args:
            skills_dict: Dictionary mapping skill IDs to skill levels
        """
        # Get skill levels
        accounting_level = skills_dict.get(ACCOUNTING_SKILL_ID, 0)
        broker_relations_level = skills_dict.get(BROKER_RELATIONS_SKILL_ID, 0)

        # Calculate transaction tax
        # Formula: final_tax = base_tax * (1 - (0.11 * accounting_level))
        tax_reduction = Decimal('0.11') * Decimal(accounting_level)
        effective_tax_rate = BASE_SALES_TAX_RATE * (Decimal('1') - tax_reduction)

        # Calculate broker fee
        # Formula: broker_fee = 3% - (0.3% * broker_relations_level)
        # Note: This is simplified and doesn't account for standings
        fee_reduction = Decimal('0.003') * Decimal(broker_relations_level)
        effective_fee_rate = BASE_BROKER_FEE_RATE - fee_reduction

        # Update the labels
        self.transaction_tax_label.setText(f"{effective_tax_rate:.2%}")
        self.broker_fee_label.setText(f"{effective_fee_rate:.2%}")
