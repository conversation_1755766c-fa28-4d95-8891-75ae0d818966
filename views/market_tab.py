"""
Market Tab

This module implements the Market tab for the EVE Trader application.
It provides item search with autocomplete and displays market data for selected items.
"""

import os
import math
from decimal import Decimal
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone

from PyQt5 import uic
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QSortFilterProxyModel, QStringListModel, QEvent, QMargins
from PyQt5.QtWidgets import (
    QWidget, QComboBox, QCompleter, QLabel, QTableView,
    QProgressBar, QPushButton, QMessageBox, QApplication, QLineEdit, QTextEdit, QGroupBox,
    QVBoxLayout, QScrollArea, QSizePolicy, QMenu, QAction
)
from PyQt5.QtGui import QKeyEvent, QColor, QPainter, QFont, QPen
from PyQt5.QtChart import (
    QChart, QChartView, QBarSeries, QBarSet, QValueAxis, QBarCategoryAxis,
    QHorizontalBarSeries, QLineSeries, QSplineSeries, QAreaSeries
)

from utils.item_database import get_item_database
from models.database import DatabaseManager
from controllers.data_sync_controller import DataSyncController
from controllers.market_analyzer import analyze_item_market # Import the analyzer
from views.table_models import TransactionsTableModel
from api.esi_client import ESIClient, JITA_REGION_ID, JITA_STATION_ID
from utils.position_sizing import calculate_recommended_quantity

# Get the absolute path to the UI file
current_dir = os.path.dirname(os.path.abspath(__file__))
ui_file_path = os.path.join(current_dir, 'ui', 'market_tab.ui')


class CustomCompleter(QCompleter):
    """
    Custom completer that changes how arrow keys interact with the autocomplete list.
    Instead of replacing the text when down arrow is pressed, it highlights the selection
    and only replaces the text when Enter is pressed.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setCompletionMode(QCompleter.PopupCompletion)
        self.highlighted.connect(self._on_highlighted)
        self.current_text = ""
        self.popup_visible = False
        self.highlighted_text = ""

    def _on_highlighted(self, text):
        """Store the highlighted text without changing the line edit text."""
        self.highlighted_text = text

    def eventFilter(self, obj, event):
        """Filter events to customize arrow key behavior."""
        if event.type() == QEvent.KeyPress:
            key_event = QKeyEvent(event)
            key = key_event.key()

            # Handle Enter key to select the highlighted item
            if key in (Qt.Key_Enter, Qt.Key_Return) and self.popup_visible and self.highlighted_text:
                if obj.metaObject().className() == 'QLineEdit':
                    obj.setText(self.highlighted_text)
                    self.popup_visible = False
                    return True

        return super().eventFilter(obj, event)

    def setWidget(self, widget):
        """Override to install event filter on the widget."""
        if self.widget():
            self.widget().removeEventFilter(self)
        super().setWidget(widget)
        if widget:
            widget.installEventFilter(self)

    def complete(self, rect=None):
        """Override to track when the popup is visible."""
        self.popup_visible = True
        if rect:
            super().complete(rect)
        else:
            super().complete()


from PyQt5.QtCore import pyqtSignal

class MarketTab(QWidget):
    """
    Market tab widget that provides item search and market data display.
    """
    # Signal to notify main window of data sync request
    sync_requested = pyqtSignal()
    # New signal to notify item selected for analysis
    item_selected_for_analysis = pyqtSignal(int)

    def __init__(self, db_manager: DatabaseManager, data_sync_controller: DataSyncController, parent=None):
        """
        Initialize the Market tab.

        Args:
            db_manager: Database manager instance
            data_sync_controller: Data sync controller instance
            parent: Parent widget
        """
        super().__init__(parent)
        self.db = db_manager
        self.data_sync_controller = data_sync_controller
        self.item_db = get_item_database()
        self.current_item_id = None
        self.current_item_market_orders: Optional[Dict[str, List[Dict[str, Any]]]] = None
        self.current_item_historical_data: Optional[List[Dict[str, Any]]] = None
        self.current_item_name: Optional[str] = None


        # Initialize ESI client
        self.esi = ESIClient()

        # Load the UI
        uic.loadUi(ui_file_path, self)

        # Initialize UI components
        self._init_ui()

        # Connect signals
        self._connect_signals()

    def _init_ui(self):
        """Initialize UI components."""
        # Get references to UI elements
        self.item_search_combo = self.findChild(QComboBox, 'itemSearchComboBox')
        self.update_data_button = self.findChild(QPushButton, 'updateDataButton')
        self.fetch_button = self.findChild(QPushButton, 'fetchButton')
        self.add_to_execute_button = self.findChild(QPushButton, 'addToExecuteButton')
        self.sync_progress_bar = self.findChild(QProgressBar, 'syncProgressBar')
        self.sync_status_label = self.findChild(QLabel, 'syncStatusLabel')

        # Item details labels
        self.item_name_label = self.findChild(QLabel, 'itemNameLabel')
        self.sell_price_value_label = self.findChild(QLabel, 'sellPriceValueLabel')
        self.buy_price_value_label = self.findChild(QLabel, 'buyPriceValueLabel')
        self.spread_value_label = self.findChild(QLabel, 'spreadValueLabel')

        # Volume information labels
        self.daily_volume_value_label = self.findChild(QLabel, 'dailyVolumeValueLabel')
        self.historical_volume_value_label = self.findChild(QLabel, 'historicalVolumeValueLabel')
        self.avg_volume_value_label = self.findChild(QLabel, 'avgVolumeValueLabel')
        self.fill_rate_value_label = self.findChild(QLabel, 'fillRateValueLabel')
        self.recommended_qty_value_label = self.findChild(QLabel, 'recommendedQtyValueLabel')

        # Total profit label
        self.total_profit_value_label = self.findChild(QLabel, 'totalProfitValueLabel')

        # Transactions table
        self.transactions_table_view = self.findChild(QTableView, 'transactionsTableView')
        # Set up context menu for transactions table
        self._setup_transactions_context_menu()

        # Chart widget
        self.chart_widget = self.findChild(QWidget, 'chartWidget')
        self._setup_chart()

        # Analysis UI elements
        self.analyze_market_button = self.findChild(QPushButton, 'analyzeMarketButton')
        if self.analyze_market_button: # Check if found before trying to set text
            self.analyze_market_button.setText("Scan for Issues") # Renamed
        self.analysis_results_group_box = self.findChild(QGroupBox, 'analysisResultsGroupBox')
        self.analysis_results_text_edit = self.findChild(QTextEdit, 'analysisResultsTextEdit')

        # Ensure these are found, otherwise critical error
        if not all([self.analyze_market_button, self.analysis_results_group_box, self.analysis_results_text_edit]):
            print("CRITICAL ERROR: Analysis UI elements not found in market_tab.ui. Check object names.")
            # Optionally, raise an exception or show a QMessageBox to the user
            # For now, we'll let it proceed, but it will likely crash if elements are missing.
        else:
            self.analyze_market_button.setVisible(False)
            self.analysis_results_group_box.setVisible(False)

        # Setup item search autocomplete
        self._setup_item_search_autocomplete()

    def _setup_item_search_autocomplete(self):
        """Setup the item search autocomplete functionality."""
        # Create a custom completer
        self.completer = CustomCompleter(self)
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.completer.setFilterMode(Qt.MatchContains)

        # Set the completer on the combo box
        self.item_search_combo.setCompleter(self.completer)

        # Get the line edit from the combo box
        line_edit = self.item_search_combo.lineEdit()
        if line_edit:
            # Connect the return pressed signal to handle item selection
            line_edit.returnPressed.connect(self._on_search_return_pressed)

        # Load initial item list (top 1000 items by popularity or alphabetically)
        self._load_initial_item_list()

    def _load_initial_item_list(self):
        """Load the initial list of items for the combo box."""
        try:
            # Get a list of items from the database
            # For now, just get the first 1000 items alphabetically
            items = self.item_db.search_items("", limit=1000)

            # Create a list of item names
            item_names = [item['item_name'] for item in items]

            # Create a model for the completer
            self.item_model = QStringListModel(item_names)
            self.completer.setModel(self.item_model)

            # Store a mapping of item names to IDs for quick lookup
            self.item_name_to_id = {item['item_name']: item['item_id'] for item in items}

            print(f"Loaded {len(item_names)} items for autocomplete")
        except Exception as e:
            print(f"Error loading items for autocomplete: {e}")
            import traceback
            print(traceback.format_exc())

    def _connect_signals(self):
        """Connect signals and slots."""
        # Connect the item search combo box
        self.item_search_combo.currentTextChanged.connect(self._on_search_text_changed)
        self.item_search_combo.activated.connect(self._on_item_selected)

        # Connect the update data button
        self.update_data_button.clicked.connect(self._on_update_data_clicked)

        # Connect the fetch button - it does the same as pressing Enter in the search box
        self.fetch_button.clicked.connect(self._on_search_return_pressed)

        # Connect the add to execute button
        self.add_to_execute_button.clicked.connect(self._on_add_to_execute_clicked)

        # We'll use the existing update data button for both general sync and market data refresh

        if self.analyze_market_button:
            self.analyze_market_button.clicked.connect(self._on_analyze_market_clicked)

    @pyqtSlot(str)
    def _on_search_text_changed(self, text: str):
        """
        Handle changes to the search text.

        Args:
            text: The current text in the search box
        """
        if len(text) < 2:
            return

        # Search for items matching the text
        items = self.item_db.search_items(text, limit=20)

        # Update the completer model
        item_names = [item['item_name'] for item in items]
        self.item_model.setStringList(item_names)

        # Update the name to ID mapping
        for item in items:
            self.item_name_to_id[item['item_name']] = item['item_id']

    @pyqtSlot()
    def _on_search_return_pressed(self):
        """
        Handle return/enter key press in the search box.
        This will load the details for the current text or highlighted item.
        """
        # If there's a highlighted item in the completer, use that
        if self.completer.highlighted_text:
            item_name = self.completer.highlighted_text
            self.item_search_combo.setCurrentText(item_name)
        else:
            # Otherwise use the current text
            item_name = self.item_search_combo.currentText()

        # Look up the item ID and load details
        self._load_item_by_name(item_name)

    def _load_item_by_name(self, item_name: str):
        """
        Load item details by name.

        Args:
            item_name: The name of the item to load
        """
        if item_name in self.item_name_to_id:
            item_id = self.item_name_to_id[item_name]
            self._load_item_details(item_id)
        else:
            # Try to search for the item
            items = self.item_db.search_items(item_name, limit=1)
            if items:
                item_id = items[0]['item_id']
                self._load_item_details(item_id)

    @pyqtSlot(int)
    def _on_item_selected(self, index: int):
        """
        Handle item selection from the combo box.

        Args:
            index: The index of the selected item
        """
        # Get the selected item name
        item_name = self.item_search_combo.currentText()

        # Load the item details
        self._load_item_by_name(item_name)

        # Emit signal for item selected for analysis
        if item_name in self.item_name_to_id:
            item_id = self.item_name_to_id[item_name]
            self.item_selected_for_analysis.emit(item_id)

    def _load_item_details(self, item_id: int):
        """
        Load and display details for the selected item.

        Args:
            item_id: The ID of the selected item
        """
        print(f"Loading details for item ID: {item_id}")
        self.current_item_id = item_id
        self.current_item_market_orders = None
        self.current_item_historical_data = None
        self.current_item_name = None

        # Hide analysis button and results until data is loaded
        if self.analyze_market_button:
            self.analyze_market_button.setVisible(False)
        if self.analysis_results_group_box:
            self.analysis_results_group_box.setVisible(False)
        if self.analysis_results_text_edit:
            self.analysis_results_text_edit.clear()

        # Disable the add to execute button until market data is loaded
        self.add_to_execute_button.setEnabled(False)

        # Get item details from the database
        item = self.item_db.get_item_details(item_id)
        if not item:
            print(f"Item not found: {item_id}")
            return

        self.current_item_name = item['item_name']
        # Update the item name label
        self.item_name_label.setText(self.current_item_name)

        # Reset total profit display
        self.total_profit_value_label.setText("-")
        self.total_profit_value_label.setStyleSheet("")

        # Load market data - always fetch live data when an item is selected
        self._load_market_data(item_id, force_live=True)

        # Load transaction history
        self._load_transaction_history(item_id)

    def _load_market_data(self, item_id: int, force_live: bool = False):
        """
        Load market data for the selected item.

        Args:
            item_id: The ID of the selected item
            force_live: If True, always fetch from ESI even if recent data exists
        """
        try:
            # Set UI to loading state
            self.sell_price_value_label.setText("Loading...")
            self.buy_price_value_label.setText("Loading...")
            self.spread_value_label.setText("Loading...")
            self.daily_volume_value_label.setText("Loading...")

            # Process UI events to show loading state
            QApplication.processEvents()

            # If not forcing live data, check if we have recent data (less than 5 minutes old)
            if not force_live:
                recent_data = self._get_recent_market_data(item_id)
                if recent_data:
                    self._update_ui_with_market_data(
                        recent_data['buy_price'],
                        recent_data['sell_price'],
                        recent_data['daily_volume']
                    )
                    print(f"Using recent market data from database for item {item_id}")
                    return

            # Fetch live market data from ESI
            jita_buy_orders, jita_sell_orders = self._fetch_market_orders(item_id)
            self.current_item_market_orders = {'buy': jita_buy_orders, 'sell': jita_sell_orders}


            if jita_buy_orders and jita_sell_orders:
                # Calculate best prices
                best_buy_price = max(order['price'] for order in jita_buy_orders)
                best_sell_price = min(order['price'] for order in jita_sell_orders)

                # Calculate daily volume (estimate based on order volumes)
                buy_volume = sum(order['volume_remain'] for order in jita_buy_orders)
                sell_volume = sum(order['volume_remain'] for order in jita_sell_orders)
                daily_volume = min(buy_volume, sell_volume)  # Conservative estimate

                # Update the UI with live market data
                self._update_ui_with_market_data(best_buy_price, best_sell_price, daily_volume)
                print(f"Using live market data from ESI for item {item_id}")

                # Update the chart with market orders
                self._update_chart_with_market_orders(jita_buy_orders, jita_sell_orders)

                # Store the updated market data in the database
                self._save_market_data(item_id, best_buy_price, best_sell_price, daily_volume)

                # Fetch historical data
                print(f"Fetching historical market data for item {item_id}...")
                self.current_item_historical_data = self.esi.get_market_history(
                    region_id=JITA_REGION_ID,
                    item_id=item_id,
                    force_refresh=True # Always get fresh history for analysis
                )
                if self.current_item_historical_data:
                    print(f"Successfully fetched {len(self.current_item_historical_data)} days of history for item {item_id}.")
                    if self.analyze_market_button:
                        self.analyze_market_button.setVisible(True)

                    # Enable the add to execute button since we have market data
                    self.add_to_execute_button.setEnabled(True)

                    # Update volume metrics with the historical data
                    self._update_volume_metrics()
                else:
                    print(f"Failed to fetch historical market data for item {item_id}.")
                    # self.current_item_historical_data will remain None
                    # Analysis button will not be shown if it depends on this.

            else:
                # Fallback to database if ESI fetch fails
                print("ESI market order fetch failed, falling back to database for current prices")
                self.current_item_market_orders = None # Ensure it's reset
                self._load_market_data_from_db(item_id)
        except Exception as e:
            print(f"Error loading market data: {e}")
            import traceback
            print(traceback.format_exc())

            # Fallback to database if there's an error
            try:
                if not self._load_market_data_from_db(item_id):
                    # Set default values if both live and DB fetch fail
                    self.sell_price_value_label.setText("Error")
                    self.buy_price_value_label.setText("Error")
                    self.spread_value_label.setText("Error")
                    self.daily_volume_value_label.setText("Error")

                    # Clear the chart on error
                    self.chart.removeAllSeries()

                    # Remove old axes if they exist
                    for axis in self.chart.axes():
                        self.chart.removeAxis(axis)

                    self.chart.setTitle("Order Book Depth - Error loading data")
            except Exception as db_error:
                print(f"Error loading market data from database: {db_error}")
                # Set default values if both live and DB fetch fail
                self.sell_price_value_label.setText("Error")
                self.buy_price_value_label.setText("Error")
                self.spread_value_label.setText("Error")
                self.daily_volume_value_label.setText("Error")

                # Clear the chart on error
                self.chart.removeAllSeries()

                # Remove old axes if they exist
                for axis in self.chart.axes():
                    self.chart.removeAxis(axis)

                self.chart.setTitle("Order Book Depth - Error loading data")

    def _update_ui_with_market_data(self, buy_price: float, sell_price: float, daily_volume: int):
        """
        Update the UI with market data.

        Args:
            buy_price: The best buy price
            sell_price: The best sell price
            daily_volume: The estimated daily volume (current volume from orders)
        """
        self.sell_price_value_label.setText(f"{float(sell_price):,.2f} ISK")
        self.buy_price_value_label.setText(f"{float(buy_price):,.2f} ISK")

        # Calculate spread
        if sell_price > 0:
            spread_pct = (sell_price - buy_price) / sell_price * 100
            self.spread_value_label.setText(f"{spread_pct:.2f}%")
        else:
            self.spread_value_label.setText("N/A")

        # Current Volume (from orders)
        self.daily_volume_value_label.setText(f"{int(daily_volume):,}")

        # Reset other volume fields until we have historical data
        self.historical_volume_value_label.setText("-")
        self.avg_volume_value_label.setText("-")
        self.fill_rate_value_label.setText("-")
        self.recommended_qty_value_label.setText("-")

        # If we have historical data and market orders, update the additional volume fields
        if self.current_item_historical_data and self.current_item_market_orders:
            self._update_volume_metrics()

    def _update_volume_metrics(self):
        """Update the additional volume metrics from historical data and market orders."""
        from controllers.market_analyzer import _get_historical_average, _get_most_recent_completed_day_data

        if not self.current_item_historical_data or not self.current_item_market_orders:
            return

        # Get historical daily volume from the most recent completed day
        most_recent_day = _get_most_recent_completed_day_data(self.current_item_historical_data)
        if most_recent_day and 'volume' in most_recent_day:
            historical_volume = most_recent_day['volume']
            self.historical_volume_value_label.setText(f"{int(historical_volume):,}")
        else:
            self.historical_volume_value_label.setText("No data")

        # Get 7-day average volume
        avg_volume_7d = _get_historical_average(self.current_item_historical_data, 'volume', 7)
        if avg_volume_7d is not None:
            self.avg_volume_value_label.setText(f"{int(avg_volume_7d):,}")
        else:
            self.avg_volume_value_label.setText("No data")

        # Calculate fill rate
        fill_rate = None
        if most_recent_day and 'volume' in most_recent_day:
            historical_volume = most_recent_day['volume']
            total_sell_order_quantity = sum(o.get('volume_remain', 0) for o in self.current_item_market_orders.get('sell', []))

            if total_sell_order_quantity > 0:
                fill_rate = historical_volume / total_sell_order_quantity
                self.fill_rate_value_label.setText(f"{fill_rate:.2f}")
            else:
                self.fill_rate_value_label.setText("N/A")

        # Calculate recommended purchase quantity
        try:
            # Get the best buy price
            buy_orders = self.current_item_market_orders.get('buy', [])
            if buy_orders:
                best_buy_price = Decimal(str(max(order['price'] for order in buy_orders)))

                # Get daily volume - use historical if available, otherwise use current
                daily_volume = None
                if most_recent_day and 'volume' in most_recent_day:
                    daily_volume = int(most_recent_day['volume'])
                else:
                    # Fallback to current volume estimate
                    buy_volume = sum(order['volume_remain'] for order in buy_orders)
                    sell_volume = sum(order['volume_remain'] for order in self.current_item_market_orders.get('sell', []))
                    daily_volume = min(buy_volume, sell_volume)

                # Calculate recommended quantity
                recommended_qty = calculate_recommended_quantity(
                    buy_price=best_buy_price,
                    daily_volume=daily_volume,
                    fill_rate=Decimal(str(fill_rate)) if fill_rate is not None else None,
                    historical_data=self.current_item_historical_data
                )

                # Update the UI
                self.recommended_qty_value_label.setText(f"{recommended_qty:,}")
            else:
                self.recommended_qty_value_label.setText("N/A")
        except Exception as e:
            print(f"Error calculating recommended quantity: {e}")
            self.recommended_qty_value_label.setText("Error")

    def _get_recent_market_data(self, item_id: int) -> Optional[Dict[str, Any]]:
        """
        Get recent market data from the database if available.
        Specifically looks for JITA station data.

        Args:
            item_id: The ID of the item

        Returns:
            Recent market data or None if not available
        """
        try:
            # Get market data from the last 5 minutes for JITA station
            query = """
            SELECT * FROM market_data
            WHERE item_id = ? AND location_id = ? AND timestamp > datetime('now', '-5 minutes')
            ORDER BY timestamp DESC
            LIMIT 1
            """
            recent_data = self.db.execute_query(query, (item_id, JITA_STATION_ID), fetch_one=True)

            # If no data with location_id, try the old format (without location filtering)
            if not recent_data:
                query = """
                SELECT * FROM market_data
                WHERE item_id = ? AND timestamp > datetime('now', '-5 minutes')
                ORDER BY timestamp DESC
                LIMIT 1
                """
                recent_data = self.db.execute_query(query, (item_id,), fetch_one=True)

            if recent_data:
                print(f"Found recent market data for item {item_id} at JITA station")
                return recent_data

            return None
        except Exception as e:
            print(f"Error getting recent market data: {e}")
            return None

    def _fetch_market_orders(self, item_id: int) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Fetch market orders for an item from ESI.

        Args:
            item_id: The ID of the item to fetch orders for

        Returns:
            Tuple of (buy_orders, sell_orders) filtered to only include JITA station
        """
        # Get buy orders
        buy_orders = self.esi.get_market_orders(
            region_id=JITA_REGION_ID,
            item_id=item_id,
            order_type='buy'
        ) or []

        # Get sell orders
        sell_orders = self.esi.get_market_orders(
            region_id=JITA_REGION_ID,
            item_id=item_id,
            order_type='sell'
        ) or []

        # Filter orders to only include those from JITA station
        jita_buy_orders = [order for order in buy_orders if order.get('location_id') == JITA_STATION_ID]
        jita_sell_orders = [order for order in sell_orders if order.get('location_id') == JITA_STATION_ID]

        return jita_buy_orders, jita_sell_orders

    def _save_market_data(self, item_id: int, buy_price: float, sell_price: float, daily_volume: int):
        """
        Save market data to the database.
        Note: This data is specifically for JITA station, not the entire region.

        Args:
            item_id: The ID of the item
            buy_price: The best buy price at JITA station
            sell_price: The best sell price at JITA station
            daily_volume: The estimated daily volume at JITA station
        """
        try:
            # Insert new market data record
            query = """
            INSERT INTO market_data (
                item_id, region_id, sell_price, buy_price, daily_volume, timestamp, location_id
            ) VALUES (?, ?, ?, ?, ?, datetime('now'), ?)
            """
            self.db.execute_update(query, (
                item_id,
                JITA_REGION_ID,
                sell_price,
                buy_price,
                daily_volume,
                JITA_STATION_ID  # Store the specific location ID
            ))
            print(f"Saved JITA station market data for item {item_id}")
            return True
        except Exception as e:
            print(f"Error saving market data: {e}")
            import traceback
            print(traceback.format_exc())
            return False

    def _load_market_data_from_db(self, item_id: int) -> bool:
        """
        Load market data from the database as a fallback.
        Specifically loads data for JITA station.

        Args:
            item_id: The ID of the selected item

        Returns:
            True if data was found and loaded, False otherwise
        """
        # Query the database for market data using the latest_market_data view
        # Filter by both item_id and location_id (JITA station)
        query = """
        SELECT * FROM latest_market_data
        WHERE item_id = ? AND location_id = ?
        """
        market_data = self.db.execute_query(query, (item_id, JITA_STATION_ID), fetch_one=True)

        # If no data with location_id, try the old format (without location filtering)
        if not market_data:
            query = """
            SELECT * FROM latest_market_data
            WHERE item_id = ?
            """
            market_data = self.db.execute_query(query, (item_id,), fetch_one=True)

        if market_data:
            # Update the UI with market data
            self._update_ui_with_market_data(
                float(market_data['buy_price']),
                float(market_data['sell_price']),
                int(market_data['daily_volume'])
            )

            # Clear the chart since we don't have order data from the database
            self.chart.removeAllSeries()

            # Remove old axes if they exist
            for axis in self.chart.axes():
                self.chart.removeAxis(axis)

            self.chart.setTitle("Order Book Depth - No detailed order data available")

            return True
        else:
            # No market data available
            self.sell_price_value_label.setText("No data")
            self.buy_price_value_label.setText("No data")
            self.spread_value_label.setText("No data")
            self.daily_volume_value_label.setText("No data")

            # Clear the chart since we don't have any data
            self.chart.removeAllSeries()

            # Remove old axes if they exist
            for axis in self.chart.axes():
                self.chart.removeAxis(axis)

            self.chart.setTitle("Order Book Depth - No data available")

            return False

    def _calculate_total_profit(self, item_id: int) -> Decimal:
        """
        Calculate the total profit for an item across all transactions.

        Args:
            item_id: The ID of the item

        Returns:
            The total profit as a Decimal
        """
        try:
            # Query the database for all transactions with profit data
            query = """
            SELECT SUM(profit) as total_profit
            FROM transactions
            WHERE item_id = ? AND profit IS NOT NULL
            """
            result = self.db.execute_query(query, (item_id,), fetch_one=True)

            if result and result['total_profit'] is not None:
                return Decimal(str(result['total_profit']))
            return Decimal('0')
        except Exception as e:
            print(f"Error calculating total profit: {e}")
            import traceback
            print(traceback.format_exc())
            return Decimal('0')

    def _load_transaction_history(self, item_id: int):
        """
        Load transaction history for the selected item.

        Args:
            item_id: The ID of the selected item
        """
        try:
            # Query the database for transactions
            query = """
            SELECT tx_id, timestamp, item_id, item_name, quantity, unit_price, total_price,
                   transaction_type, net_amount, strategy, profit
            FROM transactions
            WHERE item_id = ?
            ORDER BY timestamp DESC
            LIMIT 100
            """
            transactions = self.db.execute_query(query, (item_id,), fetch_all=True)

            if transactions:
                # Create a table model and set it on the view
                model = TransactionsTableModel(transactions)
                self.transactions_table_view.setModel(model)

                # Adjust column widths for better display
                self.transactions_table_view.resizeColumnsToContents()

                # Calculate and display total profit
                total_profit = self._calculate_total_profit(item_id)
                self._update_total_profit_display(total_profit)
            else:
                # No transactions available
                self.transactions_table_view.setModel(None)
                self._update_total_profit_display(Decimal('0'))
        except Exception as e:
            print(f"Error loading transaction history: {e}")
            import traceback
            print(traceback.format_exc())

            # Reset total profit display on error
            self.total_profit_value_label.setText("Error")
            self.total_profit_value_label.setStyleSheet("color: red;")

    def _update_total_profit_display(self, total_profit: Decimal):
        """
        Update the total profit display with the calculated value.

        Args:
            total_profit: The total profit to display
        """
        # Format the profit with commas for thousands and color based on value
        if total_profit > 0:
            self.total_profit_value_label.setText(f"{total_profit:,.2f} ISK")
            self.total_profit_value_label.setStyleSheet("color: green;")
        elif total_profit < 0:
            self.total_profit_value_label.setText(f"{total_profit:,.2f} ISK")
            self.total_profit_value_label.setStyleSheet("color: red;")
        else:
            self.total_profit_value_label.setText("0.00 ISK")
            self.total_profit_value_label.setStyleSheet("")

    @pyqtSlot()
    def _on_add_to_execute_clicked(self):
        """Handle add to execute button click."""
        if not self.current_item_id or not self.current_item_market_orders:
            QMessageBox.warning(self, "Error", "No valid item data available to add to execution.")
            return

        # Get the main window instance
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'execute_tab'):
                main_window = parent
                break
            parent = parent.parent()

        if not main_window or not hasattr(main_window, 'execute_tab'):
            QMessageBox.critical(self, "Error", "Could not access the execute tab.")
            return

        # Prepare the item data to add to the execute tab
        buy_orders = self.current_item_market_orders.get('buy', [])
        sell_orders = self.current_item_market_orders.get('sell', [])

        if not buy_orders or not sell_orders:
            QMessageBox.warning(self, "Error", "Market data is incomplete. Cannot add to execution.")
            return

        # Calculate best prices
        best_buy_price = max(order['price'] for order in buy_orders)
        best_sell_price = min(order['price'] for order in sell_orders)

        # Calculate daily volume
        daily_volume = 0
        if self.current_item_historical_data:
            from controllers.market_analyzer import _get_most_recent_completed_day_data
            most_recent_day = _get_most_recent_completed_day_data(self.current_item_historical_data)
            if most_recent_day and 'volume' in most_recent_day:
                daily_volume = most_recent_day['volume']

        if daily_volume == 0:
            # Fallback to current volume estimate
            buy_volume = sum(order['volume_remain'] for order in buy_orders)
            sell_volume = sum(order['volume_remain'] for order in sell_orders)
            daily_volume = min(buy_volume, sell_volume)

        # Calculate margin percent
        margin_percent = 0
        if best_buy_price > 0:
            margin_percent = (best_sell_price - best_buy_price) / best_buy_price * 100

        # Calculate expected profit per unit
        expected_profit = best_sell_price - best_buy_price

        # Get recommended quantity
        recommended_qty = 0
        if hasattr(self, 'recommended_qty_value_label'):
            try:
                # Try to parse the recommended quantity from the label
                text = self.recommended_qty_value_label.text().replace(',', '')
                if text and text != '-' and text != 'N/A' and text != 'Error':
                    recommended_qty = int(text)
            except (ValueError, TypeError):
                # If parsing fails, calculate it
                recommended_qty = calculate_recommended_quantity(
                    buy_price=Decimal(str(best_buy_price)),
                    daily_volume=daily_volume
                )

        # Create the item data dictionary
        item_data = {
            'item_id': self.current_item_id,
            'item_name': self.current_item_name,
            'best_buy_price': best_buy_price,
            'best_sell_price': best_sell_price,
            'daily_volume': daily_volume,
            'margin_percent': margin_percent,
            'expected_profit': expected_profit,
            'recommended_quantity': recommended_qty
        }

        # Add the item to the execute tab
        main_window.execute_tab.add_item(item_data)

    @pyqtSlot()
    def _on_update_data_clicked(self):
        """Handle update data button click."""
        # If we have a current item selected, refresh its market data
        if self.current_item_id:
            self._load_market_data(self.current_item_id, force_live=True)

        # Emit the sync requested signal for general data sync
        self.sync_requested.emit()

    def update_sync_progress(self, value: int, message: str):
        """
        Update the sync progress bar and status label.

        Args:
            value: Progress value (0-100)
            message: Status message
        """
        self.sync_progress_bar.setValue(value)
        self.sync_status_label.setText(message)

        # Show/hide progress bar based on progress
        self.sync_progress_bar.setVisible(value < 100)

        # Re-enable the update button when complete
        if value >= 100:
            self.update_data_button.setEnabled(True)
        else:
            self.update_data_button.setEnabled(False)

    def _setup_chart(self):
        """Initialize the chart for displaying order book depth."""
        # Create a chart view
        self.chart_view = QChartView()
        self.chart_view.setRenderHint(QPainter.Antialiasing)

        # Create a chart
        self.chart = QChart()
        self.chart.setTitle("Order Book Depth")
        self.chart.setAnimationOptions(QChart.SeriesAnimations)
        self.chart.setTheme(QChart.ChartThemeDark)  # Use dark theme

        # Create a small font for all chart elements
        small_font = QFont()
        small_font.setPointSize(7)  # Reduced font size
        self.chart.setTitleFont(small_font)

        # Set the chart on the chart view
        self.chart_view.setChart(self.chart)

        # Add the chart view to the chart widget
        layout = QVBoxLayout(self.chart_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.chart_view)

        # Set the chart widget to expand
        self.chart_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Set the chart to use the full available space
        self.chart.setBackgroundVisible(False)
        self.chart.setMargins(QMargins(10, 10, 10, 10))
        self.chart.layout().setContentsMargins(0, 0, 0, 0)

        # Add legend with small font
        self.chart.legend().setVisible(True)
        self.chart.legend().setAlignment(Qt.AlignBottom)
        self.chart.legend().setFont(small_font)

    @pyqtSlot()
    def _on_analyze_market_clicked(self):
        """Handle analyze market button click."""
        if not self.current_item_id or not self.current_item_market_orders or not self.current_item_historical_data:
            QMessageBox.warning(self, "Analysis Error", "Market data or historical data is not fully loaded. Cannot perform analysis.")
            return

        print(f"Analyzing market patterns for item: {self.current_item_name} (ID: {self.current_item_id})")

        # Make sure volume metrics are updated
        self._update_volume_metrics()

        analysis_tags = analyze_item_market(
            current_orders=self.current_item_market_orders,
            historical_data=self.current_item_historical_data,
            item_id=self.current_item_id,
            item_name=self.current_item_name
        )

        if self.analysis_results_text_edit and self.analysis_results_group_box:
            self.analysis_results_text_edit.clear() # Clear previous results
            if analysis_tags:
                output_text = []
                for item in analysis_tags:
                    if isinstance(item, dict) and "tag" in item and "explanation" in item:
                        output_text.append(f"Tag: {item['tag']}")
                        output_text.append(f"Explanation: {item['explanation']}\n") # Add extra newline for spacing
                    else:
                        # Fallback for unexpected format, though analyzer should always return dicts
                        output_text.append(str(item))

                # Add a note about the current item's market conditions
                # Get the fill rate and volume data
                fill_rate = None
                most_recent_day = None
                daily_volume_today = None
                avg_volume_7d = None
                from controllers.market_analyzer import _get_most_recent_completed_day_data, _get_historical_average, LOW_FILL_RATE_THRESHOLD, LOW_VOLUME_RELATIVE_THRESHOLD

                if self.current_item_historical_data:
                    most_recent_day = _get_most_recent_completed_day_data(self.current_item_historical_data)
                    avg_volume_7d = _get_historical_average(self.current_item_historical_data, 'volume', 7)

                    if most_recent_day and 'volume' in most_recent_day:
                        daily_volume_today = most_recent_day['volume']

                if most_recent_day and 'volume' in most_recent_day:
                    historical_volume = most_recent_day['volume']
                    total_sell_order_quantity = sum(o.get('volume_remain', 0) for o in self.current_item_market_orders.get('sell', []))
                    if total_sell_order_quantity > 0:
                        fill_rate = historical_volume / total_sell_order_quantity

                # Get the spread
                highest_buy_price = max([o.get('price', 0) for o in self.current_item_market_orders.get('buy', [])]) if self.current_item_market_orders.get('buy', []) else 0
                lowest_sell_price = min([o.get('price', 0) for o in self.current_item_market_orders.get('sell', [])]) if self.current_item_market_orders.get('sell', []) else 0
                spread_buy_based = None
                if highest_buy_price > 0:
                    spread_buy_based = (lowest_sell_price - highest_buy_price) / highest_buy_price

                # Add a note about the conditions for low turnover trap
                output_text.append("\nNote about Low Turnover Trap conditions:")
                output_text.append("An item is flagged as a 'Low turnover trap' if it meets EITHER of these conditions:")
                output_text.append(f"1. Low fill rate (< {LOW_FILL_RATE_THRESHOLD})")
                output_text.append(f"2. OR low volume (< {LOW_VOLUME_RELATIVE_THRESHOLD*100:.0f}% of 7-day average)")

                if fill_rate is not None and spread_buy_based is not None:
                    output_text.append(f"\nFor this item ({self.current_item_name}):")
                    output_text.append(f"- Fill rate: {fill_rate:.2f} (threshold: {LOW_FILL_RATE_THRESHOLD})")
                    output_text.append(f"- Spread: {spread_buy_based:.2f} (for information only)")

                    if fill_rate < LOW_FILL_RATE_THRESHOLD:
                        output_text.append("- Low fill rate condition is MET")
                        output_text.append("\nThis item should be flagged as a 'Low turnover trap' due to its low fill rate.")
                    else:
                        output_text.append("- Low fill rate condition is NOT met")

                    # Add volume information if available
                    if daily_volume_today is not None and avg_volume_7d and avg_volume_7d > 0:
                        volume_ratio = daily_volume_today / avg_volume_7d
                        output_text.append(f"- Volume ratio: {volume_ratio:.2f} (threshold: {LOW_VOLUME_RELATIVE_THRESHOLD})")
                        if volume_ratio < LOW_VOLUME_RELATIVE_THRESHOLD:
                            output_text.append("- Low volume condition is MET")
                            output_text.append("\nThis item should be flagged as a 'Low turnover trap' due to its low volume.")

                self.analysis_results_text_edit.setText("\n".join(output_text))
            else:
                # This case should ideally be handled by the analyzer returning a default "no patterns" dict
                self.analysis_results_text_edit.setText("No analysis results returned.")
            self.analysis_results_group_box.setVisible(True)
        else:
            print("Error: Analysis UI elements not found.")

        # We'll create series and axes when we have data

    def _update_chart_with_market_orders(self, buy_orders: List[Dict[str, Any]], sell_orders: List[Dict[str, Any]]):
        """
        Update the chart with order book depth data.

        Args:
            buy_orders: List of buy orders
            sell_orders: List of sell orders
        """
        # Remove all series and axes from chart
        self.chart.removeAllSeries()

        for axis in self.chart.axes():
            self.chart.removeAxis(axis)

        if not buy_orders and not sell_orders:
            self.chart.setTitle("No Market Orders Available")
            return

        # Group orders by price
        buy_price_groups = {}
        sell_price_groups = {}

        # Process buy orders
        for order in buy_orders:
            price = float(order['price'])
            volume = int(order['volume_remain'])

            # Round price to nearest 0.01 for grouping
            rounded_price = round(price, 2)

            if rounded_price in buy_price_groups:
                buy_price_groups[rounded_price] += volume
            else:
                buy_price_groups[rounded_price] = volume

        # Process sell orders
        for order in sell_orders:
            price = float(order['price'])
            volume = int(order['volume_remain'])

            # Round price to nearest 0.01 for grouping
            rounded_price = round(price, 2)

            if rounded_price in sell_price_groups:
                sell_price_groups[rounded_price] += volume
            else:
                sell_price_groups[rounded_price] = volume

        # Get best bid and ask prices
        if buy_price_groups and sell_price_groups:
            best_bid = max(buy_price_groups.keys())
            best_ask = min(sell_price_groups.keys())
            mid_price = (best_bid + best_ask) / 2
        elif buy_price_groups:
            best_bid = max(buy_price_groups.keys())
            best_ask = best_bid * 1.05  # Estimate if no sell orders
            mid_price = best_bid
        elif sell_price_groups:
            best_ask = min(sell_price_groups.keys())
            best_bid = best_ask * 0.95  # Estimate if no buy orders
            mid_price = best_ask
        else:
            self.chart.setTitle("No Market Orders Available")
            return

        # Create a small font for axis labels and titles
        small_font = QFont()
        small_font.setPointSize(6)  # Small font size for better fit

        # Calculate price range (5% of mid-price on each side for better visibility)
        price_range = mid_price * 0.05
        min_price = mid_price - price_range
        max_price = mid_price + price_range

        # Ensure min_price is never negative for ISK values
        min_price = max(0, min_price)

        # If we have buy and sell orders, make sure the range includes both the best bid and ask
        if buy_price_groups and sell_price_groups:
            min_price = min(min_price, best_bid * 0.98)  # Extend a bit beyond best bid
            max_price = max(max_price, best_ask * 1.02)  # Extend a bit beyond best ask

        # Create a simpler bar chart approach
        # Select a limited number of price points on each side of the spread
        num_price_points = 10  # Number of price points to show on each side

        # For buy orders, select the highest prices (closest to the spread)
        buy_prices = sorted(buy_price_groups.keys(), reverse=True)[:num_price_points]

        # For sell orders, select the lowest prices (closest to the spread)
        sell_prices = sorted(sell_price_groups.keys())[:num_price_points]

        # We'll use a different approach with separate series for buy and sell
        # to ensure they appear in the correct positions

        # Create a bar series for the chart
        series = QBarSeries()

        # Create bar sets for buy and sell orders
        buy_bar_set = QBarSet("Buy Orders")
        buy_bar_set.setColor(QColor(0, 200, 0))  # Green for buy orders

        sell_bar_set = QBarSet("Sell Orders")
        sell_bar_set.setColor(QColor(200, 0, 0))  # Red for sell orders

        # Initialize all categories with zero values
        total_categories = len(buy_prices) + 1 + len(sell_prices)  # +1 for spread

        # Fill buy_bar_set with zeros for all categories
        for i in range(total_categories):
            buy_bar_set.append(0)

        # Fill sell_bar_set with zeros for all categories
        for i in range(total_categories):
            sell_bar_set.append(0)

        # Now set the actual values for buy orders (in reversed order)
        for i, price in enumerate(reversed(buy_prices)):
            buy_bar_set.replace(i, buy_price_groups[price])

        # Now set the actual values for sell orders
        spread_index = len(buy_prices)  # Index of the spread category
        for i, price in enumerate(sell_prices):
            sell_bar_set.replace(spread_index + i, sell_price_groups[price])

        # Add the bar sets to the series
        if len(buy_prices) > 0:
            series.append(buy_bar_set)

        if len(sell_prices) > 0:
            series.append(sell_bar_set)

        print(f"DEBUG: Buy bar set count: {buy_bar_set.count()}")
        print(f"DEBUG: Sell bar set count: {sell_bar_set.count()}")

        # Add the series to the chart
        self.chart.addSeries(series)

        # Create category axis for prices (x-axis)
        axis_x = QBarCategoryAxis()

        # Create categories from prices
        categories = []

        # Add buy prices on the left side in REVERSE order
        # This puts the highest buy prices (closest to the spread) on the right side of the buy section
        for price in reversed(buy_prices):
            categories.append(f"Buy {price:.2f}")

        # Add a category for the spread
        categories.append("Spread")

        # Add sell prices on the right side
        # The lowest sell prices (closest to the spread) are already on the left side of the sell section
        for price in sell_prices:
            categories.append(f"Sell {price:.2f}")

        axis_x.append(categories)
        axis_x.setLabelsFont(small_font)
        axis_x.setTitleFont(small_font)
        axis_x.setTitleText("Price (ISK)")

        # Print debug info about categories
        print(f"DEBUG: Categories: {categories}")
        print(f"DEBUG: Buy prices: {buy_prices}")
        print(f"DEBUG: Sell prices: {sell_prices}")

        # Create value axis for volumes (y-axis)
        axis_y = QValueAxis()
        axis_y.setLabelsFont(small_font)
        axis_y.setTitleFont(small_font)
        axis_y.setTitleText("Volume")
        axis_y.setLabelFormat("%i")

        # Find the maximum volume for the y-axis range
        max_volume = max(
            max(buy_price_groups.values()) if buy_price_groups else 0,
            max(sell_price_groups.values()) if sell_price_groups else 0
        )

        # Add a small buffer (10%) to the top of the chart
        max_volume = max_volume * 1.1 if max_volume > 0 else 100

        # Set the y-axis range
        axis_y.setRange(0, max_volume)

        # Add axes to the chart
        self.chart.addAxis(axis_x, Qt.AlignBottom)
        self.chart.addAxis(axis_y, Qt.AlignLeft)

        # Attach the axes to the series
        series.attachAxis(axis_x)
        series.attachAxis(axis_y)

        # Update chart title
        spread_pct = ((best_ask - best_bid) / best_ask) * 100
        self.chart.setTitle(f"Market Orders - Spread: {spread_pct:.2f}%")

        # Adjust chart margins
        self.chart.setMargins(QMargins(5, 5, 5, 5))

        # Show legend
        self.chart.legend().setVisible(True)
        self.chart.legend().setAlignment(Qt.AlignBottom)
        self.chart.legend().setFont(small_font)

    def _setup_transactions_context_menu(self):
        """Set up the context menu for the transactions table."""
        if self.transactions_table_view:
            # Set the context menu policy
            self.transactions_table_view.setContextMenuPolicy(Qt.CustomContextMenu)
            # Connect the custom context menu requested signal
            self.transactions_table_view.customContextMenuRequested.connect(self._show_transactions_context_menu)

    def _show_transactions_context_menu(self, position):
        """
        Show context menu for the transactions table.

        Args:
            position: Position where the context menu was requested
        """
        # Get the selected row
        selected_rows = self.transactions_table_view.selectionModel().selectedRows()
        if not selected_rows:
            # Select the row at the position
            index = self.transactions_table_view.indexAt(position)
            if index.isValid():
                self.transactions_table_view.selectRow(index.row())
                selected_rows = self.transactions_table_view.selectionModel().selectedRows()
            else:
                print("No valid row at position, not showing context menu")
                return

        if not selected_rows:
            print("No rows selected, not showing context menu")
            return

        print(f"Selected transaction row: {selected_rows[0].row()}")

        # Create context menu
        context_menu = QMenu(self)

        # Add delete action
        delete_action = QAction("Delete", self)
        delete_action.triggered.connect(self._on_delete_transaction)
        context_menu.addAction(delete_action)

        # Show the menu
        global_pos = self.transactions_table_view.mapToGlobal(position)
        print(f"Showing transactions context menu at global position: {global_pos}")
        context_menu.exec_(global_pos)

    def _on_delete_transaction(self):
        """Handle 'Delete' action from transactions context menu."""
        # Get the selected row
        selected_rows = self.transactions_table_view.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Selection Error", "Please select a transaction to delete.")
            return

        # Get the transaction ID from the selected row
        selected_row_index = selected_rows[0].row()
        model = self.transactions_table_view.model()

        # Get transaction ID from the model (tx_id is at column 0)
        tx_id_index = model.index(selected_row_index, 0)
        tx_id = model.data(tx_id_index)

        if not tx_id:
            QMessageBox.warning(self, "Data Error", "Could not retrieve transaction ID.")
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete transaction #{tx_id}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # Delete the transaction from the database
        try:
            # Get the transaction type (BUY or SELL)
            tx_type_query = "SELECT transaction_type FROM transactions WHERE tx_id = ?"
            tx_type_result = self.db.execute_query(tx_type_query, (tx_id,), fetch_one=True)

            if not tx_type_result:
                QMessageBox.warning(self, "Error", "Transaction not found.")
                return

            transaction_type = tx_type_result['transaction_type']

            # Start a database transaction to ensure all operations are atomic
            with self.db.get_connection() as conn:
                cursor = conn.cursor()

                try:
                    # Check for transaction batch links (for SELL transactions)
                    if transaction_type == 'SELL':
                        batch_links_query = "SELECT COUNT(*) as count FROM transaction_batch_links WHERE transaction_id = ?"
                        cursor.execute(batch_links_query, (tx_id,))
                        result = cursor.fetchone()

                        has_batch_links = result and result['count'] > 0

                        if has_batch_links:
                            # Get the batch links to restore inventory quantities
                            batch_links_detail_query = """
                            SELECT batch_id, quantity FROM transaction_batch_links WHERE transaction_id = ?
                            """
                            cursor.execute(batch_links_detail_query, (tx_id,))
                            batch_links = cursor.fetchall()

                            # Restore inventory quantities for each batch
                            for link in batch_links:
                                restore_query = """
                                UPDATE inventory_batches
                                SET quantity_remaining = quantity_remaining + ?
                                WHERE batch_id = ?
                                """
                                cursor.execute(restore_query, (link['quantity'], link['batch_id']))
                                print(f"Restored {link['quantity']} units to batch #{link['batch_id']}")

                            # Delete the transaction batch links
                            batch_links_delete_query = "DELETE FROM transaction_batch_links WHERE transaction_id = ?"
                            cursor.execute(batch_links_delete_query, (tx_id,))
                            print(f"Deleted batch links for transaction #{tx_id}")

                    # Check for inventory batches that reference this transaction (for BUY transactions)
                    elif transaction_type == 'BUY':
                        # Check if there are any inventory batches that reference this transaction
                        inventory_query = "SELECT COUNT(*) as count FROM inventory_batches WHERE transaction_id = ?"
                        cursor.execute(inventory_query, (tx_id,))
                        result = cursor.fetchone()

                        has_inventory = result and result['count'] > 0

                        if has_inventory:
                            # Check if any of these batches have been partially consumed
                            consumed_query = """
                            SELECT batch_id, quantity_original, quantity_remaining
                            FROM inventory_batches
                            WHERE transaction_id = ? AND quantity_original > quantity_remaining
                            """
                            cursor.execute(consumed_query, (tx_id,))
                            consumed_batches = cursor.fetchall()

                            if consumed_batches:
                                # Some batches have been consumed, warn the user
                                batch_info = "\n".join([
                                    f"Batch #{b['batch_id']}: {b['quantity_original'] - b['quantity_remaining']} of {b['quantity_original']} units consumed"
                                    for b in consumed_batches
                                ])

                                warning_msg = f"This purchase transaction created inventory batches that have been partially sold:\n\n{batch_info}\n\nDeleting this transaction will remove these inventory batches and may cause inconsistencies in your profit tracking. Continue anyway?"

                                # We need to commit the transaction before showing the dialog
                                # to avoid locking the database
                                conn.commit()

                                confirm = QMessageBox.warning(
                                    self,
                                    "Warning: Inventory Impact",
                                    warning_msg,
                                    QMessageBox.Yes | QMessageBox.No,
                                    QMessageBox.No
                                )

                                if confirm != QMessageBox.Yes:
                                    return

                                # Re-start the transaction since we committed it before the dialog
                                cursor = conn.cursor()

                            # Delete the inventory batches
                            inventory_delete_query = "DELETE FROM inventory_batches WHERE transaction_id = ?"
                            cursor.execute(inventory_delete_query, (tx_id,))
                            print(f"Deleted inventory batches for transaction #{tx_id}")

                    # Now delete the transaction
                    query = "DELETE FROM transactions WHERE tx_id = ?"
                    cursor.execute(query, (tx_id,))

                    # Commit the transaction
                    conn.commit()
                    print(f"Transaction #{tx_id} deleted successfully")

                    # Refresh the transactions view
                    if self.current_item_id:
                        self._load_transaction_history(self.current_item_id)

                    # Update total profit display
                    if self.current_item_id:
                        total_profit = self._calculate_total_profit(self.current_item_id)
                        self._update_total_profit_display(total_profit)

                except Exception as e:
                    # Rollback in case of error
                    conn.rollback()
                    print(f"Error deleting transaction: {e}")
                    import traceback
                    print(traceback.format_exc())
                    QMessageBox.critical(self, "Error", f"Failed to delete transaction: {e}")
                    return
        except Exception as e:
            print(f"Error deleting transaction: {e}")
            import traceback
            print(traceback.format_exc())
            QMessageBox.critical(self, "Error", f"Failed to delete transaction: {e}")
