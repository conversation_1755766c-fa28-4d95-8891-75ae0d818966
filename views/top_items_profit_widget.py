"""
Top Items Profit Widget

This module provides a widget for displaying top items by profit in a bar graph.
"""

import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal

from PyQt5 import QtWidgets, QtCore
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QButtonGroup,
    QSizePolicy, QFrame
)
from PyQt5.QtCore import pyqtSignal, pyqtSlot, Qt, QRectF, QPointF

import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
import matplotlib.pyplot as plt
import numpy as np

from controllers.top_items_profit_controller import TopItemsProfitController


class TopItemsProfitWidget(QWidget):
    """Widget for displaying top items by profit in a bar graph."""

    # Signal to request a refresh of the data
    refresh_requested = pyqtSignal()

    def __init__(self, parent=None):
        """Initialize the top items profit widget."""
        super().__init__(parent)

        # Initialize controller to None (will be set later)
        self.controller = None

        # Initialize UI
        self._init_ui()

        # Connect signals
        self._connect_signals()

        # Initialize data
        self.profit_data = []
        self.current_date_range = None  # Default to all-time

        # Color map for consistent item colors
        self.color_map = {}
        self.color_palette = [
            '#4CAF50', '#2196F3', '#FFC107', '#E91E63', '#9C27B0',
            '#00BCD4', '#FF5722', '#795548', '#607D8B', '#CDDC39'
        ]

    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Title and reset button in a horizontal layout
        title_layout = QHBoxLayout()

        # Title
        title_label = QLabel("Top Items by Profit")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        title_layout.addWidget(title_label)

        # Spacer
        title_layout.addStretch()

        # Reset button
        self.reset_button = QPushButton("Reset to All-Time")
        self.reset_button.setEnabled(False)  # Disabled by default
        title_layout.addWidget(self.reset_button)

        main_layout.addLayout(title_layout)

        # Figure for the plot
        self.figure = Figure(figsize=(8, 8), dpi=100)  # Increased height in the figure itself
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.canvas.setMinimumHeight(500)  # Set an even taller minimum height
        main_layout.addWidget(self.canvas)

        # Date range label
        self.date_range_label = QLabel("Showing: All-Time")
        main_layout.addWidget(self.date_range_label)

    def _connect_signals(self):
        """Connect signals to slots."""
        # Connect reset button
        self.reset_button.clicked.connect(self._on_reset_clicked)

    def set_controller(self, controller: TopItemsProfitController):
        """Set the controller for this widget."""
        self.controller = controller

    def refresh_data(self):
        """Refresh the data and update the plot."""
        if not self.controller:
            return

        # Get data from controller based on current date range
        self.profit_data = self.controller.get_top_items_profit_data(self.current_date_range)

        # Update the plot
        self._update_plot()

    def update_date_range(self, start_date: datetime, end_date: datetime):
        """Update the widget with a new date range."""
        self.current_date_range = (start_date, end_date)

        # Enable reset button
        self.reset_button.setEnabled(True)

        # Update date range label
        self.date_range_label.setText(f"Showing: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

        # Refresh data with new date range
        self.refresh_data()

    def _on_reset_clicked(self):
        """Handle reset button click."""
        # Reset date range
        self.current_date_range = None

        # Disable reset button
        self.reset_button.setEnabled(False)

        # Update date range label
        self.date_range_label.setText("Showing: All-Time")

        # Refresh data
        self.refresh_data()

    def _update_plot(self):
        """Update the plot with the current data."""
        # Clear the figure
        self.figure.clear()

        # Create subplot
        ax = self.figure.add_subplot(111)

        # Check if we have data
        if not self.profit_data:
            ax.text(0.5, 0.5, "No profit data available\nTry making some transactions first",
                    horizontalalignment='center', verticalalignment='center',
                    transform=ax.transAxes)
            self.canvas.draw()
            return

        # Extract item names and profits
        items = []
        profits = []

        for item in self.profit_data:
            items.append(item['item_name'])
            profits.append(float(item['profit']))

        # Check if all profits are zero
        if all(profit == 0 for profit in profits):
            ax.text(0.5, 0.5, "No profit data available\nAll transactions have zero profit",
                    horizontalalignment='center', verticalalignment='center',
                    transform=ax.transAxes)
            self.canvas.draw()
            return

        # Assign consistent colors to items
        colors = []
        for item in items:
            if item not in self.color_map:
                # Assign a new color from the palette
                color_index = len(self.color_map) % len(self.color_palette)
                self.color_map[item] = self.color_palette[color_index]
            colors.append(self.color_map[item])

        # Create horizontal bar chart (reversed order so highest profit is at the top)
        y_pos = np.arange(len(items))
        ax.barh(y_pos, profits, align='center', color=colors)
        ax.set_yticks(y_pos)
        ax.set_yticklabels(items)

        # Format the plot
        ax.set_xlabel('Profit (ISK)')
        ax.invert_yaxis()  # Items with highest profit at the top
        ax.grid(True, linestyle='--', alpha=0.7, axis='x')

        # Format x-axis with ISK values
        ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f"{x:,.0f}"))

        # Adjust layout
        self.figure.tight_layout()

        # Draw the canvas
        self.canvas.draw()
