import sys
import os
import webbrowser
import uuid
import threading
from PyQt5 import uic
from PyQt5.QtWidgets import (
    QMainWindow, QApplication, QTableView, QMessageBox, QProgressBar, QLabel,
    QTableWidget, QTableWidgetItem, QWidget, QVBoxLayout, QScrollArea, QMenu, QAction
)
from PyQt5.QtCore import pyqtSignal, pyqtSlot, QThread, QObject, Qt # For threading

# Import the authentication components
from api.auth import ESIAuthenticator
from api.callback_server import authenticate_with_eve_sso

# Import table models
from views.table_models import TransactionsTableModel, OrdersTableModel

# Import item database
from utils.item_database import get_item_database

# Import tabs
from views.trade_execution_tab import TradeExecutionTab
from controllers.trade_execution_controller import TradeExecutionController
from views.market_tab import MarketTab
from views.execute_tab import ExecuteTab
from views.configure_tab import ConfigureTab
from views.net_worth_widget import NetWorthWidget
from controllers.net_worth_controller import NetWorthController
from views.daily_profit_widget import DailyProfitWidget
from controllers.daily_profit_controller import DailyProfitController
from views.top_items_profit_widget import TopItemsProfitWidget
from controllers.top_items_profit_controller import TopItemsProfitController

# Import market request handler
from utils.market_request_handler import MarketRequestHandler

# --- Placeholder for Controllers ---
# In a real app, these would be properly instantiated and passed in
# from controllers.accounting_controller import AccountingController
# from controllers.strategy_controller import StrategyController
# from controllers.performance_controller import PerformanceController
# from controllers.data_sync_controller import DataSyncController # Assuming this will be created
# from models.database import DatabaseManager
# from api.esi_client import ESIClient
# from api.auth import ESIAuthenticator

# --- Placeholder Worker Thread ---
# (Based on design doc example - will need actual implementation later)
class DataSyncWorker(QObject): # Use QObject for signals/slots if not using QThread directly for run()
    progress_updated = pyqtSignal(int, str)
    sync_completed = pyqtSignal(bool, str)
    # Add signals for specific data updates if needed, e.g., transactions_updated = pyqtSignal(list)

    def __init__(self, data_sync_controller):
        super().__init__()
        self.controller = data_sync_controller
        self.is_running = False

    @pyqtSlot() # Mark as a slot if triggered by a signal
    def run(self):
        """ The main task execution method for the worker. """
        if self.is_running:
            print("Sync already in progress.")
            return
        self.is_running = True
        try:
            self.progress_updated.emit(0, "Starting synchronization...")

            # Use the actual controller if available, otherwise use mock implementation
            if self.controller:
                print("Using actual DataSyncController")

                # Check if controller has necessary components
                if not hasattr(self.controller, 'esi') or not self.controller.esi:
                    error_msg = "DataSyncController has no ESI client"
                    print(error_msg)
                    self.sync_completed.emit(False, error_msg)
                    return

                # Check if character ID is set
                if not hasattr(self.controller, 'character_id') or not self.controller.character_id:
                    error_msg = "No character ID set in DataSyncController"
                    print(error_msg)
                    self.sync_completed.emit(False, error_msg)
                    return

                # Check if ESI client has character ID
                if not self.controller.esi.character_id:
                    error_msg = "No character ID set in ESI client"
                    print(error_msg)
                    self.sync_completed.emit(False, error_msg)
                    return

                print(f"Starting sync with character ID: {self.controller.character_id}")
                success = self.controller.sync_all_data(progress_callback=self.progress_updated.emit)
                summary = "Sync completed successfully." if success else "Sync failed."
            else:
                # Mock implementation for testing
                print("Using mock implementation (no controller available)")
                import time
                self.progress_updated.emit(10, "Syncing wallet transactions...")
                time.sleep(1)
                self.progress_updated.emit(30, "Syncing market orders...")
                time.sleep(1)
                # Removed inventory sync step
                time.sleep(1)
                self.progress_updated.emit(80, "Calculating profits...")
                time.sleep(1)
                self.progress_updated.emit(90, "Classifying strategies...")
                time.sleep(1)
                success = True # Assume success for now
                summary = "Mock sync completed successfully."

            self.progress_updated.emit(100, summary)
            self.sync_completed.emit(success, summary)
        except Exception as e:
            import traceback
            error_msg = f"Sync failed: {str(e)}"
            print(error_msg)
            print(f"Exception details: {traceback.format_exc()}")
            self.sync_completed.emit(False, error_msg)
        finally:
            self.is_running = False


# --- Main Window Class ---
# Get the absolute path to the UI file
current_dir = os.path.dirname(os.path.abspath(__file__))
ui_file_path = os.path.join(current_dir, 'ui', 'main_window.ui')
Ui_MainWindow, QtBaseClass = uic.loadUiType(ui_file_path)

class MainWindow(QMainWindow, Ui_MainWindow):
    # Signals
    start_sync_signal = pyqtSignal()
    # Signal for handling authentication callback from another thread
    auth_callback_signal = pyqtSignal(str, int, str)

    def __init__(self, authenticator=None, esi_client=None, data_sync_controller=None):
        super().__init__()
        self.setupUi(self)

        # --- Initialize Controllers ---
        # Store references to controllers passed in via dependency injection
        self.authenticator = authenticator if authenticator else ESIAuthenticator()
        self.esi_client = esi_client
        self.data_sync_controller = data_sync_controller

        # Connect the authentication callback signal to its slot
        self.auth_callback_signal.connect(self.handle_auth_callback)

        # Check if we have an authenticated character
        self.check_auth_status()

        # --- Setup Worker Thread ---
        self.worker_thread = QThread()
        self.data_sync_worker = DataSyncWorker(self.data_sync_controller)
        self.data_sync_worker.moveToThread(self.worker_thread)
        print("Data sync worker initialized with controller:", self.data_sync_controller)

        # --- Connect Signals and Slots ---
        # Worker signals
        self.data_sync_worker.progress_updated.connect(self.update_sync_progress)
        self.data_sync_worker.sync_completed.connect(self.on_sync_complete)
        # Signal to start worker task
        self.start_sync_signal.connect(self.data_sync_worker.run)
        # Thread management
        self.worker_thread.started.connect(lambda: print("Worker thread started."))
        self.worker_thread.finished.connect(lambda: print("Worker thread finished."))
        # Ensure thread quits when app closes
        QApplication.instance().aboutToQuit.connect(self.worker_thread.quit)


        # UI element signals
        self.updateDataButton.clicked.connect(self.start_data_sync)
        # Authentication button will be connected after ConfigureTab is created

        # --- Initial UI State ---
        self.syncProgressBar.setVisible(False)
        self.syncStatusLabel.setText("")

        # Set up context menus for table views
        self.transactionsTableView.setContextMenuPolicy(Qt.CustomContextMenu)
        self.transactionsTableView.customContextMenuRequested.connect(self._show_transactions_context_menu)

        self.ordersTableView.setContextMenuPolicy(Qt.CustomContextMenu)
        self.ordersTableView.customContextMenuRequested.connect(self._show_orders_context_menu)

        # Start the worker thread (it will wait for signals)
        self.worker_thread.start()

        # Load initial data from database if available
        self.refresh_data_views()

        # Initialize the item database
        self.initialize_item_database()

        # Initialize the market request handler
        self.initialize_market_request_handler()

        # Create tab instances
        market_tab_widget = self._create_market_tab()
        trade_execution_tab_widget = self._create_trade_execution_tab()
        execute_tab_widget = self._create_execute_tab()
        configure_tab_widget = self._create_configure_tab()

        # Create and add the net worth widget to the Monitor tab
        self._setup_net_worth_widget()

        # Remove placeholder tabs from the UI
        # Initial UI order: marketTab (0), dataManagementTab (1), settingsTab (2)

        # Remove market placeholder at index 0
        original_market_placeholder = self.tabWidget.widget(0)
        if original_market_placeholder and original_market_placeholder.objectName() == "marketTab":
            self.tabWidget.removeTab(0)
            print("Removed marketTab placeholder.")
        else:
            print("Warning: Could not find marketTab placeholder at index 0 to remove.")

        # At this point, tabWidget should contain [dataManagementTab, settingsTab]

        # Insert actual tabs in the desired order
        # Market Tab at index 0 (renamed to "Analyze")
        if market_tab_widget:
            self.tabWidget.insertTab(0, market_tab_widget, "Analyze")
            market_tab_widget.sync_requested.connect(self.start_data_sync) # Connect signal
            print("Analyze tab inserted at index 0.")
        else:
            print("Failed to create Analyze tab, not inserted.")

        # Trade Execution Tab at index 1 (renamed to "Search")
        if trade_execution_tab_widget:
            self.tabWidget.insertTab(1, trade_execution_tab_widget, "Search")
            print("Search tab inserted at index 1.")
            # Connect MarketTab's item_selected_for_analysis signal to TradeExecutionTab slot
            if market_tab_widget:
                market_tab_widget.item_selected_for_analysis.connect(trade_execution_tab_widget._on_item_selected_for_analysis)
        else:
            print("Failed to create Search tab, not inserted.")

        # Execute Tab at index 2
        if execute_tab_widget:
            self.tabWidget.insertTab(2, execute_tab_widget, "Execute")
            print("Execute tab inserted at index 2.")
        else:
            print("Failed to create Execute tab, not inserted.")

        # Find and replace the settingsTab with our ConfigureTab
        settings_tab_found = False
        for i in range(self.tabWidget.count()):
            tab_widget = self.tabWidget.widget(i)
            if tab_widget and tab_widget.objectName() == "settingsTab":
                # Remove the old settings tab
                self.tabWidget.removeTab(i)
                # Insert our new Configure tab
                if configure_tab_widget:
                    self.tabWidget.insertTab(i, configure_tab_widget, "Configure")
                    print(f"Configure tab inserted at index {i}, replacing settingsTab.")
                    # Store a reference to the configure tab
                    self.configure_tab = configure_tab_widget
                else:
                    print("Failed to create Configure tab, not inserted.")
                settings_tab_found = True
                break

        # If we didn't find the settingsTab, add our Configure tab at the end
        if not settings_tab_found and configure_tab_widget:
            index = self.tabWidget.count()
            self.tabWidget.addTab(configure_tab_widget, "Configure")
            print(f"Configure tab added at index {index}.")
            # Store a reference to the configure tab
            self.configure_tab = configure_tab_widget

        # Final Tab Order: Analyze (0), Search (1), Execute (2), Monitor (3), Configure (4)
        self.tabWidget.setCurrentIndex(0)

        # Connect tab changed signal to handle default subtab selection
        self.tabWidget.currentChanged.connect(self._on_tab_changed)


    def check_auth_status(self):
        """ Check if we have an authenticated character and update the UI accordingly. """
        if not hasattr(self, 'authenticator') or not self.authenticator:
            self.authStatusLabel.setText("Status: Authentication not configured")
            return

        # Query the database for authenticated characters
        conn = self.authenticator._get_db_connection()
        if not conn:
            self.authStatusLabel.setText("Status: Database connection error")
            return

        try:
            cursor = conn.cursor()
            cursor.execute("SELECT character_id, character_name, expires_at FROM api_tokens ORDER BY character_name")
            rows = cursor.fetchall()

            if not rows:
                self.authStatusLabel.setText("Status: No authenticated characters")
                return

            # Display the first character (or we could show a list if multiple)
            char = rows[0]
            self.authStatusLabel.setText(f"Status: Authenticated as {char['character_name']}")

            # Update the ESI client with this character ID if we have one
            if self.esi_client:
                self.esi_client.character_id = char['character_id']
                print(f"Updated ESI client with character ID: {char['character_id']} during startup")

                # Update the data sync controller with the updated ESI client
                if self.data_sync_controller:
                    self.data_sync_controller.esi = self.esi_client
                    self.data_sync_controller.character_id = char['character_id']
                    print(f"Updated data sync controller with character ID: {char['character_id']} during startup")
                else:
                    print("Warning: No data sync controller available to update during startup")

        except Exception as e:
            self.authStatusLabel.setText(f"Status: Error checking auth - {str(e)}")
        finally:
            conn.close()

    @pyqtSlot(str, int, str)
    def handle_auth_callback(self, status, character_id=0, character_name=""):
        """
        Handle the authentication callback from another thread via signal/slot.
        This method runs in the main UI thread.

        Args:
            status: Status message ("success", "error", "timeout")
            character_id: The authenticated character ID (if successful)
            character_name: The authenticated character name (if successful)
        """
        print(f"Auth callback received: {status}, character_id={character_id}, name={character_name}")

        # Re-enable the authentication button
        self.authenticateButton.setEnabled(True)

        if status == "success":
            # Update the UI with success
            self.authStatusLabel.setText(f"Status: Authenticated as {character_name}")

            # Update the ESI client with this character ID if we have one
            if self.esi_client:
                self.esi_client.character_id = character_id
                print(f"Updated ESI client with character ID: {character_id}")

                # Update the data sync controller with the updated ESI client
                if self.data_sync_controller:
                    self.data_sync_controller.esi = self.esi_client
                    self.data_sync_controller.character_id = character_id
                    print(f"Updated data sync controller with character ID: {character_id}")
                else:
                    print("Warning: No data sync controller available to update")

                # Also update the net worth widget controller if it exists
                if hasattr(self, 'net_worth_widget') and self.net_worth_widget:
                    controller = getattr(self.net_worth_widget, 'controller', None)
                    if controller:
                        controller.character_id = character_id
                        if hasattr(controller, 'esi') and controller.esi:
                            controller.esi.character_id = character_id
                        # Refresh the data now that we have a character ID
                        self.net_worth_widget.refresh_data()

            # Show success message
            QMessageBox.information(self, "Authentication Successful",
                                   f"Successfully authenticated as {character_name}")

            # Refresh the skills data in the Configure tab if it exists
            if hasattr(self, 'configure_tab') and self.configure_tab:
                print("Refreshing skills data in Configure tab after authentication...")
                self.configure_tab.refresh_skills_data()

        elif status == "error":
            # Show error message
            self.authStatusLabel.setText(f"Status: Authentication error - {character_name}")
            QMessageBox.critical(self, "Authentication Error",
                               f"Error during authentication: {character_name}")

        elif status == "timeout":
            # Show timeout message
            self.authStatusLabel.setText("Status: Authentication timed out or failed")
            QMessageBox.warning(self, "Authentication Failed",
                               "Authentication timed out or failed. Please try again.")

    @pyqtSlot()
    def start_authentication(self):
        """ Handles the EVE SSO authentication process. """
        print("Authentication button clicked - Starting EVE SSO flow")

        # Generate a random state value for security
        state = str(uuid.uuid4())

        # Get the authentication URL
        auth_url = self.authenticator.get_auth_url(state=state)

        # Update UI to show we're starting authentication
        self.authStatusLabel.setText("Status: Authentication started - waiting for callback...")
        self.authenticateButton.setEnabled(False)

        # Define a callback function to handle the authorization code
        # This will be called from the callback server thread
        def handle_auth_code(code):
            try:
                # Process the callback code
                character_id = self.authenticator.process_callback(code)
                if character_id:
                    # Get character info to display
                    conn = self.authenticator._get_db_connection()
                    if conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT character_name FROM api_tokens WHERE character_id = ?", (character_id,))
                        row = cursor.fetchone()
                        character_name = row['character_name'] if row else "Unknown"
                        conn.close()

                        # Emit signal to update UI from the main thread
                        self.auth_callback_signal.emit("success", character_id, character_name)
                    else:
                        # Emit signal with error
                        self.auth_callback_signal.emit("error", 0, "Database connection error")
                else:
                    # Emit signal with error
                    self.auth_callback_signal.emit("error", 0, "Failed to process authentication callback")
            except Exception as e:
                # Emit signal with error
                self.auth_callback_signal.emit("error", 0, str(e))

        # Start the authentication flow in a separate thread
        def auth_thread_func():
            try:
                # This will open the browser and wait for the callback
                code = authenticate_with_eve_sso(auth_url, handle_auth_code)

                # If code is None and no callback was processed, emit timeout signal
                if code is None:
                    # Check if the button is still disabled (no callback processed)
                    if not self.authenticateButton.isEnabled():
                        self.auth_callback_signal.emit("timeout", 0, "")
            except Exception as e:
                # Emit signal with error
                self.auth_callback_signal.emit("error", 0, str(e))

        # Start the authentication in a separate thread
        auth_thread = threading.Thread(target=auth_thread_func)
        auth_thread.daemon = True
        auth_thread.start()


    @pyqtSlot()
    def start_data_sync(self):
        """ Initiates the data synchronization process in the worker thread. """
        print("Update Data button clicked.")
        # Disable button, show progress bar
        self.updateDataButton.setEnabled(False)
        self.syncProgressBar.setVisible(True)
        self.syncProgressBar.setValue(0)
        self.syncStatusLabel.setText("Starting sync...")
        # Emit signal to start worker's run method in its thread
        self.start_sync_signal.emit()

    @pyqtSlot(int, str)
    def update_sync_progress(self, value: int, message: str):
        """ Updates the progress bar and status label in all tabs. """
        # Update in Data Management tab
        self.syncProgressBar.setValue(value)
        self.syncStatusLabel.setText(message)

        # Update in Market tab if it exists
        market_tab = self.tabWidget.widget(0)
        if isinstance(market_tab, MarketTab):
            market_tab.update_sync_progress(value, message)

    @pyqtSlot(bool, str)
    def on_sync_complete(self, success: bool, message: str):
        """ Handles completion of the sync process. """
        print(f"Sync complete signal received: Success={success}, Message={message}")
        self.updateDataButton.setEnabled(True)
        self.syncProgressBar.setVisible(False)
        self.syncStatusLabel.setText(message)
        if success:
            # Removed pop-up message
            # Refresh all data views
            self.refresh_data_views()
        else:
            QMessageBox.warning(self, "Sync Failed", message)

    def refresh_data_views(self):
        """ Refreshes all data views (transactions, orders) with latest data from database. """
        print("Refreshing data views...")
        self.refresh_transactions_view()
        self.refresh_orders_view()

        # Also refresh the analysis results in the Search tab if it exists
        search_tab = self.tabWidget.widget(1)
        if search_tab and hasattr(search_tab, 'refresh_analysis_results'):
            print("Refreshing analysis results in Search tab...")
            search_tab.refresh_analysis_results()

        # Refresh the skills data in the Configure tab if it exists
        if hasattr(self, 'configure_tab') and self.configure_tab:
            print("Refreshing skills data in Configure tab...")
            self.configure_tab.refresh_skills_data()

        # Refresh the profit visualization widgets if they exist
        if hasattr(self, 'daily_profit_widget'):
            print("Refreshing daily profit widget...")
            self.daily_profit_widget.refresh_data()

        if hasattr(self, 'top_items_profit_widget'):
            print("Refreshing top items profit widget...")
            self.top_items_profit_widget.refresh_data()

        # Refresh the net worth widget if it exists
        if hasattr(self, 'net_worth_widget') and self.net_worth_widget:
            print("Refreshing net worth data...")
            self.net_worth_widget.refresh_data()

    def refresh_transactions_view(self):
        """ Refreshes the transactions table view with latest data from database. """
        print("Refreshing transactions view...")
        # Check if we have a database connection
        if not hasattr(self, 'data_sync_controller') or not self.data_sync_controller or not hasattr(self.data_sync_controller, 'db'):
            print("Warning: No database connection available for refreshing transactions view")
            return

        try:
            # Fetch transactions from database
            query = """
            SELECT tx_id, timestamp, item_id, item_name, quantity, unit_price, total_price,
                   transaction_type, net_amount, strategy, profit
            FROM transactions
            ORDER BY timestamp DESC
            LIMIT 1000
            """
            transactions = self.data_sync_controller.db.execute_query(query, fetch_all=True)

            if not transactions:
                print("No transactions found in database")
                return

            print(f"Found {len(transactions)} transactions")

            # Create a table model and set it on the view
            model = TransactionsTableModel(transactions)
            self.transactionsTableView.setModel(model)

            # Adjust column widths for better display
            self.transactionsTableView.resizeColumnsToContents()



        except Exception as e:
            print(f"Error refreshing transactions view: {e}")
            import traceback
            print(traceback.format_exc())



    def _on_analyze_transaction_item(self):
        """Handle 'Analyze Item' action from transactions context menu."""
        # Get the selected row
        selected_rows = self.transactionsTableView.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Selection Error", "Please select a transaction to analyze.")
            return

        # Get the item data from the selected row
        selected_row_index = selected_rows[0].row()
        model = self.transactionsTableView.model()

        # Get item_id and item_name from the model
        # In our TransactionsTableModel, item_id is at column 2, item_name is at column 3
        item_id_index = model.index(selected_row_index, 2)
        item_name_index = model.index(selected_row_index, 3)

        item_id = model.data(item_id_index)
        item_name = model.data(item_name_index)

        if not item_id or not item_name:
            QMessageBox.warning(self, "Data Error", "Could not retrieve item data from the transaction.")
            return

        print(f"Analyzing transaction item: {item_name} (ID: {item_id})")
        self._analyze_item(item_id, item_name)

    def _on_analyze_order_item(self):
        """Handle 'Analyze Item' action from orders context menu."""
        # Get the selected row
        selected_rows = self.ordersTableView.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "Selection Error", "Please select an order to analyze.")
            return

        # Get the item data from the selected row
        selected_row_index = selected_rows[0].row()
        model = self.ordersTableView.model()

        # Get item_name from the model (orders table doesn't have item_id column)
        # In OrdersTableModel, item_name is at column 1
        item_name_index = model.index(selected_row_index, 1)
        item_name = model.data(item_name_index)

        if not item_name:
            QMessageBox.warning(self, "Data Error", "Could not retrieve item data from the order.")
            return

        print(f"Analyzing order item: {item_name}")

        # We don't have the item_id, but we can look it up using the item name
        self._analyze_item(None, item_name)

    def _analyze_item(self, item_id, item_name):
        """
        Common method to analyze an item by switching to the Analyze tab.

        Args:
            item_id: The ID of the item to analyze (can be None if only item_name is available)
            item_name: The name of the item to analyze
        """
        # Note: item_id is not used in this implementation but kept for future use
        # Switch to the Analyze tab (index 0)
        self.tabWidget.setCurrentIndex(0)

        # Get the market tab instance
        market_tab = self.tabWidget.widget(0)
        if not isinstance(market_tab, MarketTab):
            QMessageBox.critical(self, "Error", "Could not access the Analyze tab.")
            return

        # Load the item in the market tab
        # First set the item name in the search combo box
        market_tab.item_search_combo.setCurrentText(item_name)

        # Then trigger the item loading
        market_tab._load_item_by_name(item_name)

    def refresh_orders_view(self):
        """ Refreshes the orders table view with latest data from database. """
        print("Refreshing orders view...")
        # Check if we have a database connection
        if not hasattr(self, 'data_sync_controller') or not self.data_sync_controller or not hasattr(self.data_sync_controller, 'db'):
            print("Warning: No database connection available for refreshing orders view")
            return

        try:
            # Check if character_id column exists in orders table
            check_column_query = "PRAGMA table_info(orders)"
            columns = self.data_sync_controller.db.execute_query(check_column_query, fetch_all=True)
            has_character_id = any(col['name'] == 'character_id' for col in columns) if columns else False

            # Get the current character ID
            character_id = self.data_sync_controller.character_id if hasattr(self.data_sync_controller, 'character_id') else None

            # Fetch active orders from database
            if has_character_id and character_id:
                print(f"Filtering orders by character_id: {character_id}")
                query = """
                SELECT order_id, item_name, order_type, quantity_original, quantity_remaining,
                       price, placed_time, strategy
                FROM orders
                WHERE quantity_remaining > 0 AND NOT expired AND NOT canceled
                AND character_id = ?
                ORDER BY placed_time DESC
                """
                orders = self.data_sync_controller.db.execute_query(query, (character_id,), fetch_all=True)
            else:
                print("Character ID not available or column doesn't exist, fetching all orders")
                query = """
                SELECT order_id, item_name, order_type, quantity_original, quantity_remaining,
                       price, placed_time, strategy
                FROM orders
                WHERE quantity_remaining > 0 AND NOT expired AND NOT canceled
                ORDER BY placed_time DESC
                """
                orders = self.data_sync_controller.db.execute_query(query, fetch_all=True)

            if not orders:
                print("No active orders found in database")
                return

            print(f"Found {len(orders)} active orders")

            # Create a table model and set it on the view
            model = OrdersTableModel(orders)
            self.ordersTableView.setModel(model)

            # Adjust column widths for better display
            self.ordersTableView.resizeColumnsToContents()



        except Exception as e:
            print(f"Error refreshing orders view: {e}")
            import traceback
            print(traceback.format_exc())



    def _create_trade_execution_tab(self) -> TradeExecutionTab | None:
        """Creates the trade execution tab instance."""
        try:
            if not hasattr(self, 'data_sync_controller') or \
               not self.data_sync_controller or \
               not hasattr(self.data_sync_controller, 'db'):
                print("Warning: No database connection available for Trade Execution tab creation.")
                return None

            trade_execution_controller = TradeExecutionController(
                db_manager=self.data_sync_controller.db,
                esi_client=self.esi_client
            )
            trade_execution_tab = TradeExecutionTab(
                controller=trade_execution_controller,
                main_window=self
            )
            print("Trade Execution tab instance created successfully.")
            return trade_execution_tab
        except Exception as e:
            print(f"Error creating Trade Execution tab instance: {e}")
            import traceback
            print(traceback.format_exc())
            return None

    def _create_execute_tab(self) -> QWidget:
        """Creates the Execute tab for managing items to execute trades for."""
        try:
            # Create an instance of the ExecuteTab class
            execute_tab = ExecuteTab()

            # Store a reference to the execute tab for other tabs to access
            self.execute_tab = execute_tab

            print("Execute tab instance created successfully.")
            return execute_tab
        except Exception as e:
            print(f"Error creating Execute tab instance: {e}")
            import traceback
            print(traceback.format_exc())
            return QWidget()  # Return an empty widget as fallback

    def _create_market_tab(self) -> MarketTab | None:
        """Creates the market tab instance."""
        try:
            if not hasattr(self, 'data_sync_controller') or \
               not self.data_sync_controller or \
               not hasattr(self.data_sync_controller, 'db'):
                print("Warning: No database connection available for Market tab creation.")
                return None

            market_tab = MarketTab(
                db_manager=self.data_sync_controller.db,
                data_sync_controller=self.data_sync_controller
            )
            # Signal connection will be done in __init__ after instance is confirmed
            print("Market tab instance created successfully.")
            return market_tab
        except Exception as e:
            print(f"Error creating Market tab instance: {e}")
            import traceback
            print(traceback.format_exc())
            return None

    def _create_configure_tab(self) -> ConfigureTab | None:
        """Creates the configure tab instance."""
        try:
            if not hasattr(self, 'data_sync_controller') or \
               not self.data_sync_controller or \
               not hasattr(self.data_sync_controller, 'db'):
                print("Warning: No database connection available for Configure tab creation.")
                return None

            configure_tab = ConfigureTab(
                db_manager=self.data_sync_controller.db,
                esi_client=self.esi_client
            )

            # Connect the authentication button to the start_authentication method
            configure_tab.authenticateButton.clicked.connect(self.start_authentication)

            # Update the authentication status label
            self.authStatusLabel = configure_tab.authStatusLabel

            # Check authentication status to update the label
            self.check_auth_status()

            print("Configure tab instance created successfully.")
            return configure_tab
        except Exception as e:
            print(f"Error creating Configure tab instance: {e}")
            import traceback
            print(traceback.format_exc())
            return None

    def initialize_item_database(self):
        """Initialize the item database if needed."""
        try:
            # Get the item database instance
            self.item_db = get_item_database()

            # Check if we have items
            count = self.item_db.count_items()
            print(f"Item database contains {count} items")

            if count < 1000:  # Arbitrary threshold
                print("Item database needs initialization. Starting import...")
                self.syncStatusLabel.setText("Initializing item database, please wait...")
                QApplication.processEvents()  # Update UI

                # Import from Fuzzwork in a separate thread to avoid freezing the UI
                import threading

                def import_thread_func():
                    success = self.item_db.import_from_fuzzwork()
                    if success:
                        new_count = self.item_db.count_items()
                        self.syncStatusLabel.setText(f"Item database initialized with {new_count} items")
                    else:
                        self.syncStatusLabel.setText("Failed to initialize item database")

                thread = threading.Thread(target=import_thread_func)
                thread.daemon = True
                thread.start()
            else:
                print(f"Item database ready with {count} items")
        except Exception as e:
            print(f"Error initializing item database: {e}")

    def initialize_market_request_handler(self):
        """Initialize the market request handler for TinyGamer integration."""
        try:
            # Check if we have a database connection
            if not hasattr(self, 'data_sync_controller') or not self.data_sync_controller or not hasattr(self.data_sync_controller, 'db'):
                print("Warning: No database connection available for market request handler")
                return

            # Create the market request handler with the ESI client
            self.market_request_handler = MarketRequestHandler(
                db_manager=self.data_sync_controller.db,
                esi_client=self.esi_client
            )

            # Connect the status signal to a method that will update the UI
            self.market_request_handler.status_updated.connect(self.update_market_request_status)

            # Start the handler
            self.market_request_handler.start()

            print("Market request handler initialized and started")
        except Exception as e:
            print(f"Error initializing market request handler: {e}")
            import traceback
            print(traceback.format_exc())

    def update_market_request_status(self, message):
        """Update the UI with market request handler status messages."""
        # If we have an execute tab, update its status
        if hasattr(self, 'execute_tab') and self.execute_tab:
            # Check if the execute tab has a method to update status
            if hasattr(self.execute_tab, 'update_status'):
                self.execute_tab.update_status(message)
            else:
                print(f"Market request status: {message}")
        else:
            print(f"Market request status: {message}")




    def _show_transactions_context_menu(self, position):
        """
        Show context menu for the transactions table.

        Args:
            position: Position where the context menu was requested
        """
        print(f"Transactions context menu requested at position: {position}")

        # Get the selected row
        selected_rows = self.transactionsTableView.selectionModel().selectedRows()
        if not selected_rows:
            # Select the row at the position
            index = self.transactionsTableView.indexAt(position)
            if index.isValid():
                self.transactionsTableView.selectRow(index.row())
                selected_rows = self.transactionsTableView.selectionModel().selectedRows()
            else:
                print("No valid row at position, not showing context menu")
                return

        if not selected_rows:
            print("No rows selected, not showing context menu")
            return

        print(f"Selected transaction row: {selected_rows[0].row()}")

        # Create context menu
        context_menu = QMenu(self)

        # Add analyze action
        analyze_action = QAction("Analyze Item", self)
        analyze_action.triggered.connect(self._on_analyze_transaction_item)
        context_menu.addAction(analyze_action)

        # Show the menu
        global_pos = self.transactionsTableView.mapToGlobal(position)
        print(f"Showing transactions context menu at global position: {global_pos}")
        context_menu.exec_(global_pos)

    def _show_orders_context_menu(self, position):
        """
        Show context menu for the orders table.

        Args:
            position: Position where the context menu was requested
        """
        print(f"Orders context menu requested at position: {position}")

        # Get the selected row
        selected_rows = self.ordersTableView.selectionModel().selectedRows()
        if not selected_rows:
            # Select the row at the position
            index = self.ordersTableView.indexAt(position)
            if index.isValid():
                self.ordersTableView.selectRow(index.row())
                selected_rows = self.ordersTableView.selectionModel().selectedRows()
            else:
                print("No valid row at position, not showing context menu")
                return

        if not selected_rows:
            print("No rows selected, not showing context menu")
            return

        print(f"Selected order row: {selected_rows[0].row()}")

        # Create context menu
        context_menu = QMenu(self)

        # Add analyze action
        analyze_action = QAction("Analyze Item", self)
        analyze_action.triggered.connect(self._on_analyze_order_item)
        context_menu.addAction(analyze_action)

        # Show the menu
        global_pos = self.ordersTableView.mapToGlobal(position)
        print(f"Showing orders context menu at global position: {global_pos}")
        context_menu.exec_(global_pos)

    @pyqtSlot(int)
    def _on_tab_changed(self, index):
        """
        Handle tab changed event to set default subtabs.

        Args:
            index: The index of the selected tab
        """
        # Check if the Monitor tab (index 3) was selected
        if index == 3:
            # Set the default subtab to "Active" (index 0)
            self.dataViewTabWidget.setCurrentIndex(0)

    def _setup_net_worth_widget(self):
        """Create and add the net worth widget to the Monitor tab."""
        try:
            # Check if we have the necessary components
            if not hasattr(self, 'data_sync_controller') or \
               not self.data_sync_controller or \
               not hasattr(self.data_sync_controller, 'db') or \
               not self.esi_client:
                print("Warning: Missing components for net worth widget setup.")
                return

            # Create the net worth controller
            net_worth_controller = NetWorthController(
                db_manager=self.data_sync_controller.db,
                esi_client=self.esi_client
            )

            # Create the net worth widget
            self.net_worth_widget = NetWorthWidget()
            self.net_worth_widget.set_controller(net_worth_controller)

            # Connect the refresh signal
            self.net_worth_widget.refresh_requested.connect(self.start_data_sync)

            # Find the Monitor tab
            for i in range(self.tabWidget.count()):
                if self.tabWidget.tabText(i) == "Monitor":
                    monitor_tab = self.tabWidget.widget(i)

                    # Find the scroll area content widget
                    scroll_area = monitor_tab.findChild(QScrollArea, "monitorScrollArea")
                    if scroll_area and scroll_area.widget():
                        scroll_content = scroll_area.widget()
                        layout = scroll_content.layout()
                        if layout:
                            # Find the index of the dataViewTabWidget to place the net worth widget after it
                            data_view_tab_widget = scroll_content.findChild(QWidget, "dataViewTabWidget")
                            if data_view_tab_widget:
                                # Find the index of the dataViewTabWidget in the layout
                                for i in range(layout.count()):
                                    if layout.itemAt(i).widget() == data_view_tab_widget:
                                        # Insert the net worth widget after the dataViewTabWidget
                                        layout.insertWidget(i + 1, self.net_worth_widget)
                                        print("Net worth widget added after dataViewTabWidget in Monitor tab")
                                        break
                                else:
                                    # If we couldn't find the dataViewTabWidget, add to the end
                                    layout.addWidget(self.net_worth_widget)
                                    print("Net worth widget added to the end of Monitor tab layout")
                            else:
                                # If we couldn't find the dataViewTabWidget, add to the end
                                layout.addWidget(self.net_worth_widget)
                                print("Net worth widget added to the end of Monitor tab layout (dataViewTabWidget not found)")
                        else:
                            print("Warning: Could not find layout in Monitor tab scroll content")
                    else:
                        # Fallback to the old method if scroll area not found
                        layout = monitor_tab.layout()
                        if layout:
                            # Add the net worth widget to the end of the layout
                            layout.addWidget(self.net_worth_widget)
                            print("Net worth widget added to the end of Monitor tab (fallback method)")
                        else:
                            print("Warning: Could not find layout in Monitor tab")
                    break
            else:
                print("Warning: Could not find Monitor tab")

            # After setting up the net worth widget, set up the daily profit and top items profit widgets
            self._setup_daily_profit_widget()
            self._setup_top_items_profit_widget()

        except Exception as e:
            print(f"Error setting up net worth widget: {e}")
            import traceback
            print(traceback.format_exc())

    def _setup_daily_profit_widget(self):
        """Create and add the daily profit widget to the Monitor tab."""
        try:
            # Check if we have the necessary components
            if not hasattr(self, 'data_sync_controller') or \
               not self.data_sync_controller or \
               not hasattr(self.data_sync_controller, 'db'):
                print("Warning: Missing components for daily profit widget setup.")
                return

            # Create the daily profit controller
            daily_profit_controller = DailyProfitController(
                db_manager=self.data_sync_controller.db
            )

            # Create the daily profit widget
            self.daily_profit_widget = DailyProfitWidget()
            self.daily_profit_widget.set_controller(daily_profit_controller)

            # Find the Monitor tab
            for i in range(self.tabWidget.count()):
                if self.tabWidget.tabText(i) == "Monitor":
                    monitor_tab = self.tabWidget.widget(i)

                    # Find the scroll area content widget
                    scroll_area = monitor_tab.findChild(QScrollArea, "monitorScrollArea")
                    if scroll_area and scroll_area.widget():
                        scroll_content = scroll_area.widget()
                        layout = scroll_content.layout()
                        if layout:
                            # Add the daily profit widget after the net worth widget
                            if hasattr(self, 'net_worth_widget'):
                                # Find the index of the net worth widget in the layout
                                for i in range(layout.count()):
                                    if layout.itemAt(i).widget() == self.net_worth_widget:
                                        # Insert the daily profit widget after the net worth widget
                                        layout.insertWidget(i + 1, self.daily_profit_widget)
                                        print("Daily profit widget added after net worth widget in Monitor tab")
                                        break
                                else:
                                    # If we couldn't find the net worth widget, add to the end
                                    layout.addWidget(self.daily_profit_widget)
                                    print("Daily profit widget added to the end of Monitor tab layout")
                            else:
                                # If we don't have a net worth widget, add to the end
                                layout.addWidget(self.daily_profit_widget)
                                print("Daily profit widget added to the end of Monitor tab layout (net worth widget not found)")
                        else:
                            print("Warning: Could not find layout in Monitor tab scroll content")
                    else:
                        print("Warning: Could not find scroll area in Monitor tab")
                    break
            else:
                print("Warning: Could not find Monitor tab")

            # Refresh the daily profit data
            self.daily_profit_widget.refresh_data()

        except Exception as e:
            print(f"Error setting up daily profit widget: {e}")
            import traceback
            print(traceback.format_exc())

    def _setup_top_items_profit_widget(self):
        """Create and add the top items profit widget to the Monitor tab."""
        try:
            # Check if we have the necessary components
            if not hasattr(self, 'data_sync_controller') or \
               not self.data_sync_controller or \
               not hasattr(self.data_sync_controller, 'db'):
                print("Warning: Missing components for top items profit widget setup.")
                return

            # Create the top items profit controller
            top_items_profit_controller = TopItemsProfitController(
                db_manager=self.data_sync_controller.db
            )

            # Create the top items profit widget
            self.top_items_profit_widget = TopItemsProfitWidget()
            self.top_items_profit_widget.set_controller(top_items_profit_controller)

            # Find the Monitor tab
            for i in range(self.tabWidget.count()):
                if self.tabWidget.tabText(i) == "Monitor":
                    monitor_tab = self.tabWidget.widget(i)

                    # Find the scroll area content widget
                    scroll_area = monitor_tab.findChild(QScrollArea, "monitorScrollArea")
                    if scroll_area and scroll_area.widget():
                        scroll_content = scroll_area.widget()
                        layout = scroll_content.layout()
                        if layout:
                            # Add the top items profit widget after the daily profit widget
                            if hasattr(self, 'daily_profit_widget'):
                                # Find the index of the daily profit widget in the layout
                                for i in range(layout.count()):
                                    if layout.itemAt(i).widget() == self.daily_profit_widget:
                                        # Insert the top items profit widget after the daily profit widget
                                        layout.insertWidget(i + 1, self.top_items_profit_widget)
                                        print("Top items profit widget added after daily profit widget in Monitor tab")
                                        break
                                else:
                                    # If we couldn't find the daily profit widget, add to the end
                                    layout.addWidget(self.top_items_profit_widget)
                                    print("Top items profit widget added to the end of Monitor tab layout")
                            else:
                                # If we don't have a daily profit widget, add to the end
                                layout.addWidget(self.top_items_profit_widget)
                                print("Top items profit widget added to the end of Monitor tab layout (daily profit widget not found)")
                        else:
                            print("Warning: Could not find layout in Monitor tab scroll content")
                    else:
                        print("Warning: Could not find scroll area in Monitor tab")
                    break
            else:
                print("Warning: Could not find Monitor tab")

            # Refresh the top items profit data
            self.top_items_profit_widget.refresh_data()

            # Connect the daily profit widget's date range selected signal to the top items profit widget
            if hasattr(self, 'daily_profit_widget'):
                self.daily_profit_widget.date_range_selected.connect(self.top_items_profit_widget.update_date_range)

        except Exception as e:
            print(f"Error setting up top items profit widget: {e}")
            import traceback
            print(traceback.format_exc())

    def closeEvent(self, event):
        """ Ensure worker thread is cleaned up on window close. """
        print("Close event triggered. Stopping worker thread...")
        if self.worker_thread.isRunning():
            self.worker_thread.quit()
            self.worker_thread.wait() # Wait for thread to finish

        # Stop the market request handler
        if hasattr(self, 'market_request_handler') and self.market_request_handler:
            self.market_request_handler.stop()
            print("Market request handler stopped")

        # Clean up the Execute tab if it exists
        if hasattr(self, 'execute_tab') and self.execute_tab:
            try:
                self.execute_tab.cleanup()
                print("Execute tab cleaned up")
            except Exception as e:
                print(f"Error cleaning up Execute tab: {e}")

        event.accept()


# --- Main execution ---
# (This part will be moved to main.py)
# if __name__ == '__main__':
#     app = QApplication(sys.argv)
#     window = MainWindow()
#     window.show()
#     sys.exit(app.exec_())
