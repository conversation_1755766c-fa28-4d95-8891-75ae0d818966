"""
Search EVE Online Items

A simple command-line utility to search for EVE Online items in the local database.
"""

import sys
import argparse
from eve_trader.utils.item_database import get_item_database

def main():
    """Main function to search for items."""
    parser = argparse.ArgumentParser(description='Search EVE Online items in the local database.')
    parser.add_argument('search_term', nargs='?', help='The item name to search for')
    parser.add_argument('--id', type=int, help='Look up an item by its ID')
    parser.add_argument('--limit', type=int, default=20, help='Maximum number of results to return (default: 20)')
    parser.add_argument('--count', action='store_true', help='Show the total number of items in the database')
    
    args = parser.parse_args()
    
    # Get the item database instance
    item_db = get_item_database()
    
    # Show item count if requested
    if args.count:
        count = item_db.count_items()
        print(f"Total items in database: {count}")
        if not args.search_term and args.id is None:
            return 0
    
    # Look up by ID if provided
    if args.id is not None:
        item = item_db.get_item_details(args.id)
        if item:
            print(f"Item ID: {item['item_id']}")
            print(f"Name: {item['item_name']}")
            print(f"Group: {item['group_name']} (ID: {item['group_id']})")
            print(f"Category: {item['category_name']} (ID: {item['category_id']})")
            print(f"Published: {bool(item['published'])}")
            if item['market_group_id']:
                print(f"Market Group ID: {item['market_group_id']}")
            print(f"Volume: {item['volume']}")
            print(f"Last Updated: {item['last_updated']}")
        else:
            print(f"No item found with ID {args.id}")
        return 0
    
    # Search by name if provided
    if args.search_term:
        results = item_db.search_items(args.search_term, limit=args.limit)
        if results:
            print(f"Found {len(results)} items matching '{args.search_term}':")
            print(f"{'ID':<10} {'Name':<40} {'Group':<20} {'Category':<20}")
            print("-" * 90)
            for item in results:
                print(f"{item['item_id']:<10} {item['item_name']:<40} {item['group_name']:<20} {item['category_name']:<20}")
        else:
            print(f"No items found matching '{args.search_term}'")
        return 0
    
    # If no search term or ID provided, show usage
    if not args.count:
        parser.print_help()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
