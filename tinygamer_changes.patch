diff --git a/TinyGamer/Games/EveOnline/Sensors/PopupWindow.json b/TinyGamer/Games/EveOnline/Sensors/PopupWindow.json
deleted file mode 100644
index 6107adb..0000000
--- a/TinyGamer/Games/EveOnline/Sensors/PopupWindow.json
+++ /dev/null
@@ -1 +0,0 @@
-{"sensor_name": "PopupWindow", "sensor_type": "OpenCV", "region": {"x": 765, "y": 915, "width": 2077, "height": 989}, "timeout": 1.0, "parent": "", "parent_anchor_point": null, "base64_encoded_image_data": "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", "threshold": 0.8, "vertical_priority": false}
\ No newline at end of file
diff --git a/TinyGamer/Games/EveOnline/Tasks/TaskBuyOrderPriceSet.py b/TinyGamer/Games/EveOnline/Tasks/TaskBuyOrderPriceSet.py
index 29aa241..0d76381 100644
--- a/TinyGamer/Games/EveOnline/Tasks/TaskBuyOrderPriceSet.py
+++ b/TinyGamer/Games/EveOnline/Tasks/TaskBuyOrderPriceSet.py
@@ -11,7 +11,7 @@ class TaskBuyOrderPriceSet(Task):
     def __init__(self, price=None, game_interactor=None):
         super().__init__("BuyOrderPriceSet", game_interactor=game_interactor)
         self.price = price
-    
+
     def execute(self):
         """
         Execute the task to set the price in the buy order window.
@@ -24,28 +24,46 @@ class TaskBuyOrderPriceSet(Task):
                 "Buy order window not found after opening it"
             ):
                 return False, {"error": "Buy order window not found after opening it"}
-            
+
             # Check if the price input field is visible
             if not self.is_sensor_found("BuyOrderWindowPrice"):
                 self.logger.error("Price input field in buy order window not found")
                 return False, {"error": "Price input field in buy order window not found"}
-            
+
             # Get the price input field location
             price_field_location = self.global_state.get("BuyOrderWindowPrice")
-            
+
             # Click on the price input field
             self.interactor.EveSafeClick(x=price_field_location["centerpoint"]["x"], y=price_field_location["centerpoint"]["y"])
             self.wait(0.5)
-            
+
             # Select all items (Ctrl+A)
             self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
             self.interactor.EvePressKeyWithModifier(Key.cmd, "a")
             self.wait(0.5)
-            
+
             # Type the price
             if self.price is not None:
-                self.interactor.type_string(str(self.price))
-                self.wait(0.5)
+                # Log the price for debugging
+                self.logger.info(f"Setting price: {self.price}")
+
+                # Use the specialized price typing method if available
+                if hasattr(self.interactor, 'type_price'):
+                    self.logger.info("Using specialized price typing method")
+                    self.interactor.type_price(self.price)
+                else:
+                    # Fall back to regular typing with explicit formatting
+                    try:
+                        # Format with two decimal places
+                        formatted_price = f"{float(self.price):.2f}"
+                        self.logger.info(f"Formatted price: {formatted_price}")
+                        self.interactor.type_string(formatted_price)
+                    except ValueError:
+                        # If conversion fails, use as is
+                        self.logger.warning(f"Could not format price, using as is: {self.price}")
+                        self.interactor.type_string(str(self.price))
+
+                self.wait(1.0)  # Increased wait time after typing price
                 return True, {}
             else:
                 self.logger.error("No price provided")
diff --git a/TinyGamer/Games/EveOnline/Tasks/TaskMarketWindowPlaceBuyOrder.py b/TinyGamer/Games/EveOnline/Tasks/TaskMarketWindowPlaceBuyOrder.py
index d10ee04..de0b407 100644
--- a/TinyGamer/Games/EveOnline/Tasks/TaskMarketWindowPlaceBuyOrder.py
+++ b/TinyGamer/Games/EveOnline/Tasks/TaskMarketWindowPlaceBuyOrder.py
@@ -11,7 +11,6 @@ from Games.EveOnline.Tasks.TaskInformationWindowMarketDetailsClick import TaskIn
 import logging
 import pyperclip
 
-
 @TaskRegistry.register
 class TaskMarketWindowPlaceBuyOrder(Task):
     """
@@ -22,7 +21,7 @@ class TaskMarketWindowPlaceBuyOrder(Task):
         super().__init__("MarketWindowPlaceBuyOrder", game_interactor=game_interactor)
         self.price = price
         self.quantity = quantity
-    
+
     def execute(self):
         """
         Execute the task sequence for placing a buy order.
@@ -42,72 +41,139 @@ class TaskMarketWindowPlaceBuyOrder(Task):
                         self.logger.info(f"Using quantity from clipboard: {self.quantity}")
             except Exception as e:
                 self.logger.warning(f"Error reading from clipboard: {str(e)}")
+
+            # Try to get price from temporary file
+            try:
+                import tempfile
+                import os
+
+                # Get the temp file path
+                temp_dir = tempfile.gettempdir()
+                price_file_path = os.path.join(temp_dir, "tinygamer_price.txt")
+
+                # Check if the file exists
+                if os.path.exists(price_file_path):
+                    # Read the price from the file
+                    with open(price_file_path, "r") as f:
+                        price_str = f.read().strip()
+
+                    # Validate and use the price
+                    if price_str:
+                        try:
+                            # Convert to float to validate it's a number
+                            float(price_str)
+                            self.price = price_str
+                            self.logger.info(f"Using price from temp file: {self.price}")
+                        except ValueError:
+                            self.logger.warning(f"Invalid price in temp file: {price_str}")
+            except Exception as e:
+                self.logger.warning(f"Error reading price from temp file: {str(e)}")
             # Open the multibuy window
             if not self.run_subtask_with_error_handling(
                 self.create_subtask(TaskMultibuyWindowOpen),
                 "Multibuy window not found after opening it"
             ):
                 return False, {"error": "Multibuy window not found after opening it"}
-            
+
             # Click on the import button
             if not self.run_subtask_with_error_handling(
                 self.create_subtask(TaskMultibuyWindowImportClick),
                 "Failed to click on import button"
             ):
                 return False, {"error": "Failed to click on import button"}
-            
+
             # Confirm the import
             if not self.run_subtask_with_error_handling(
                 self.create_subtask(TaskMultibuyWindowImportConfirm),
                 "Failed to confirm import"
             ):
                 return False, {"error": "Failed to confirm import"}
-            
+
             # Click on the first item
             if not self.run_subtask_with_error_handling(
                 self.create_subtask(TaskMultiBuyWindowFirstItemClick),
                 "Failed to click on first item"
             ):
                 return False, {"error": "Failed to click on first item"}
-            
+
             # Click market details
             if not self.run_subtask_with_error_handling(
                 self.create_subtask(TaskInformationWindowMarketDetailsClick),
                 "Failed to click on market details"
             ):
                 return False, {"error": "Failed to click on market details"}
-            
+
             # Click on the buy order button
             if not self.run_subtask_with_error_handling(
                 self.create_subtask(TaskMarketBuyOrderClick),
                 "Failed to click on buy order button"
             ):
                 return False, {"error": "Failed to click on buy order button"}
-            
+
             # Set the price in the buy order window
             if not self.run_subtask_with_error_handling(
                 self.create_subtask(TaskBuyOrderPriceSet, price=self.price),
                 "Failed to set price in buy order window"
             ):
                 return False, {"error": "Failed to set price in buy order window"}
-            
+
             # Set the quantity in the buy order window
             if not self.run_subtask_with_error_handling(
                 self.create_subtask(TaskBuyOrderQuantitySet, quantity=self.quantity),
                 "Failed to set quantity in buy order window"
             ):
                 return False, {"error": "Failed to set quantity in buy order window"}
-            
+
             # Place the buy order
             if not self.run_subtask_with_error_handling(
                 self.create_subtask(TaskBuyOrderPlaceOrder),
                 "Failed to place buy order"
             ):
+                self._write_completion_status(False, "Failed to place buy order")
                 return False, {"error": "Failed to place buy order"}
-            
+
+            # Write completion status to temp file
+            self._write_completion_status(True, "Buy order placed successfully")
+
             return True, {}
         except CancelException as ex:
+            self._write_completion_status(False, str(ex))
             return False, {"error": str(ex)}
         except Exception as e:
             logging.exception(f"Exception in TaskMarketWindowPlaceBuyOrder.execute:")
+            self._write_completion_status(False, str(e))
             return False, {"error": str(e)}
+
+    def _write_completion_status(self, success: bool, message: str):
+        """
+        Write task completion status to a temporary file for TinyTrader to read.
+
+        Args:
+            success: Whether the task completed successfully
+            message: Status message
+        """
+        try:
+            import tempfile
+            import os
+            import json
+            import time
+
+            # Get the temp file path
+            temp_dir = tempfile.gettempdir()
+            status_file_path = os.path.join(temp_dir, "tinygamer_status.json")
+
+            # Create status data
+            status_data = {
+                "task": self.name,
+                "success": success,
+                "message": message,
+                "timestamp": time.time()
+            }
+
+            # Write status to file
+            with open(status_file_path, "w") as f:
+                json.dump(status_data, f)
+
+            self.logger.info(f"Wrote completion status to {status_file_path}: {success}, {message}")
+        except Exception as e:
+            self.logger.error(f"Error writing completion status: {str(e)}")
\ No newline at end of file
diff --git a/TinyGamer/Games/EveOnline/Tasks/TaskPopupWindowClose.py b/TinyGamer/Games/EveOnline/Tasks/TaskPopupWindowClose.py
new file mode 100644
index 0000000..4a84837
--- /dev/null
+++ b/TinyGamer/Games/EveOnline/Tasks/TaskPopupWindowClose.py
@@ -0,0 +1,37 @@
+from task import Task, TaskRegistry, CancelException
+from Games.EveOnline.Tasks.TaskOpenSpaceClick import TaskOpenSpaceClick
+from pynput.keyboard import Key
+import logging
+
+@TaskRegistry.register
+class TaskPopupWindowClose(Task):
+    """
+    Task for closing the popup window.
+    """
+    def __init__(self, game_interactor=None):
+        super().__init__("PopupWindowClose", game_interactor=game_interactor)
+    
+    def execute(self):
+        """
+        Execute the task to close the popup window.
+        Returns (success, metadata) tuple.
+        """
+        try:
+            if not self.is_sensor_found("PopupWindow"):
+                return True, {}
+            
+            popup_window_location = self.global_state.get("PopupWindow")
+
+            # Click on the popup window
+            self.interactor.EveSafeClick(x=popup_window_location["centerpoint"]["x"], y=popup_window_location["centerpoint"]["y"])
+            self.wait(1)
+
+            if self.is_sensor_found("PopupWindow"):
+                self.logger.error("Popup window didn't close after clicking yes")
+                return False, {"error": "Popup window not found"}
+            return True, {}
+        except CancelException as ex:
+            return False, {"error": str(ex)}
+        except Exception as e:
+            logging.exception(f"Exception in TaskPopupWindowClose.execute:")
+            return False, {"error": str(e)}
diff --git a/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryClose.py b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryClose.py
new file mode 100644
index 0000000..974f394
--- /dev/null
+++ b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryClose.py
@@ -0,0 +1,34 @@
+from task import Task, TaskRegistry, CancelException
+from pynput.keyboard import Key
+import logging
+
+@TaskRegistry.register
+class TaskStationInventoryClose(Task):
+    """
+    Task for closing the station inventory window.
+    """
+    def __init__(self, game_interactor=None):
+        super().__init__("StationInventoryClose", game_interactor=game_interactor)
+    
+    def execute(self):
+        """
+        Execute the task to close the station inventory window.
+        Returns (success, metadata) tuple.
+        """
+        try:
+            if not self.is_sensor_found("StationInventoryWindowOpen"):
+                return True, {}
+            
+            self.interactor.EvePressKeyWithModifier(Key.alt, "c")
+            self.wait(1.0)
+            
+            if self.is_sensor_found("StationInventoryWindowOpen"):
+                self.logger.error("Station inventory window didn't close after pressing Alt+C")
+                return False, {"error": "Station inventory window didn't close after pressing Alt+C"}
+            return True, {}
+        except CancelException as ex:
+            return False, {"error": str(ex)}
+        except Exception as e:
+            logging.exception(f"Exception in TaskStationInventoryClose.execute:")
+            return False, {"error": str(e)}
+
diff --git a/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryItemSell.py b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryItemSell.py
new file mode 100644
index 0000000..1585793
--- /dev/null
+++ b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryItemSell.py
@@ -0,0 +1,127 @@
+from task import Task, TaskRegistry, CancelException
+from pynput.keyboard import Key
+import logging
+
+@TaskRegistry.register
+class TaskStationInventoryItemSell(Task):
+    """
+    Task for selling an item in the station inventory with specified quantity and price.
+    """
+    def __init__(self, quantity=None, price=None, game_interactor=None):
+        super().__init__("StationInventoryItemSell", game_interactor=game_interactor)
+        self.quantity = quantity
+        self.price = price
+
+    def execute(self):
+        """
+        Execute the task to sell an item with specified quantity and price.
+        Returns (success, metadata) tuple.
+        """
+        try:
+            # Check if the sell item window is open
+            if not self.is_sensor_found("SellItemWindow"):
+                self.logger.error("Sell item window not found")
+                return False, {"error": "Sell item window not found"}
+
+            # Check if the quantity field is visible
+            if not self.is_sensor_found("SellItemWindowQuantity"):
+                self.logger.error("Quantity field in sell item window not found")
+                return False, {"error": "Quantity field in sell item window not found"}
+
+            # Get the quantity field location
+            quantity_field_location = self.global_state.get("SellItemWindowQuantity")
+
+            # Click on the quantity field
+            self.interactor.EveSafeClick(x=quantity_field_location["centerpoint"]["x"], y=quantity_field_location["centerpoint"]["y"])
+            self.wait(0.5)
+
+            # Select all text (Alt+A)
+            self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
+            self.interactor.EvePressKeyWithModifier(Key.cmd, "a")
+            self.wait(0.5)
+
+            # Type the quantity
+            if self.quantity is not None:
+                self.interactor.type_string(str(self.quantity))
+                self.wait(0.5)
+            else:
+                self.logger.error("No quantity provided")
+                return False, {"error": "No quantity provided"}
+
+            # Check if the price field is visible
+            if not self.is_sensor_found("SellItemWindowPrice"):
+                self.logger.error("Price field in sell item window not found")
+                return False, {"error": "Price field in sell item window not found"}
+
+            # Get the price field location
+            price_field_location = self.global_state.get("SellItemWindowPrice")
+
+            # Click on the price field
+            self.interactor.EveSafeClick(x=price_field_location["centerpoint"]["x"], y=price_field_location["centerpoint"]["y"])
+            self.wait(0.5)
+
+            # Select all text (Alt+A)
+            self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
+            self.interactor.EvePressKeyWithModifier(Key.cmd, "a")
+            self.wait(0.5)
+
+            # Type the price
+            if self.price is not None:
+                # Log the price for debugging
+                self.logger.info(f"Setting price: {self.price}")
+
+                # Validate and format the price
+                try:
+                    # Convert to float to validate
+                    price_float = float(self.price)
+
+                    # Ensure price is positive
+                    if price_float <= 0:
+                        self.logger.warning(f"Price is zero or negative: {price_float}, using minimum price")
+                        price_float = 0.01  # Set a minimum price
+
+                    # Format with two decimal places
+                    formatted_price = f"{price_float:.2f}"
+                    self.logger.info(f"Validated and formatted price: {formatted_price}")
+
+                    # Use the specialized price typing method if available
+                    if hasattr(self.interactor, 'type_price'):
+                        self.logger.info("Using specialized price typing method")
+                        self.interactor.type_price(formatted_price)
+                    else:
+                        # Fall back to regular typing
+                        self.interactor.type_string(formatted_price)
+                except ValueError:
+                    # If conversion fails, use a safe default
+                    self.logger.warning(f"Could not parse price '{self.price}', using minimum price")
+                    self.interactor.type_string("0.01")
+
+                self.wait(0.5)
+            else:
+                self.logger.error("No price provided")
+                return False, {"error": "No price provided"}
+
+            # Check if the sell button is visible
+            if not self.is_sensor_found("SellItemWindowSellButton"):
+                self.logger.error("Sell button in sell item window not found")
+                return False, {"error": "Sell button in sell item window not found"}
+
+            # Get the sell button location
+            sell_button_location = self.global_state.get("SellItemWindowSellButton")
+
+            # Click on the sell button
+            self.interactor.EveSafeClick(x=sell_button_location["centerpoint"]["x"], y=sell_button_location["centerpoint"]["y"])
+            self.wait(1.0)
+
+            # If a warning appears, click on it
+            if self.is_sensor_found("PopupWindowOpen"):
+                warning_location = self.global_state.get("PopupWindowOpen")
+                self.interactor.EveSafeClick(x=warning_location["centerpoint"]["x"], y=warning_location["centerpoint"]["y"])
+                self.wait(1.0)
+
+            return True, {}
+        except CancelException as ex:
+            return False, {"error": str(ex)}
+        except Exception as e:
+            logging.exception(f"Exception in TaskStationInventoryItemSell.execute:")
+            return False, {"error": str(e)}
diff --git a/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryOpen.py b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryOpen.py
new file mode 100644
index 0000000..0209a0f
--- /dev/null
+++ b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryOpen.py
@@ -0,0 +1,39 @@
+from task import Task, TaskRegistry, CancelException
+from Games.EveOnline.Tasks.TaskOpenSpaceClick import TaskOpenSpaceClick
+from pynput.keyboard import Key
+import logging
+
+@TaskRegistry.register
+class TaskStationInventoryOpen(Task):
+    """
+    Task for opening the station inventory window.
+    """
+    def __init__(self, game_interactor=None):
+        super().__init__("StationInventoryOpen", game_interactor=game_interactor)
+    
+    def execute(self):
+        """
+        Execute the task to open the station inventory window.
+        Returns (success, metadata) tuple.
+        """
+        try:
+            if not self.run_subtask_with_error_handling(
+                self.create_subtask(TaskOpenSpaceClick),
+                "Station inventory window not found after opening it"
+            ):
+                return False, {"error": "Station inventory window not found after opening it"}
+            if self.is_sensor_found("StationInventoryWindowOpen"):
+                return True, {}
+            
+            self.interactor.EvePressKeyWithModifier(Key.alt, "c")
+            self.wait(1.0)
+            
+            if not self.is_sensor_found("StationInventoryWindowOpen"):
+                self.logger.error("Station inventory window didn't appear after pressing Alt+C")
+                return False, {"error": "Station inventory window not found"}
+            return True, {}
+        except CancelException as ex:
+            return False, {"error": str(ex)}
+        except Exception as e:
+            logging.exception(f"Exception in TaskStationInventoryOpen.execute:")
+            return False, {"error": str(e)}
diff --git a/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryOpenSubmenu.py b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryOpenSubmenu.py
new file mode 100644
index 0000000..413fa56
--- /dev/null
+++ b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventoryOpenSubmenu.py
@@ -0,0 +1,55 @@
+from task import Task, TaskRegistry, CancelException
+from Games.EveOnline.Tasks.TaskStationInventoryOpen import TaskStationInventoryOpen
+import logging
+
+@TaskRegistry.register
+class TaskStationInventoryOpenSubmenu(Task):
+    """
+    Task for opening the submenu for the first item in the station inventory.
+    """
+    def __init__(self, game_interactor=None):
+        super().__init__("StationInventoryOpenSubmenu", game_interactor=game_interactor)
+    
+    def execute(self):
+        """
+        Execute the task to open the submenu for the first item in the station inventory.
+        Returns (success, metadata) tuple.
+        """
+        try:
+            # First make sure the station inventory window is open
+            if not self.run_subtask_with_error_handling(
+                self.create_subtask(TaskStationInventoryOpen),
+                "Station inventory window not found after opening it"
+            ):
+                return False, {"error": "Station inventory window not found after opening it"}
+            
+            # Check if the first item is visible
+            if not self.is_sensor_found("StationInventoryWindowFirstItem"):
+                self.logger.error("First item in station inventory window not found")
+                return False, {"error": "First item in station inventory window not found"}
+            
+            # Get the first item location
+            first_item_location = self.global_state.get("StationInventoryWindowFirstItem")
+            
+            # Right-click on the first item
+            self.interactor.EveSafeClickRight(x=first_item_location["centerpoint"]["x"], y=first_item_location["centerpoint"]["y"])
+            self.wait(1.0)
+            
+            # Check if the sell menu option is visible
+            if not self.is_sensor_found("StationInventoryWindowSellMenu"):
+                self.logger.error("Sell menu option not found")
+                return False, {"error": "Sell menu option not found"}
+            
+            # Get the sell menu option location
+            sell_menu_location = self.global_state.get("StationInventoryWindowSellMenu")
+            
+            # Left-click on the sell menu option
+            self.interactor.EveSafeClick(x=sell_menu_location["centerpoint"]["x"], y=sell_menu_location["centerpoint"]["y"])
+            self.wait(1.0)
+            
+            return True, {}
+        except CancelException as ex:
+            return False, {"error": str(ex)}
+        except Exception as e:
+            logging.exception(f"Exception in TaskStationInventoryOpenSubmenu.execute:")
+            return False, {"error": str(e)}
diff --git a/TinyGamer/Games/EveOnline/Tasks/TaskStationInventorySellAllItems.py b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventorySellAllItems.py
new file mode 100644
index 0000000..0571b7f
--- /dev/null
+++ b/TinyGamer/Games/EveOnline/Tasks/TaskStationInventorySellAllItems.py
@@ -0,0 +1,253 @@
+from task import Task, TaskRegistry, CancelException
+from Games.EveOnline.Tasks.TaskStationInventoryOpen import TaskStationInventoryOpen
+from Games.EveOnline.Tasks.TaskStationInventoryOpenSubmenu import TaskStationInventoryOpenSubmenu
+from Games.EveOnline.Tasks.TaskStationInventoryItemSell import TaskStationInventoryItemSell
+from Games.EveOnline.Tasks.TaskStationInventoryClose import TaskStationInventoryClose
+import pynput.keyboard
+from pynput.keyboard import Key, KeyCode
+import logging
+import pyperclip
+import os
+import tempfile
+import json
+import time
+
+@TaskRegistry.register
+class TaskStationInventorySellAllItems(Task):
+    """
+    Task for selling all items in the station inventory.
+    This is a composite task that runs other tasks in sequence.
+    """
+    def __init__(self, game_interactor=None):
+        super().__init__("StationInventorySellAllItems", game_interactor=game_interactor)
+
+    def execute(self):
+        """
+        Execute the task sequence for selling all items in the station inventory.
+        Returns (success, metadata) tuple.
+        """
+        while True:
+            try:
+                # First make sure the station inventory window is open
+                if not self.run_subtask_with_error_handling(
+                    self.create_subtask(TaskStationInventoryOpen),
+                    "Station inventory window not found after opening it"
+                ):
+                    return False, {"error": "Station inventory window not found after opening it"}
+
+                # Check if there's a container for the first item
+                has_container = self.is_sensor_found("StationInventoryWindowContainer")
+                if has_container:
+                    self.logger.info("Container found in station inventory")
+                    return True, {"message": "Container found, manual handling required"}
+
+                # Check if there's a first item
+                if not self.is_sensor_found("StationInventoryWindowFirstItem"):
+                    self.logger.info("No items found in station inventory")
+                    return True, {"message": "No items found in station inventory"}
+
+                # Get the first item location
+                first_item_location = self.global_state.get("StationInventoryWindowFirstItem")
+
+                # Click on the first item
+                self.interactor.EveSafeClick(x=first_item_location["centerpoint"]["x"], y=first_item_location["centerpoint"]["y"])
+                self.wait(0.5)
+
+                # Clear the clipboard before copying
+                try:
+                    pyperclip.copy("")
+                    self.logger.info("Cleared clipboard")
+                except Exception as e:
+                    self.logger.warning(f"Failed to clear clipboard: {str(e)}")
+
+                # Copy the item name and quantity to clipboard (Alt+C)
+                self.interactor.EvePressKeyWithModifier(Key.ctrl, "c")
+                self.interactor.EvePressKeyWithModifier(Key.cmd, "c")
+                self.wait(1.0)  # Increased wait time to ensure clipboard is updated
+
+                # Get the item name and quantity from clipboard
+                max_retries = 3
+                clipboard_text = ""
+
+                for retry in range(max_retries):
+                    try:
+                        clipboard_text = pyperclip.paste()
+                        self.logger.info(f"Clipboard content (attempt {retry+1}): {clipboard_text}")
+
+                        # Check if the clipboard contains what looks like an item and quantity
+                        parts = clipboard_text.rsplit(" ", 1)
+                        if len(parts) == 2 and parts[1].isdigit():
+                            self.logger.info(f"Valid clipboard format detected on attempt {retry+1}")
+                            break
+
+                        # If not valid, try again
+                        self.logger.warning(f"Invalid clipboard format on attempt {retry+1}, retrying...")
+                        self.wait(0.5)
+                        self.interactor.EvePressKeyWithModifier(Key.ctrl, "c")
+                        self.interactor.EvePressKeyWithModifier(Key.cmd, "c")                        
+                        self.wait(1.0)
+                    except Exception as e:
+                        self.logger.warning(f"Error reading clipboard on attempt {retry+1}: {str(e)}")
+                        self.wait(0.5)
+
+                if not clipboard_text:
+                    self.logger.error("Failed to copy item information to clipboard after multiple attempts")
+                    return False, {"error": "Failed to copy item information to clipboard after multiple attempts"}
+
+                # Parse the clipboard text to get item name and quantity
+                # Format is expected to be "ItemName Quantity"
+                try:
+                    # First, check if the clipboard text is too long (likely log data)
+                    if len(clipboard_text) > 500:  # Arbitrary limit to catch log data
+                        self.logger.error(f"Clipboard content too long, likely not item data. Length: {len(clipboard_text)}")
+                        return False, {"error": "Clipboard content too long, likely not item data"}
+
+                    # Try to extract item name and quantity using regex (primary method)
+                    import re
+                    matches = re.findall(r'(.*?)(\d+)$', clipboard_text.strip())
+                    if matches:
+                        item_name = matches[0][0].strip()
+                        quantity = matches[0][1]
+                        self.logger.info(f"Item: {item_name}, Quantity: {quantity}")
+                    else:
+                        # Try the alternative approach - splitting by last space
+                        parts = clipboard_text.rsplit(" ", 1)
+                        if len(parts) == 2 and parts[1].isdigit():
+                            item_name = parts[0]
+                            quantity = parts[1]
+                            self.logger.info(f"Item (alternative parsing): {item_name}, Quantity: {quantity}")
+                        else:
+                            self.logger.error(f"Invalid clipboard format: {clipboard_text}")
+                            return False, {"error": f"Invalid clipboard format: {clipboard_text}"}
+                except Exception as e:
+                    self.logger.error(f"Error parsing clipboard: {str(e)}")
+                    return False, {"error": f"Error parsing clipboard: {str(e)}"}
+
+                # Communicate with TinyMarket to get the appropriate sell price
+                try:
+                    # Create a request file for TinyMarket
+                    temp_dir = tempfile.gettempdir()
+                    request_file_path = os.path.join(temp_dir, "tinygamer_market_request.json")
+
+                    request_data = {
+                        "action": "get_sell_price",
+                        "item_name": item_name,
+                        "timestamp": time.time()
+                    }
+
+                    with open(request_file_path, "w") as f:
+                        json.dump(request_data, f)
+
+                    self.logger.info(f"Wrote market request to {request_file_path}")
+
+                    # Wait for TinyMarket to process the request and provide a price
+                    max_wait_time = 30  # seconds - increased to allow time for fetching market data
+                    start_time = time.time()
+                    price = None
+
+                    response_file_path = os.path.join(temp_dir, "tinygamer_market_response.json")
+
+                    while time.time() - start_time < max_wait_time:
+                        if os.path.exists(response_file_path):
+                            try:
+                                with open(response_file_path, "r") as f:
+                                    response_data = json.load(f)
+
+                                if "price" in response_data:
+                                    price = response_data["price"]
+                                    self.logger.info(f"Got price from TinyMarket: {price}")
+
+                                    # Validate the price is a valid number
+                                    try:
+                                        price_float = float(price)
+                                        if price_float <= 0:
+                                            self.logger.warning(f"Received zero or negative price: {price_float}, using minimum price")
+                                            price = "0.01"  # Set a minimum price
+                                        else:
+                                            # Format with exactly 2 decimal places
+                                            price = f"{price_float:.2f}"
+                                            self.logger.info(f"Validated and formatted price: {price}")
+                                    except ValueError:
+                                        self.logger.warning(f"Invalid price format received: {price}, using minimum price")
+                                        price = "0.01"  # Default minimum price
+
+                                    break
+                            except Exception as e:
+                                self.logger.warning(f"Error reading response file: {str(e)}")
+
+                        self.wait(0.5)
+
+                    if price is None:
+                        self.logger.error("Failed to get price from TinyMarket")
+                        return False, {"error": "Failed to get price from TinyMarket"}
+                except Exception as e:
+                    self.logger.error(f"Error communicating with TinyMarket: {str(e)}")
+                    return False, {"error": f"Error communicating with TinyMarket: {str(e)}"}
+
+                # Open the submenu for the first item
+                if not self.run_subtask_with_error_handling(
+                    self.create_subtask(TaskStationInventoryOpenSubmenu),
+                    "Failed to open submenu for first item"
+                ):
+                    return False, {"error": "Failed to open submenu for first item"}
+
+                self.wait(3.0)
+
+                # Sell the item with the specified quantity and price
+                if not self.run_subtask_with_error_handling(
+                    self.create_subtask(TaskStationInventoryItemSell, quantity=quantity, price=price),
+                    "Failed to sell item"
+                ):
+                    return False, {"error": "Failed to sell item"}
+
+                # Write completion status to temp file
+                self._write_completion_status(True, f"Sold item: {item_name}, Quantity: {quantity}, Price: {price}")
+
+                # Close the station inventory window
+                if not self.run_subtask_with_error_handling(
+                    self.create_subtask(TaskStationInventoryClose),
+                    "Failed to close station inventory window"
+                ):
+                    return False, {"error": "Failed to close station inventory window"}
+
+            except CancelException as ex:
+                self._write_completion_status(False, str(ex))
+                return False, {"error": str(ex)}
+            except Exception as e:
+                logging.exception(f"Exception in TaskStationInventorySellAllItems.execute:")
+                self._write_completion_status(False, str(e))
+                return False, {"error": str(e)}
+
+    def _write_completion_status(self, success: bool, message: str):
+        """
+        Write task completion status to a temporary file for TinyTrader to read.
+
+        Args:
+            success: Whether the task completed successfully
+            message: Status message
+        """
+        try:
+            import tempfile
+            import os
+            import json
+            import time
+
+            # Get the temp file path
+            temp_dir = tempfile.gettempdir()
+            status_file_path = os.path.join(temp_dir, "tinygamer_status.json")
+
+            # Create status data
+            status_data = {
+                "task": self.name,
+                "success": success,
+                "message": message,
+                "timestamp": time.time()
+            }
+
+            # Write status to file
+            with open(status_file_path, "w") as f:
+                json.dump(status_data, f)
+
+            self.logger.info(f"Wrote completion status to {status_file_path}: {success}, {message}")
+        except Exception as e:
+            self.logger.error(f"Error writing completion status: {str(e)}")
diff --git a/TinyGamer/director.py b/TinyGamer/director.py
index 04bf725..79d5e36 100644
--- a/TinyGamer/director.py
+++ b/TinyGamer/director.py
@@ -50,10 +50,11 @@ class Director:
     Root application-level class that manages threads and windows.
     Handles task execution, global state, and UI interaction.
     """
-    def __init__(self, camera_device_id=None, init_webcam=True):
+    def __init__(self, camera_device_id=None, init_webcam=True, no_status=False):
         self.app = QApplication(sys.argv)
         self.global_state = GlobalState()
         self.game_interactor = GameInteractor()
+        self.no_status = no_status
 
         # Load camera device ID from config if not provided
         if camera_device_id is None:
@@ -85,9 +86,12 @@ class Director:
         # Set up logging
         if not hasattr(self, 'logger'):
             self.logger = logging.getLogger("Director")
-        self.log_handler = LogHandler(self.signal_emitter)
-        self.log_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
-        logging.getLogger().addHandler(self.log_handler)
+
+        # Only add the log handler if we're showing the status window
+        if not self.no_status:
+            self.log_handler = LogHandler(self.signal_emitter)
+            self.log_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
+            logging.getLogger().addHandler(self.log_handler)
 
         # Initialize webcam frame grabber if requested
         if init_webcam:
@@ -97,8 +101,15 @@ class Director:
         self._load_tasks()
         self._load_sensors()
 
-        # Create the UI
-        self._create_ui()
+        # Create the UI if not in headless mode
+        if not self.no_status:
+            self._create_ui()
+        else:
+            # Create a minimal QMainWindow to keep the QApplication running
+            self.main_window = QMainWindow()
+            self.main_window.resize(1, 1)  # Minimal size
+            self.main_window.setWindowTitle("TinyGamer (Headless)")
+            self.logger.info("Running in headless mode (no status window)")
 
         # Start the task execution thread
         self.task_thread = threading.Thread(target=self._task_executor, daemon=True)
@@ -157,20 +168,26 @@ class Director:
 
     def update_log(self, message: str):
         """Update the log view with a new message."""
-        self.log_view.append(message)
-        # Auto-scroll to bottom
-        scrollbar = self.log_view.verticalScrollBar()
-        scrollbar.setValue(scrollbar.maximum())
+        if not self.no_status and hasattr(self, 'log_view'):
+            self.log_view.append(message)
+            # Auto-scroll to bottom
+            scrollbar = self.log_view.verticalScrollBar()
+            scrollbar.setValue(scrollbar.maximum())
 
     def update_status(self, task_name: str, status: str):
         """Update the status display."""
-        if task_name:
-            self.status_label.setText(f"{task_name}: {status}")
-        else:
-            self.status_label.setText(status)
+        if not self.no_status and hasattr(self, 'status_label'):
+            if task_name:
+                self.status_label.setText(f"{task_name}: {status}")
+            else:
+                self.status_label.setText(status)
 
     def update_ui(self):
         """Periodic UI update."""
+        # Skip UI updates in headless mode
+        if self.no_status:
+            return
+
         # Update UI elements based on current state
         if self.is_paused:
             self.pause_button.setText("Resume")
@@ -320,7 +337,12 @@ class Director:
 
     def run(self):
         """Start the application main loop."""
-        self.main_window.show()
+        if not self.no_status:
+            self.main_window.show()
+        else:
+            # In headless mode, we still need a minimal window to keep the QApplication running
+            # but we can hide it
+            self.main_window.hide()
         return self.app.exec_()
 
     def _setup_keyboard_listener(self):
@@ -329,21 +351,21 @@ class Director:
         self.keyboard_listener = keyboard.Listener(on_press=self._on_key_press)
         self.keyboard_listener.daemon = True
         self.keyboard_listener.start()
-        self.logger.info("Keyboard listener started - Press '5' to exit and '6' to pause/resume")
+        self.logger.info("Keyboard listener started - Press 'End' to exit and 'Home' to pause/resume")
 
     def _on_key_press(self, key):
         """Handle key press events for hotkeys.
         Can be overridden by subclasses to add more hotkeys.
         """
         try:
-            # Check for the '5' key
-            if hasattr(key, 'char') and key.char == '5':
-                self.logger.info("Hotkey '5' pressed - initiating clean shutdown")
+            # Check for the 'End' key (previously '5')
+            if key == keyboard.Key.end:
+                self.logger.info("Hotkey 'End' pressed - initiating clean shutdown")
                 # Trigger clean shutdown from the main thread
                 self.app.quit()
-            # Check for the '6' key
-            if hasattr(key, 'char') and key.char == '6':
-                self.logger.info("Hotkey '6' pressed - toggling pause state")
+            # Check for the 'Home' key (previously '6')
+            if key == keyboard.Key.home:
+                self.logger.info("Hotkey 'Home' pressed - toggling pause state")
                 self.toggle_pause()
                 self.global_state.set('paused', self.is_paused)
         except Exception as e:
@@ -382,8 +404,10 @@ class Director:
         # Stop webcam grabber if running
         if hasattr(self, "webcam_grabber") and self.webcam_grabber is not None:
             self.webcam_grabber.stop()
-        # Final cleanup
-        logging.getLogger().removeHandler(self.log_handler)
+
+        # Final cleanup - only remove log handler if it was added
+        if not self.no_status and hasattr(self, 'log_handler'):
+            logging.getLogger().removeHandler(self.log_handler)
 
 
 if __name__ == "__main__":
diff --git a/TinyGamer/eve_director.py b/TinyGamer/eve_director.py
index 3fbb271..91b2105 100644
--- a/TinyGamer/eve_director.py
+++ b/TinyGamer/eve_director.py
@@ -1,5 +1,6 @@
 import os
 import logging
+import pynput
 from PyQt5.QtCore import Qt
 from director import Director
 from task import TaskRegistry
@@ -11,24 +12,29 @@ class EveDirector(Director):
     Special director class for Eve Online automation.
     Extends the base Director with Eve-specific hotkeys and task loading.
     """
-    def __init__(self):
+    def __init__(self, no_preview=False, no_status=False):
         # Load camera device ID from config
         config_manager = ConfigManager()
         camera_device_id = config_manager.get_camera_device_number()
 
         # Initialize with device_id from config but don't init webcam yet
-        super().__init__(camera_device_id=camera_device_id, init_webcam=False)
+        super().__init__(camera_device_id=camera_device_id, init_webcam=False, no_status=no_status)
 
-        # Now that QApplication is initialized through the parent constructor,
-        # we can safely create and show the camera preview dialog
-        camera_dialog = CameraSelectionDialog()
-        if camera_dialog.exec_():
-            # Get the possibly updated camera ID from the dialog
-            camera_device_id = camera_dialog.get_selected_camera_id()
+        self.logger = logging.getLogger("EveDirector")
+
+        if no_preview or no_status:
+            # Skip camera preview dialog and use the camera ID from config
+            self.logger.info(f"Skipping camera preview dialog (--no-preview or --no-status flag used)")
+        else:
+            # Now that QApplication is initialized through the parent constructor,
+            # we can safely create and show the camera preview dialog
+            camera_dialog = CameraSelectionDialog()
+            if camera_dialog.exec_():
+                # Get the possibly updated camera ID from the dialog
+                camera_device_id = camera_dialog.get_selected_camera_id()
 
         # Now initialize the webcam with the camera ID
         self.init_webcam_grabber(device_id=camera_device_id)
-        self.logger = logging.getLogger("EveDirector")
         self.logger.info(f"Eve Director initialized with camera ID: {camera_device_id}")
 
     def _get_task_directories(self):
@@ -51,24 +57,29 @@ class EveDirector(Director):
         Handle Eve-specific hotkeys in addition to base Director hotkeys.
         """
         try:
-            # Check for the '1' key to launch asteroid targeting task
-            if hasattr(key, 'char') and key.char == '1':
-                self.logger.info("Hotkey '1' pressed - starting TaskMining")
-                success, metadata = self.run_task("TaskMiningLowSec")
-                if not success:
-                    self.logger.error(f"Failed to run TaskMining: {metadata}")
-            # Check for the '2' key to launch auto travel task
-            elif hasattr(key, 'char') and key.char == '2':
-                self.logger.info("Hotkey '2' pressed - starting TaskAutoTravel")
-                success, metadata = self.run_task("TaskAutoTravel")
-                if not success:
-                    self.logger.error(f"Failed to run TaskAutoTravel: {metadata}")
-            # Check for the '3' key to launch buy order task
-            elif hasattr(key, 'char') and key.char == '3':
-                self.logger.info("Hotkey '3' pressed - starting TaskMarketWindowPlaceBuyOrder")
+            # # Check for the '1' key to launch asteroid targeting task
+            # if hasattr(key, 'char') and key.char == '1':
+            #     self.logger.info("Hotkey '1' pressed - starting TaskMining")
+            #     success, metadata = self.run_task("TaskMiningLowSec")
+            #     if not success:
+            #         self.logger.error(f"Failed to run TaskMining: {metadata}")
+            # # Check for the '2' key to launch auto travel task
+            # elif hasattr(key, 'char') and key.char == '2':
+            #     self.logger.info("Hotkey '2' pressed - starting TaskAutoTravel")
+            #     success, metadata = self.run_task("TaskAutoTravel")
+            #     if not success:
+            #         self.logger.error(f"Failed to run TaskAutoTravel: {metadata}")
+            # # Check for the 'page up' key to launch buy order task
+            if key == pynput.keyboard.Key.page_up:
+                self.logger.info("Hotkey 'page up' pressed - starting TaskMarketWindowPlaceBuyOrder")
                 success, metadata = self.run_task("TaskMarketWindowPlaceBuyOrder")
                 if not success:
                     self.logger.error(f"Failed to run TaskMarketWindowPlaceBuyOrder: {metadata}")
+            elif key == pynput.keyboard.Key.page_down:
+                self.logger.info("Hotkey 'page down' pressed - starting TaskStationInventorySellAllItems")
+                success, metadata = self.run_task("TaskStationInventorySellAllItems")
+                if not success:
+                    self.logger.error(f"Failed to run TaskStationInventorySellAllItems: {metadata}")
             else:
                 # Call the parent class's key handler for other keys
                 super()._on_key_press(key)
diff --git a/TinyGamer/game_interactor.py b/TinyGamer/game_interactor.py
index 8e5308b..e33dcdc 100644
--- a/TinyGamer/game_interactor.py
+++ b/TinyGamer/game_interactor.py
@@ -5,11 +5,11 @@ import pyautogui
 
 class GameInteractor:
     """Utility class for interacting with games via mouse and keyboard input"""
-    
+
     def __init__(self):
         self.mouse = MouseController()
         self.keyboard = KeyboardController()
-        
+
     def _scale_xy(self, x, y):
         """Scale 4k coordinates (3840x2160) to current screen size."""
         screenshot = pyautogui.screenshot()
@@ -26,7 +26,7 @@ class GameInteractor:
         """Move the mouse to the specified coordinates (input in 4k, scaled to current screen)"""
         x, y = self._scale_xy(x, y)
         self.mouse.position = (x, y)
-        
+
     def click(self, button=Button.left):
         """Click the specified mouse button"""
         self.mouse.press(button)
@@ -39,25 +39,37 @@ class GameInteractor:
     def click_up(self, button=Button.left):
         """Click the specified mouse button up"""
         self.mouse.release(button)
-        
+
     def click_right(self, button=Button.right):
         """Click the right mouse button"""
         self.mouse.press(button)
         self.mouse.release(button)
-        
+
     def type_string(self, text):
         """Type the given string"""
-        self.keyboard.type(text)
-        
+        # For prices, type each character with a small delay
+        if '.' in text and len(text) < 20 and any(c.isdigit() for c in text):
+            # This is likely a price with a decimal point
+            for char in text:
+                self.keyboard.type(char)
+                # Add a slightly longer delay for the decimal point
+                if char == '.':
+                    time.sleep(0.2)
+                else:
+                    time.sleep(0.05)
+        else:
+            # For other text, type normally
+            self.keyboard.type(text)
+
     def press_key(self, key):
         """Press and release a keyboard key"""
         self.keyboard.press(key)
         self.keyboard.release(key)
-        
+
     def hold_key(self, key):
         """Press and hold a keyboard key"""
         self.keyboard.press(key)
-        
+
     def release_key(self, key):
         """Release a keyboard key that was being held"""
         self.keyboard.release(key)
@@ -84,14 +96,14 @@ class GameInteractor:
         self.keyboard.press(key)
         time.sleep(0.1)
         self.keyboard.release(key)
-        
+
     def EveSafeClick(self, x, y, button=Button.left):
         """Perform a safe click for Eve Online:
         1. Move to coordinates
         2. Wait 1 second
         3. Click
         4. Wait 0.25 seconds
-        
+
         Args:
             x: x-coordinate
             y: y-coordinate
@@ -108,7 +120,7 @@ class GameInteractor:
         2. Wait 1 second
         3. Click
         4. Wait 0.25 seconds
-        
+
         Args:
             x: x-coordinate
             y: y-coordinate
@@ -118,3 +130,34 @@ class GameInteractor:
         time.sleep(1.0)  # Wait 1 second
         self.click_right(button)
         time.sleep(0.25)  # Wait 0.25 seconds
+
+    def type_price(self, price_str):
+        """
+        Specialized method for typing price values in EVE Online.
+        Ensures proper decimal point handling.
+
+        Args:
+            price_str: The price as a string
+        """
+        # Ensure the price is properly formatted with a decimal point
+        if isinstance(price_str, (int, float)):
+            price_str = f"{float(price_str):.2f}"
+        else:
+            # Try to convert to float and back to ensure proper format
+            try:
+                price_str = f"{float(price_str):.2f}"
+            except ValueError:
+                # If conversion fails, use as is
+                pass
+
+        # Type each character with a delay
+        for char in price_str:
+            self.keyboard.type(char)
+            # Add a slightly longer delay for the decimal point
+            if char == '.':
+                time.sleep(0.3)
+            else:
+                time.sleep(0.1)
+
+        # Add a small delay after typing the full price
+        time.sleep(0.5)
diff --git a/TinyGamer/main.py b/TinyGamer/main.py
index 08ce8d3..c9473ec 100644
--- a/TinyGamer/main.py
+++ b/TinyGamer/main.py
@@ -13,10 +13,21 @@ from sensor_runner import SensorRunner
 from webcam_frame_grabber import WebcamFrameGrabber
 from config_manager import ConfigManager
 
-def run_director(use_eve=False):
+def run_director(use_eve=False, no_preview=False, no_status=False):
     """Run the main TinyGamer Director application."""
     multiprocessing.freeze_support()
-    director = EveDirector() if use_eve else Director()
+
+    # Set up file logging if no_status is enabled
+    if no_status:
+        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
+        os.makedirs(log_dir, exist_ok=True)
+        log_file = os.path.join(log_dir, 'tinygamer.log')
+        file_handler = logging.FileHandler(log_file, mode='w')
+        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
+        logging.getLogger().addHandler(file_handler)
+        logging.info(f"Running in headless mode, logging to {log_file}")
+
+    director = EveDirector(no_preview=no_preview, no_status=no_status) if use_eve else Director(no_status=no_status)
     try:
         exit_code = director.run()
     except Exception as e:
@@ -78,12 +89,14 @@ def main():
     parser = argparse.ArgumentParser(description='TinyGamer - Game Automation Tool')
     parser.add_argument('--builder', action='store_true', help='Run BuilderBuddy instead of Director')
     parser.add_argument('--eve', action='store_true', help='Run with EveDirector for Eve Online specific features')
+    parser.add_argument('--no-preview', action='store_true', help='Skip camera preview dialog and go straight to main loop')
+    parser.add_argument('--no-status', action='store_true', help='Run without status window and log to file instead')
     args = parser.parse_args()
 
     if args.builder:
         return run_builder_buddy()
     else:
-        return run_director(use_eve=args.eve)
+        return run_director(use_eve=args.eve, no_preview=args.no_preview, no_status=args.no_status)
 
 if __name__ == "__main__":
     sys.exit(main())
