-- Add market_data table and latest_market_data view
-- This script adds the missing market_data table and latest_market_data view

-- Create market_data table if it doesn't exist
CREATE TABLE IF NOT EXISTS market_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    region_id INTEGER NOT NULL DEFAULT 10000002, -- Default to The Forge (Jita)
    sell_price REAL,
    buy_price REAL,
    daily_volume INTEGER,
    weekly_volume INTEGER,
    price_volatility REAL,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(item_id, region_id, timestamp)
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_market_data_item_id ON market_data(item_id);
CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp);

-- Create view for latest market data per item
CREATE VIEW IF NOT EXISTS latest_market_data AS
SELECT m.*
FROM market_data m
INNER JOIN (
    SELECT item_id, region_id, MAX(timestamp) as max_timestamp
    FROM market_data
    GROUP BY item_id, region_id
) latest ON m.item_id = latest.item_id 
    AND m.region_id = latest.region_id 
    AND m.timestamp = latest.max_timestamp;

-- Insert a test record for item ID 17765 (if needed for testing)
INSERT OR IGNORE INTO market_data (
    item_id, 
    region_id, 
    sell_price, 
    buy_price, 
    daily_volume, 
    weekly_volume, 
    price_volatility
) VALUES (
    17765,  -- Item ID from the error message
    10000002,  -- The Forge (Jita)
    1000000.0,  -- Example sell price
    900000.0,   -- Example buy price
    1000,       -- Example daily volume
    7000,       -- Example weekly volume
    0.05        -- Example price volatility
);
