"""
Initialize the EVE Online Item Database

This script downloads and imports EVE Online Static Data Export (SDE) data
into the local item database for quick item ID to name lookups.
"""

import os
import sys
import time
from eve_trader.utils.item_database import get_item_database

def main():
    """Main function to initialize the item database."""
    print("Initializing EVE Online Item Database...")
    
    # Get the item database instance
    item_db = get_item_database()
    
    # Check if we already have items
    count = item_db.count_items()
    print(f"Current item count in database: {count}")
    
    if count > 1000:  # Arbitrary threshold
        print("Database already contains items. Do you want to update it anyway? (y/n)")
        choice = input().lower()
        if choice != 'y':
            print("Skipping database update.")
            return
    
    # Start the import process
    print("Downloading and importing EVE Online SDE data...")
    print("This may take a few minutes...")
    
    start_time = time.time()
    success = item_db.import_from_fuzzwork()
    end_time = time.time()
    
    if success:
        new_count = item_db.count_items()
        print(f"Import completed successfully in {end_time - start_time:.2f} seconds.")
        print(f"Database now contains {new_count} items.")
        
        # Test a few common items
        test_ids = [34, 35, 36, 37, 38]  # Some common items (Tritanium, Pyerite, etc.)
        print("\nTesting item lookups:")
        for item_id in test_ids:
            name = item_db.get_item_name(item_id)
            print(f"  Item {item_id}: {name}")
        
        print("\nTesting item search:")
        search_results = item_db.search_items("Tritanium")
        if search_results:
            print(f"  Found {len(search_results)} items matching 'Tritanium'")
            for item in search_results[:3]:  # Show first 3
                print(f"    {item['item_id']}: {item['item_name']}")
        else:
            print("  No items found matching 'Tritanium'")
    else:
        print("Import failed. Please check the logs for details.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
