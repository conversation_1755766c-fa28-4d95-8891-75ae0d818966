from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Optional

from models.database import DatabaseManager
from models.transaction import Transaction
# Need access to inventory to get cost basis and potentially acquisition date
from controllers.accounting_controller import AccountingController

# Strategy Classification Rules (updated)
# Durations in days
MARGIN_TRADING_MAX_DAYS = 29
SPECULATION_MIN_DAYS = 30
ARBITRAGE_MAX_HOURS = 1 # Changed to hours for clarity

# Price change percentages (as decimals)
# Margin trading no longer has price requirements
SPECULATION_MIN_CHANGE = Decimal('0.20') # 20%

# Strategy Names
STRATEGY_MARGIN = "Margin Trading"
STRATEGY_SPECULATION = "Speculation"
STRATEGY_ARBITRAGE = "Arbitrage"
# STRATEGY_LONG_TERM removed as requested
STRATEGY_UNCLASSIFIED = "Unclassified"
STRATEGY_LOSS = "Loss Trade" # Added category for trades sold at a loss

class StrategyController:
    def __init__(self, db_manager: DatabaseManager, accounting_controller: AccountingController):
        self.db = db_manager
        self.accounting = accounting_controller # Needed for inventory info

    def classify_transaction(self, sell_transaction: Transaction) -> str:
        """
        Classifies a single SELL transaction based on rules.
        Uses inventory average cost and acquisition date as proxies for buy info.
        Updates the transaction record in the database.
        Returns the assigned strategy name.
        """
        if sell_transaction.transaction_type != 'SELL':
            return STRATEGY_UNCLASSIFIED # Only classify sells

        print(f"Classifying strategy for TX {sell_transaction.tx_id} (Item: {sell_transaction.item_name})")

        # --- Get necessary data ---
        # 1. Cost Basis (Average Buy Cost)
        # We need the cost basis *before* the sale occurred. Fetch inventory item.
        inventory_item = self.accounting.get_inventory_item(
            sell_transaction.item_id,
            sell_transaction.location_id
        )

        if not inventory_item:
            print(f"Warning: Cannot classify TX {sell_transaction.tx_id}. No inventory found for item {sell_transaction.item_id}.")
            # Update DB?
            # self.db.execute_update("UPDATE transactions SET strategy = ? WHERE tx_id = ?", (STRATEGY_UNCLASSIFIED, sell_transaction.tx_id))
            return STRATEGY_UNCLASSIFIED

        # Use the average cost before this sale reduced inventory
        # Recalculate cost basis before this sale occurred
        cost_basis_per_unit = inventory_item['avg_unit_cost']
        if inventory_item['quantity'] + sell_transaction.quantity > 0:
             try:
                 cost_basis_per_unit = (inventory_item['total_cost'] + (sell_transaction.quantity * inventory_item['avg_unit_cost'])) / (inventory_item['quantity'] + sell_transaction.quantity)
             except ZeroDivisionError:
                 print(f"Warning: Zero division error calculating pre-sale cost basis for TX {sell_transaction.tx_id}. Using current avg cost.")
                 cost_basis_per_unit = inventory_item['avg_unit_cost'] # Fallback

        if cost_basis_per_unit <= 0:
             print(f"Warning: Cannot classify TX {sell_transaction.tx_id}. Invalid cost basis ({cost_basis_per_unit}) found for item {sell_transaction.item_id}.")
             return STRATEGY_UNCLASSIFIED


        # 2. Sell Price
        sell_price_per_unit = sell_transaction.unit_price

        # 3. Hold Duration (Approximate)
        # Use inventory acquisition_date as proxy for average buy time
        acquisition_date = inventory_item['acquisition_date']
        if not acquisition_date:
            # If no acquisition date, maybe use the timestamp of the earliest buy transaction?
            # This requires querying transactions table - potentially slow.
            # Fallback: Use a default or mark as unclassified for duration.
            print(f"Warning: Cannot determine hold duration for TX {sell_transaction.tx_id}. Missing acquisition date for item {sell_transaction.item_id}.")
            hold_duration = timedelta(days=-1) # Indicate unknown duration
        else:
             # Ensure acquisition_date is timezone-aware if timestamp is
             if sell_transaction.timestamp.tzinfo is not None and acquisition_date.tzinfo is None:
                  # Assume acquisition_date was stored as UTC if naive
                  acquisition_date = acquisition_date.replace(tzinfo=timezone.utc)
             elif sell_transaction.timestamp.tzinfo is None and acquisition_date.tzinfo is not None:
                  # Make sell_transaction timestamp aware (assume UTC)
                  sell_transaction.timestamp = sell_transaction.timestamp.replace(tzinfo=timezone.utc)

             hold_duration = sell_transaction.timestamp - acquisition_date

        # --- Apply Classification Rules ---
        strategy = STRATEGY_UNCLASSIFIED
        price_change_pct = (sell_price_per_unit - cost_basis_per_unit) / cost_basis_per_unit

        # Handle Loss first
        if price_change_pct < 0:
            strategy = STRATEGY_LOSS
        else:
            # Apply rules based on duration and price change
            hold_days = hold_duration.total_seconds() / (60 * 60 * 24)
            hold_hours = hold_duration.total_seconds() / (60 * 60)

            if hold_days < 0: # Unknown duration
                 strategy = STRATEGY_UNCLASSIFIED # Or base only on price change?
            elif hold_hours < ARBITRAGE_MAX_HOURS:
                strategy = STRATEGY_ARBITRAGE
            elif hold_days <= MARGIN_TRADING_MAX_DAYS:
                # Margin trading now only based on hold duration (< 29 days)
                strategy = STRATEGY_MARGIN
            elif hold_days >= SPECULATION_MIN_DAYS and price_change_pct > SPECULATION_MIN_CHANGE:
                # Speculation now requires minimum 30 days hold
                strategy = STRATEGY_SPECULATION
            else:
                # Fits no specific category based on duration/profit %
                strategy = STRATEGY_UNCLASSIFIED


        print(f"TX {sell_transaction.tx_id}: Hold={hold_duration}, PriceChg={price_change_pct:.2%}, Strategy={strategy}")

        # --- Update Database ---
        try:
            self.db.execute_update("UPDATE transactions SET strategy = ? WHERE tx_id = ?", (strategy, sell_transaction.tx_id))
            # Update object in memory too
            sell_transaction.strategy = strategy
        except Exception as e:
            print(f"Error updating strategy in DB for TX {sell_transaction.tx_id}: {e}")
            return STRATEGY_UNCLASSIFIED # Return unclassified if DB update fails

        return strategy

    def classify_all_transactions(self, character_id: Optional[int] = None):
        """
        Iterates through all SELL transactions lacking a strategy and classifies them.
        Optionally filters by character_id if multiple characters are possible in DB.
        """
        print("Starting strategy classification for all unclassified SELL transactions...")
        query = "SELECT * FROM transactions WHERE transaction_type = 'SELL' AND (strategy IS NULL OR strategy = ?)"
        params = [STRATEGY_UNCLASSIFIED]

        # Note: Filtering by character_id might require joining with api_tokens or adding character_id to transactions
        # Assuming transactions are implicitly for one character based on ESI client context for now.

        count = 0
        failed_count = 0
        try:
            sell_transactions = self.db.execute_query(query, params, fetch_all=True)
            if not sell_transactions:
                print("No unclassified SELL transactions found.")
                return

            print(f"Found {len(sell_transactions)} unclassified SELL transactions to process.")

            # Convert rows to Transaction objects
            from models.transaction import Transaction # Local import
            for row in sell_transactions:
                 try:
                     tx_obj = Transaction(**dict(row)) # Create object from DB row
                     assigned_strategy = self.classify_transaction(tx_obj)
                     if assigned_strategy != STRATEGY_UNCLASSIFIED:
                         count += 1
                     else:
                         failed_count += 1
                 except Exception as e:
                     print(f"Error processing transaction row {row['tx_id']} for classification: {e}")
                     failed_count += 1

            print(f"Strategy classification complete. Classified: {count}, Failed/Unclassified: {failed_count}")

        except Exception as e:
            print(f"An error occurred during bulk classification: {e}")


# Example Usage
if __name__ == '__main__':
    print("--- Strategy Controller Example ---")
    db_manager = DatabaseManager()
    # Need AccountingController, which might need ESIClient
    # Dummy ESIClient for offline testing if needed
    esi_client = None # Or initialize properly if testing with ESI
    if esi_client:
         print("Note: ESIClient provided, skill lookups might occur.")
    else:
         print("Note: No ESIClient provided, skill lookups will default to 0.")

    try:
        # Need to instantiate dependencies
        accounting_controller = AccountingController(db_manager, esi_client)
        strategy_controller = StrategyController(db_manager, accounting_controller)

        print("\n--- Classifying all unclassified transactions ---")
        # This requires BUY transactions and inventory to be populated correctly first
        # And SELL transactions to exist in the DB.
        # Run data sync first in a real scenario.
        strategy_controller.classify_all_transactions()

    except Exception as e:
        print(f"An error occurred during StrategyController testing: {e}")
