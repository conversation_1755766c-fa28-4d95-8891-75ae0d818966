import time
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Optional, Callable, List, Dict, Any

from models.database import DatabaseManager
from api.esi_client import ESIClient
from api.auth import ESIAuthenticator # Needed if ESIClient isn't pre-configured
from controllers.accounting_controller import AccountingController
from controllers.strategy_controller import StrategyController, STRATEGY_UNCLASSIFIED
from controllers.inventory_controller import InventoryController
from controllers.net_worth_controller import NetWorthController
from models.transaction import Transaction # For type hinting and instantiation
from models.order import Order # For type hinting and instantiation
from utils.item_database import get_item_database

# Define type for progress callback function: (percentage: int, message: str) -> None
ProgressCallback = Callable[[int, str], None]

# Helper function to safely parse datetime from ESI strings
def parse_esi_datetime(dt_str: Optional[str]) -> Optional[datetime]:
    if not dt_str:
        return None
    try:
        # ESI format example: "2023-01-16T15:04:05Z"
        return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
    except (ValueError, TypeError):
        print(f"Warning: Could not parse datetime string: {dt_str}")
        return None

# Helper function to get item name using the item database
def get_item_name(item_id: int) -> str:
    """
    Get the name of an item by its ID using the item database.

    Args:
        item_id: The EVE Online item type ID

    Returns:
        The item name if found, otherwise a placeholder string
    """
    item_db = get_item_database()

    # Check if the item database needs initialization
    count = item_db.count_items()
    if count < 1000:  # Arbitrary threshold
        print("Item database needs initialization. Starting import...")
        success = item_db.import_from_fuzzwork()
        if success:
            print(f"Item database initialized with {item_db.count_items()} items")
        else:
            print("Failed to initialize item database")

    return item_db.get_item_name(item_id)

class DataSyncController:
    def __init__(self,
                 db_manager: DatabaseManager,
                 esi_client: ESIClient,
                 accounting_controller: AccountingController,
                 strategy_controller: StrategyController):
        self.db = db_manager
        self._esi = None  # Initialize as None
        self.accounting = accounting_controller
        self.strategy = strategy_controller
        self.inventory = InventoryController(db_manager, accounting_controller)
        self.character_id = None
        # Set the ESI client, which will also update the character_id
        self.esi = esi_client

    @property
    def esi(self):
        """Get the ESI client."""
        return self._esi

    @esi.setter
    def esi(self, esi_client):
        """Set the ESI client and update character_id."""
        self._esi = esi_client
        if esi_client and hasattr(esi_client, 'character_id'):
            self.character_id = esi_client.character_id
            print(f"DataSyncController: Updated character_id to {self.character_id} from ESI client")

    def _emit_progress(self, callback: Optional[ProgressCallback], percent: int, message: str):
        """ Helper to safely call the progress callback. """
        print(f"Sync Progress: {percent}% - {message}")
        if callback:
            try:
                callback(percent, message)
            except Exception as e:
                print(f"Error in progress callback: {e}")

    def sync_wallet_transactions(self, progress_callback: Optional[ProgressCallback] = None) -> bool:
        """
        Fetches new wallet transactions from ESI and stores them.

        Note: This method has been modified to always fetch fresh data from ESI without using cache.
        All transactions from ESI are processed, and the database's INSERT OR IGNORE functionality
        prevents duplicate entries.
        """
        if not self.character_id:
            self._emit_progress(progress_callback, 5, "Error: Character ID not set for ESI client.")
            return False

        self._emit_progress(progress_callback, 5, "Fetching wallet transactions...")

        # Determine the starting point for fetching transactions
        last_tx_id = None
        try:
            # Find the highest transaction_id already in the DB for this character
            # Note: Assumes tx_id increases over time. ESI uses journal_ref_id which is better.
            # Let's query based on journal_ref_id instead.
            last_journal_ref_row = self.db.execute_query(
                "SELECT MAX(journal_ref_id) as max_ref FROM transactions WHERE character_id = ?",
                (self.character_id,),
                fetch_one=True
            )
            # ESI wallet transactions endpoint uses 'from_id' which refers to transaction_id, not journal_ref_id.
            # This makes incremental sync tricky. Fetching all available might be simpler.
            # ESI only returns last ~30 days or ~2500 entries via this endpoint.
            # A full history requires parsing market logs or using other endpoints if available.
            # For now, fetch all available pages from the standard endpoint.
            print("Fetching all available wallet transactions (may be limited by ESI)...")
            all_esi_transactions = self.esi.get_all_pages('get_wallet_transactions')

        except Exception as e:
            self._emit_progress(progress_callback, 10, f"Error querying last transaction: {e}")
            return False

        if all_esi_transactions is None:
            self._emit_progress(progress_callback, 10, "Failed to fetch transactions from ESI.")
            return False
        if not all_esi_transactions:
            self._emit_progress(progress_callback, 10, "No new transactions found.")
            return True # Not an error if there's nothing new

        self._emit_progress(progress_callback, 10, f"Processing {len(all_esi_transactions)} ESI transaction entries...")

        processed_count = 0
        skipped_count = 0
        error_count = 0
        new_buys = []
        new_sells = []

        # We no longer skip transactions based on existing journal reference IDs
        # This ensures we always process all transactions from ESI
        # We'll rely on the database's INSERT OR IGNORE to prevent duplicates
        existing_refs = set()
        print("Caching disabled: Processing all transactions from ESI without skipping existing ones.")

        for i, tx_data in enumerate(all_esi_transactions):
            try:
                journal_ref_id = tx_data.get('journal_ref_id')
                if not journal_ref_id:
                     print(f"Skipping transaction entry - missing journal_ref_id: {tx_data}")
                     skipped_count += 1
                     continue

                # We no longer skip transactions based on existing journal reference IDs
                # The database's INSERT OR IGNORE will handle duplicates

                # Basic check if it's a player market transaction
                # Ref types for market transactions: 1 (player trading), 42 (market escrow) - need journal for fees
                # This endpoint might only show type 1? Check ESI docs.
                # Let's assume this endpoint gives relevant buy/sell for now.
                if tx_data.get('is_buy') is None:
                     print(f"Skipping non-buy/sell transaction? RefID: {journal_ref_id}, Data: {tx_data}")
                     skipped_count += 1
                     continue


                # --- Map ESI data to Transaction model ---
                item_id = tx_data['type_id']
                item_name = get_item_name(item_id) # Get item name from database

                # Calculate total price
                total_price = Decimal(str(tx_data['unit_price'])) * tx_data['quantity']

                # Calculate fees based on transaction type and character skills
                broker_fee = Decimal('0')
                sales_tax = Decimal('0')

                # For SELL transactions, calculate both broker fee and sales tax
                if not tx_data['is_buy']:
                    # Calculate broker fee (paid when placing the sell order)
                    broker_fee = self.accounting.calculate_broker_fee(total_price, self.character_id)

                    # Calculate sales tax (paid when the sell order is fulfilled)
                    sales_tax = self.accounting.calculate_sales_tax(total_price, self.character_id)

                    # Net amount for SELL = total_price - broker_fee - sales_tax
                    net_amount_placeholder = total_price - broker_fee - sales_tax
                else:
                    # For BUY transactions, calculate only broker fee
                    broker_fee = self.accounting.calculate_broker_fee(total_price, self.character_id)

                    # Net amount for BUY = -(total_price + broker_fee)
                    net_amount_placeholder = -(total_price + broker_fee)

                transaction = Transaction(
                    tx_id=tx_data['transaction_id'], # Use ESI's transaction_id as primary key? Or journal_ref_id? DB uses tx_id.
                    timestamp=parse_esi_datetime(tx_data['date']),
                    item_id=item_id,
                    item_name=item_name,
                    quantity=tx_data['quantity'],
                    unit_price=Decimal(str(tx_data['unit_price'])),
                    total_price=total_price,
                    transaction_type='BUY' if tx_data['is_buy'] else 'SELL',
                    order_id=None, # Wallet endpoint doesn't link directly to order_id
                    broker_fee=broker_fee, # Placeholder
                    sales_tax=sales_tax, # Placeholder
                    # Use ESI net_amount if available, otherwise placeholder
                    net_amount=Decimal(str(tx_data.get('net_amount', net_amount_placeholder))),
                    strategy=None, # To be classified later
                    profit=None, # To be calculated later
                    location_id=tx_data['location_id'], # Assuming Jita 4-4 mostly
                    is_buy_order=tx_data['is_buy'], # Redundant with transaction_type but ESI provides it
                    client_id=tx_data['client_id'],
                    journal_ref_id=journal_ref_id,
                    character_id=self.character_id, # Add character_id to track which character the transaction belongs to
                    notes=None
                )

                # --- Store in Database ---
                tx_dict = transaction.to_dict()

                # First check if this transaction already exists in the database
                check_q = "SELECT 1 FROM transactions WHERE tx_id = ?"
                exists = self.db.execute_query(check_q, (transaction.tx_id,), fetch_one=True)

                if exists:
                    # Transaction already exists, skip it
                    skipped_count += 1
                    continue

                # Transaction doesn't exist, insert it
                columns = ', '.join(tx_dict.keys())
                placeholders = ', '.join(f':{key}' for key in tx_dict.keys())
                query = f"INSERT INTO transactions ({columns}) VALUES ({placeholders})"
                insert_id = self.db.execute_update(query, tx_dict)

                if insert_id is not None:
                    processed_count += 1
                    # Add to appropriate list for inventory processing
                    if transaction.transaction_type == 'BUY':
                        new_buys.append(transaction)
                    else:
                        new_sells.append(transaction)
                else:
                    # Insert failed for some reason
                    print(f"Error inserting transaction {transaction.tx_id} (Ref: {journal_ref_id}).")
                    error_count += 1


            except Exception as e:
                print(f"Error processing ESI transaction entry {i}: {e}")
                print(f"Data: {tx_data}")
                error_count += 1

            if i % 100 == 0: # Update progress periodically
                 self._emit_progress(progress_callback, 10 + int(60 * (i / len(all_esi_transactions))), f"Processing ESI transactions ({i}/{len(all_esi_transactions)})...")


        self._emit_progress(progress_callback, 70, f"Transaction sync done. Processed: {processed_count}, Skipped: {skipped_count}, Errors: {error_count}")

        # --- Process BUY transactions for FIFO inventory tracking ---
        self._emit_progress(progress_callback, 75, f"Processing {len(new_buys)} new BUY transactions for inventory...")
        for i, buy_tx in enumerate(new_buys):
            # Add to inventory with FIFO tracking
            self.inventory.process_buy_transaction(buy_tx)
            if i % 50 == 0:
                self._emit_progress(progress_callback, 75 + int(5 * (i / max(1, len(new_buys)))),
                                   f"Processing inventory (buys) ({i}/{len(new_buys)})...")

        # --- Process SELL transactions with FIFO inventory tracking ---
        self._emit_progress(progress_callback, 80, f"Processing {len(new_sells)} new SELL transactions with FIFO...")
        for i, sell_tx in enumerate(new_sells):
            # Process with FIFO inventory tracking
            total_profit, batch_links = self.inventory.process_sell_transaction(sell_tx)

            # Update the transaction object in memory
            sell_tx.profit = total_profit

            if i % 50 == 0:
                self._emit_progress(progress_callback, 80 + int(10 * (i / max(1, len(new_sells)))),
                                   f"Processing FIFO sales ({i}/{len(new_sells)})...")

        # --- Determine strategies based on FIFO batch links ---
        self._emit_progress(progress_callback, 90, "Determining strategies for new SELL transactions...")
        classified_count = 0
        for i, sell_tx in enumerate(new_sells):
            if sell_tx.profit is not None:
                # Determine strategy based on the batch links
                strategy = self.inventory.determine_strategy_for_transaction(sell_tx.tx_id)

                if strategy:
                    # Update the transaction with the determined strategy
                    self.db.execute_update(
                        "UPDATE transactions SET strategy = ? WHERE tx_id = ?",
                        (strategy, sell_tx.tx_id)
                    )
                    sell_tx.strategy = strategy
                    classified_count += 1
                else:
                    # If no strategy could be determined from batch links, use the classifier
                    strategy = self.strategy.classify_transaction(sell_tx)
                    if strategy != STRATEGY_UNCLASSIFIED:
                        classified_count += 1

            if i % 50 == 0:
                self._emit_progress(progress_callback, 90 + int(9 * (i / max(1, len(new_sells)))),
                                   f"Determining strategies ({i}/{len(new_sells)})...")

        self._emit_progress(progress_callback, 99, f"Strategy determination done. Classified: {classified_count}/{len(new_sells)}")

        return error_count == 0


    def sync_market_orders(self, progress_callback: Optional[ProgressCallback] = None) -> bool:
        """
        Fetches current character market orders and updates the database.

        Note: This method has been modified to always fetch fresh data from ESI without using cache.
        All orders from ESI are processed, and the database's INSERT OR IGNORE functionality
        prevents duplicate entries.
        """
        if not self.character_id:
            self._emit_progress(progress_callback, 5, "Error: Character ID not set for ESI client.")
            return False

        self._emit_progress(progress_callback, 5, "Fetching active market orders with force_refresh=True...")
        try:
            esi_orders = self.esi.get_character_orders(force_refresh=True)
            if esi_orders is None:
                self._emit_progress(progress_callback, 10, "Failed to fetch market orders from ESI.")
                return False

            self._emit_progress(progress_callback, 10, f"Processing {len(esi_orders)} ESI order entries...")

            # Get existing active order IDs from DB to compare
            # We still need this for detecting orders that are no longer active
            db_order_rows = self.db.execute_query(
                 "SELECT order_id, quantity_remaining, price, last_updated FROM orders WHERE quantity_remaining > 0 AND canceled = 0 AND expired = 0 AND character_id = ?",
                 (self.character_id,),
                 fetch_all=True
            )
            db_orders = {row['order_id']: dict(row) for row in db_order_rows} if db_order_rows else {}
            esi_order_ids = set()
            processed_count = 0
            error_count = 0

            for i, order_data in enumerate(esi_orders):
                 order_id = order_data['order_id']
                 esi_order_ids.add(order_id)
                 try:
                      # --- Map ESI data to Order model ---
                      item_id = order_data['type_id']
                      item_name = get_item_name(item_id) # Get item name from database

                      # Parse dates and decimals
                      placed_time = parse_esi_datetime(order_data['issued'])
                      price = Decimal(str(order_data['price']))
                      escrow = Decimal(str(order_data.get('escrow', '0'))) # Optional field

                      # Handle missing is_buy_order field
                      is_buy_order = order_data.get('is_buy_order')

                      # If is_buy_order is missing, try to determine from other fields
                      if is_buy_order is None:
                          # Check if escrow is present (typically only for buy orders)
                          if 'escrow' in order_data and order_data['escrow'] > 0:
                              is_buy_order = True
                          # Check if range is 'station' (typical for sell orders)
                          elif order_data.get('range') == 'station':
                              is_buy_order = False
                          # Default to sell order if we can't determine
                          else:
                              print(f"Warning: Could not determine if order {order_id} is buy or sell. Defaulting to SELL.")
                              is_buy_order = False

                      order = Order(
                           order_id=order_id,
                           item_id=item_id,
                           item_name=item_name,
                           order_type='BUY' if is_buy_order else 'SELL',
                           quantity_original=order_data['volume_total'],
                           quantity_remaining=order_data['volume_remain'],
                           price=price,
                           placed_time=placed_time,
                           location_id=order_data['location_id'],
                           character_id=self.character_id, # Add character_id to track which character the order belongs to
                           fulfilled_time=None, # Active orders aren't fulfilled
                           expired=False, # Active orders aren't expired
                           canceled=False, # Active orders aren't canceled
                           broker_fee=Decimal('0'), # TODO: Calculate or get from journal/history
                           relist_count=0, # TODO: Track relists
                           relist_fees=Decimal('0'), # TODO: Track relist fees
                           strategy=None, # TODO: Link strategy?
                           range=order_data['range'],
                           escrow=escrow,
                           is_corporation=order_data['is_corporation'],
                           esi_expires=None, # TODO: Get from ESI headers if possible
                           last_updated=datetime.now(timezone.utc)
                      )

                      # --- Compare with DB and Update/Insert ---
                      order_dict = order.to_dict()
                      if order_id in db_orders:
                           # Existing order - check if update needed (e.g., quantity_remaining, price)
                           # Simple check: update if quantity or price differs
                           if (db_orders[order_id]['quantity_remaining'] != order.quantity_remaining or
                               Decimal(db_orders[order_id]['price']) != order.price):

                                set_clause = ', '.join(f"{key} = :{key}" for key in order_dict if key != 'order_id')
                                query = f"UPDATE orders SET {set_clause} WHERE order_id = :order_id"
                                self.db.execute_update(query, order_dict)
                                print(f"Updated existing order {order_id}.")
                           # else:
                           #      print(f"Order {order_id} unchanged.")
                           # Optionally update last_updated timestamp even if unchanged?
                      else:
                           # New order - insert
                           columns = ', '.join(order_dict.keys())
                           placeholders = ', '.join(f':{key}' for key in order_dict.keys())
                           query = f"INSERT OR IGNORE INTO orders ({columns}) VALUES ({placeholders})"
                           self.db.execute_update(query, order_dict)
                           print(f"Inserted new active order {order_id}.")

                      processed_count += 1

                 except Exception as e:
                      print(f"Error processing ESI order entry {i} (ID: {order_id}): {e}")
                      print(f"Data: {order_data}")
                      error_count += 1

                 if i % 50 == 0: # Update progress periodically
                      self._emit_progress(progress_callback, 10 + int(80 * (i / len(esi_orders))), f"Processing ESI orders ({i}/{len(esi_orders)})...")


            # --- Mark missing orders as potentially fulfilled/canceled/expired ---
            # Orders in DB but not in ESI response are no longer active
            missing_order_ids = set(db_orders.keys()) - esi_order_ids
            if missing_order_ids:
                 self._emit_progress(progress_callback, 95, f"Marking {len(missing_order_ids)} potentially closed orders...")
                 print(f"Orders no longer active in ESI: {missing_order_ids}")
                 # Mark as quantity 0, but don't know if fulfilled/canceled/expired without history endpoint
                 # A simple approach is to set quantity_remaining to 0.
                 # A better approach involves fetching order history.
                 update_query = "UPDATE orders SET quantity_remaining = 0, last_updated = ? WHERE order_id = ?"
                 now_iso = datetime.now(timezone.utc).isoformat()
                 for missing_id in missing_order_ids:
                      self.db.execute_update(update_query, (now_iso, missing_id))

            self._emit_progress(progress_callback, 99, f"Order sync done. Processed: {processed_count}, Errors: {error_count}")
            return error_count == 0

        except Exception as e:
            self._emit_progress(progress_callback, 99, f"Error during order sync: {e}")
            return False


    def ensure_single_character_mode(self) -> bool:
        """
        Ensures that the application is only working with one character at a time.
        Checks if there are transactions or orders from other characters and returns False if found.
        """
        if not self.character_id:
            print("Error: No character ID set for data sync controller.")
            return False

        # Check if there are transactions from other characters
        other_char_tx = self.db.execute_query(
            "SELECT DISTINCT character_id FROM transactions WHERE character_id != ? LIMIT 1",
            (self.character_id,),
            fetch_one=True
        )

        if other_char_tx:
            print(f"Error: Found transactions from another character (ID: {other_char_tx['character_id']}). "
                  f"This application only supports one character at a time.")
            return False

        # Check if there are orders from other characters
        other_char_order = self.db.execute_query(
            "SELECT DISTINCT character_id FROM orders WHERE character_id != ? LIMIT 1",
            (self.character_id,),
            fetch_one=True
        )

        if other_char_order:
            print(f"Error: Found orders from another character (ID: {other_char_order['character_id']}). "
                  f"This application only supports one character at a time.")
            return False

        return True

    def check_auth_status(self) -> bool:
        """ Check if authentication is valid before starting sync. """
        if not self.character_id:
            print("Error: No character ID set for data sync controller.")
            return False

        if not self.esi or not hasattr(self.esi, 'authenticator'):
            print("Error: ESI client not properly initialized.")
            return False

        # Check if token exists in database
        token_data = self.esi.authenticator.get_token_data(self.character_id)
        if not token_data:
            print(f"Error: No token data found for character {self.character_id}.")
            return False

        # Check if token is valid or can be refreshed
        access_token = self.esi.authenticator.get_valid_access_token(self.character_id)
        if not access_token:
            print(f"Error: Could not get valid access token for character {self.character_id}.")
            return False

        print(f"Authentication status check passed for character {self.character_id}.")
        return True

    def sync_all_data(self, progress_callback: Optional[ProgressCallback] = None) -> bool:
        """ Orchestrates the synchronization of all required ESI data. """
        self._emit_progress(progress_callback, 0, "Starting full data synchronization...")

        # Ensure we're only working with one character
        if not self.ensure_single_character_mode():
            self._emit_progress(progress_callback, 100, "Synchronization failed: Multiple characters detected in database.")
            return False

        # Check authentication status
        if not self.check_auth_status():
            self._emit_progress(progress_callback, 100, "Synchronization failed: Authentication check failed.")
            return False

        # --- Check if FIFO migration is needed ---
        migration_status = self.db.execute_query(
            "SELECT completed FROM migration_status WHERE migration_name = 'fifo_inventory_tracking'",
            fetch_one=True
        )

        # Check if migration status doesn't exist or if it's not completed (completed = 0)
        if not migration_status or not migration_status['completed']:
            self._emit_progress(progress_callback, 5, "Initializing FIFO inventory tracking...")
            # Create tables if they don't exist
            self.inventory._ensure_tables_exist()

            # Check if we have existing transactions to migrate
            tx_count = self.db.execute_query(
                "SELECT COUNT(*) as count FROM transactions",
                fetch_one=True
            )

            if tx_count and tx_count['count'] > 0:
                self._emit_progress(progress_callback, 10, "Migrating existing transactions to FIFO inventory system...")
                self.inventory.migrate_historical_transactions()
                self._emit_progress(progress_callback, 20, "FIFO migration completed.")

        # --- Sync Wallet Transactions (and process inventory/profit/strategy) ---
        self._emit_progress(progress_callback, 5, "Starting wallet transaction synchronization...")
        if not self.sync_wallet_transactions(progress_callback):
             # Progress callback already emitted error message inside sync_wallet_transactions
             self._emit_progress(progress_callback, 100, "Synchronization failed during transaction processing.")
             return False
        # Wallet sync handles progress from 5% to 99% internally

        # --- Sync Active Market Orders ---
        # Reset progress slightly for order sync phase
        self._emit_progress(progress_callback, 70, "Starting order synchronization...")
        if not self.sync_market_orders(progress_callback):
             self._emit_progress(progress_callback, 100, "Synchronization failed during order processing.")
             return False
        # Order sync handles progress from 5% to 99% internally

        # --- TODO: Add other sync steps as needed ---
        # - Sync Wallet Journal (for fees, taxes, other entries)
        # - Sync Market Order History (to properly close missing orders)
        # - Sync Market Prices (for inventory valuation)

        # --- Record Net Worth Snapshot ---
        self._emit_progress(progress_callback, 95, "Recording net worth snapshot...")
        try:
            # Create a net worth controller
            net_worth_controller = NetWorthController(
                db_manager=self.db,
                esi_client=self.esi
            )

            # Record a snapshot
            net_worth_controller.record_net_worth_snapshot(force_refresh=True)
            self._emit_progress(progress_callback, 99, "Net worth snapshot recorded.")
        except Exception as e:
            print(f"Error recording net worth snapshot: {e}")
            # Continue with sync even if net worth snapshot fails
            self._emit_progress(progress_callback, 99, "Warning: Failed to record net worth snapshot.")

        self._emit_progress(progress_callback, 100, "Full data synchronization completed.")
        return True


# Example Usage
if __name__ == '__main__':
    print("--- Data Sync Controller Example ---")

    # Need to initialize all dependencies
    try:
        db_manager = DatabaseManager()
        # Replace with your actual character ID that has tokens in the DB
        TEST_CHARACTER_ID = 123456
        token_exists = db_manager.execute_query("SELECT 1 FROM api_tokens WHERE character_id = ?", (TEST_CHARACTER_ID,), fetch_one=True)

        if token_exists:
            print(f"Token found for character {TEST_CHARACTER_ID}. Initializing dependencies...")
            authenticator = ESIAuthenticator() # Assumes auth.py is configured
            esi_client = ESIClient(character_id=TEST_CHARACTER_ID, authenticator=authenticator)
            accounting_controller = AccountingController(db_manager, esi_client)
            strategy_controller = StrategyController(db_manager, accounting_controller)
            data_sync_controller = DataSyncController(
                db_manager=db_manager,
                esi_client=esi_client,
                accounting_controller=accounting_controller,
                strategy_controller=strategy_controller
            )

            print("\n--- Running Full Data Sync ---")
            # Define a simple progress callback for testing
            def simple_progress(percent, message):
                print(f"  [PROGRESS {percent}%] {message}")

            success = data_sync_controller.sync_all_data(progress_callback=simple_progress)

            if success:
                print("\n--- Data Sync Completed Successfully ---")
            else:
                print("\n--- Data Sync Failed ---")

        else:
            print(f"No token found for character {TEST_CHARACTER_ID}. Cannot run sync example.")

    except ImportError as e:
        print(f"Import Error, likely missing dependencies or incorrect relative paths: {e}")
    except Exception as e:
        print(f"An error occurred during DataSyncController testing: {e}")
