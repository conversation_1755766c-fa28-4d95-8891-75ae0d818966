"""
Top Items Profit Controller

This module provides a controller for retrieving and processing top items profit data.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal

from models.database import DatabaseManager


class TopItemsProfitController:
    """Controller for retrieving and processing top items profit data."""

    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize the top items profit controller.

        Args:
            db_manager: Database manager instance
        """
        self.db = db_manager

    def get_top_items_profit_data(self, date_range: Optional[Tuple[datetime, datetime]] = None) -> List[Dict[str, Any]]:
        """
        Get top items profit data, optionally filtered by date range.

        Args:
            date_range: Optional tuple of (start_date, end_date)

        Returns:
            List of item profit data dictionaries
        """
        try:
            # Check if the profit column exists in the transactions table
            check_query = """
            PRAGMA table_info(transactions)
            """
            columns = self.db.execute_query(check_query, fetch_all=True)
            has_profit_column = any(col['name'] == 'profit' for col in columns)

            # If we have a profit column, use it
            if has_profit_column:
                print("Using profit column from transactions table for top items")
                return self._get_top_items_with_profit_column(date_range)
            else:
                # Otherwise, calculate profit from total_price
                print("Calculating profit from transaction data for top items")
                return self._get_top_items_with_calculated_profit(date_range)

        except Exception as e:
            print(f"Error getting top items profit data: {e}")
            import traceback
            print(traceback.format_exc())

            # Return empty list in case of error
            return []

    def _get_top_items_with_profit_column(self, date_range: Optional[Tuple[datetime, datetime]] = None) -> List[Dict[str, Any]]:
        """
        Get top items profit data using the profit column, optionally filtered by date range.

        Args:
            date_range: Optional tuple of (start_date, end_date)

        Returns:
            List of item profit data dictionaries
        """
        # Build query based on whether we have a date range
        if date_range:
            start_date, end_date = date_range

            # Format dates for SQLite
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')

            query = """
            WITH item_profits AS (
                SELECT
                    item_id,
                    item_name,
                    SUM(profit) as total_profit
                FROM transactions
                WHERE
                    transaction_type = 'SELL' AND
                    timestamp >= ? AND
                    timestamp <= ?
                GROUP BY item_id, item_name
                ORDER BY total_profit DESC
                LIMIT 9
            ),
            other_profits AS (
                SELECT
                    'Other' as item_name,
                    SUM(profit) as total_profit
                FROM transactions
                WHERE
                    transaction_type = 'SELL' AND
                    timestamp >= ? AND
                    timestamp <= ? AND
                    item_id NOT IN (SELECT item_id FROM item_profits)
            )
            SELECT * FROM item_profits
            UNION ALL
            SELECT * FROM other_profits
            ORDER BY total_profit DESC
            """

            params = (start_date_str, end_date_str, start_date_str, end_date_str)
        else:
            # All time
            query = """
            WITH item_profits AS (
                SELECT
                    item_id,
                    item_name,
                    SUM(profit) as total_profit
                FROM transactions
                WHERE transaction_type = 'SELL'
                GROUP BY item_id, item_name
                ORDER BY total_profit DESC
                LIMIT 9
            ),
            other_profits AS (
                SELECT
                    'Other' as item_name,
                    SUM(profit) as total_profit
                FROM transactions
                WHERE
                    transaction_type = 'SELL' AND
                    item_id NOT IN (SELECT item_id FROM item_profits)
            )
            SELECT * FROM item_profits
            UNION ALL
            SELECT * FROM other_profits
            ORDER BY total_profit DESC
            """

            params = ()

        # Execute query
        results = self.db.execute_query(query, params, fetch_all=True)

        # Process results
        top_items_profit_data = []

        for row in results:
            # Skip items with zero or negative profit
            if not row['total_profit'] or float(row['total_profit']) <= 0:
                continue

            top_items_profit_data.append({
                'item_id': row.get('item_id', 0),  # 'Other' won't have an item_id
                'item_name': row['item_name'],
                'profit': row['total_profit'] or 0  # Handle NULL values
            })

        return top_items_profit_data

    def _get_top_items_with_calculated_profit(self, date_range: Optional[Tuple[datetime, datetime]] = None) -> List[Dict[str, Any]]:
        """
        Get top items profit data by calculating profit from total_price, optionally filtered by date range.

        Args:
            date_range: Optional tuple of (start_date, end_date)

        Returns:
            List of item profit data dictionaries
        """
        # Build query based on whether we have a date range
        if date_range:
            start_date, end_date = date_range

            # Format dates for SQLite
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')

            query = """
            WITH item_profits AS (
                SELECT
                    item_id,
                    item_name,
                    SUM(CASE WHEN transaction_type = 'SELL' THEN total_price ELSE 0 END) as total_profit
                FROM transactions
                WHERE
                    timestamp >= ? AND
                    timestamp <= ?
                GROUP BY item_id, item_name
                ORDER BY total_profit DESC
                LIMIT 9
            ),
            other_profits AS (
                SELECT
                    'Other' as item_name,
                    SUM(CASE WHEN transaction_type = 'SELL' THEN total_price ELSE 0 END) as total_profit
                FROM transactions
                WHERE
                    timestamp >= ? AND
                    timestamp <= ? AND
                    item_id NOT IN (SELECT item_id FROM item_profits)
            )
            SELECT * FROM item_profits
            UNION ALL
            SELECT * FROM other_profits
            ORDER BY total_profit DESC
            """

            params = (start_date_str, end_date_str, start_date_str, end_date_str)
        else:
            # All time
            query = """
            WITH item_profits AS (
                SELECT
                    item_id,
                    item_name,
                    SUM(CASE WHEN transaction_type = 'SELL' THEN total_price ELSE 0 END) as total_profit
                FROM transactions
                GROUP BY item_id, item_name
                ORDER BY total_profit DESC
                LIMIT 9
            ),
            other_profits AS (
                SELECT
                    'Other' as item_name,
                    SUM(CASE WHEN transaction_type = 'SELL' THEN total_price ELSE 0 END) as total_profit
                FROM transactions
                WHERE
                    item_id NOT IN (SELECT item_id FROM item_profits)
            )
            SELECT * FROM item_profits
            UNION ALL
            SELECT * FROM other_profits
            ORDER BY total_profit DESC
            """

            params = ()

        # Execute query
        results = self.db.execute_query(query, params, fetch_all=True)

        # Process results
        top_items_profit_data = []

        for row in results:
            # Skip items with zero or negative profit
            if not row['total_profit'] or float(row['total_profit']) <= 0:
                continue

            top_items_profit_data.append({
                'item_id': row.get('item_id', 0),  # 'Other' won't have an item_id
                'item_name': row['item_name'],
                'profit': row['total_profit'] or 0  # Handle NULL values
            })

        return top_items_profit_data
