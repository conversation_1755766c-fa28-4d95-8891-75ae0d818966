"""
Market Analyzer

This module provides functions to analyze market data for specific patterns.
"""
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta, timezone
import math

# --- Constants for Analysis ---
# These can be tuned or made configurable later

# Fake Margin Detection
FAKE_MARGIN_SPREAD_THRESHOLD_BUY_BASED = 1.0  # (sell-buy)/buy > 100%
FAKE_MARGIN_AVG_PRICE_FACTOR = 0.7  # avg_price_7d < (lowest_sell * 0.7)

# Spread Trap
SPREAD_TRAP_SPREAD_THRESHOLD_BUY_BASED = 0.5  # (sell-buy)/buy > 50%
LOW_VOLUME_ABSOLUTE_DEFAULT = 10000
LOW_VOLUME_ABSOLUTE_SHIPS_RARE = 5
LOW_VOLUME_RELATIVE_THRESHOLD = 0.3  # volume < 30% of 7-day average
LOW_FILL_RATE_THRESHOLD = 0.1 # (volume_today) / (total_outstanding_sell_quantity) < 0.1

# Price Spike
PRICE_SPIKE_HIGH_FACTOR = 2.5  # high_price_today > 2.5x avg_high_past_7d
PRICE_SPIKE_VOLUME_FACTOR = 0.5  # volume_today < 0.5x avg_volume_7d

# Dead Listings
DEAD_LISTINGS_ORDER_AGE_HOURS = 24
DEAD_LISTINGS_AVG_PRICE_FLAT_PERCENTAGE = 0.02  # price change < 2% over last 3 days
# Uses LOW_VOLUME_... thresholds as well

# Order Staleness
ORDER_STALENESS_AGE_HOURS = 24
ORDER_STALENESS_TOP_N_ORDERS = 5

# Healthy Liquidity
HEALTHY_LIQUIDITY_SPREAD_THRESHOLD_SELL_BASED = 0.1  # (sell-buy)/sell < 10%
HEALTHY_LIQUIDITY_VOLUME_CONSISTENCY_DAYS = 3
HEALTHY_LIQUIDITY_VOLUME_FACTOR = 0.7  # volume > 70% of 7-day average
HEALTHY_LIQUIDITY_AVG_VOLUME_DEFAULT = 10000
HEALTHY_LIQUIDITY_AVG_VOLUME_SHIPS_MODULES = 20
HEALTHY_LIQUIDITY_PRICE_DEVIATION_THRESHOLD = 0.05 # avg price within 5% of top buy/sell

# Sell/Buy Wall Pressure
WALL_TOP_N_ORDERS = 3
WALL_QUANTITY_PERCENTAGE_THRESHOLD = 0.80  # 80% of total quantity
WALL_PRICE_COMPARISON_DAYS = 3 # Compare current price to X-day average

# Volume Collapse
VOLUME_COLLAPSE_FACTOR = 0.25  # daily_volume < 25% of 7d average

# Spread Compression
SPREAD_COMPRESSION_DAYS = 3


# --- Helper Functions ---

def _get_current_best_prices(current_orders: Dict[str, List[Dict[str, Any]]]) -> Tuple[Optional[float], Optional[float]]:
    """Extracts best buy and sell prices from current orders."""
    buy_orders = current_orders.get('buy', [])
    sell_orders = current_orders.get('sell', [])

    highest_buy_price = None
    if buy_orders:
        highest_buy_price = max(order['price'] for order in buy_orders if isinstance(order.get('price'), (int, float)))

    lowest_sell_price = None
    if sell_orders:
        lowest_sell_price = min(order['price'] for order in sell_orders if isinstance(order.get('price'), (int, float)))

    return highest_buy_price, lowest_sell_price

def _get_historical_average(historical_data: List[Dict[str, Any]], key: str, days: int) -> Optional[float]:
    """Calculates the average of a specific key over a number of days from historical data."""
    if not historical_data or days <= 0:
        return None

    relevant_data = historical_data[-days:] # Get the last 'days' entries
    if not relevant_data:
        return None

    values = [entry[key] for entry in relevant_data if key in entry and isinstance(entry[key], (int, float))]
    if not values:
        return None

    return sum(values) / len(values)

def _get_historical_sum(historical_data: List[Dict[str, Any]], key: str, days: int) -> Optional[float]:
    """Calculates the sum of a specific key over a number of days from historical data."""
    if not historical_data or days <= 0:
        return None

    relevant_data = historical_data[-days:]
    if not relevant_data:
        return None

    values = [entry[key] for entry in relevant_data if key in entry and isinstance(entry[key], (int, float))]
    if not values: # Could be 0 if all values are 0
        return 0

    return sum(values)

def _get_most_recent_completed_day_data(historical_data: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """Gets data for the most recently completed day (assumes sorted by date ascending)."""
    if not historical_data:
        return None
    # ESI history often includes the current partial day at the end.
    # We want the last full day. If only one day, it might be partial.
    # A robust way is to check 'date' against today. For now, assume last entry is most recent.
    # If history is guaranteed to be full days, then historical_data[-1] is fine.
    # If it might include today's partial, historical_data[-2] (if len > 1) is safer for "previous day".
    # For "Price Spike" and "Volume Collapse", "today" means "most recent full day's data".
    if len(historical_data) > 0:
        # Assuming historical_data is sorted with the most recent day last.
        # And that the last entry is a complete day as per ESI spec for /history/
        return historical_data[-1]
    return None

def _is_item_ship_or_rare(item_name: Optional[str]) -> bool:
    """Basic heuristic to determine if an item is a ship or rare module."""
    # This is a placeholder. A more robust solution would involve item categories/groups.
    if not item_name:
        return False
    name_lower = item_name.lower()
    ship_keywords = ["frigate", "destroyer", "cruiser", "battlecruiser", "battleship", "carrier", "dreadnought", "titan", "shuttle", "hauler"]
    if any(keyword in name_lower for keyword in ship_keywords):
        return True
    # Add other heuristics for "rare" items if needed
    return False

# --- Analysis Check Functions ---

def check_fake_margin(
    highest_buy_price: Optional[float],
    lowest_sell_price: Optional[float],
    avg_price_7d: Optional[float]
) -> Optional[Dict[str, str]]:
    if highest_buy_price and lowest_sell_price and avg_price_7d and highest_buy_price > 0:
        spread_buy_based = (lowest_sell_price - highest_buy_price) / highest_buy_price
        condition_met = (spread_buy_based > FAKE_MARGIN_SPREAD_THRESHOLD_BUY_BASED and
                         avg_price_7d < (lowest_sell_price * FAKE_MARGIN_AVG_PRICE_FACTOR))
        if condition_met:
            tag = "(Alert) Fake margin detected"
            explanation = (
                f"The item shows a large potential profit margin based on current buy/sell prices, but its 7-day average traded price is significantly lower than the current lowest sell price. "
                f"This suggests the high sell orders might not be realistic or are rarely met.\n"
                f"Condition: (Spread (Sell-Buy)/Buy > {FAKE_MARGIN_SPREAD_THRESHOLD_BUY_BASED*100:.0f}%) AND (7d Avg Price < Lowest Sell Price * {FAKE_MARGIN_AVG_PRICE_FACTOR:.2f})"
            )
            return {"tag": tag, "explanation": explanation}
    return None

def check_low_turnover(
    highest_buy_price: Optional[float],
    lowest_sell_price: Optional[float],
    daily_volume_today: Optional[float],
    avg_volume_7d: Optional[float],
    total_sell_order_quantity: Optional[int],
    item_name: Optional[str]
) -> Optional[Dict[str, str]]:
    """
    Check if an item has low turnover (low fill rate or low volume).

    This only requires either:
    1. Low fill rate (< 0.1)
    2. OR low volume (< 30% of 7-day average)
    """
    # Check for low volume
    is_low_volume = False
    low_vol_reason = []
    if daily_volume_today is not None:
        # Only check relative volume compared to 7-day average
        if avg_volume_7d and avg_volume_7d > 0:
            volume_ratio = daily_volume_today / avg_volume_7d
            if volume_ratio < LOW_VOLUME_RELATIVE_THRESHOLD:
                is_low_volume = True
                low_vol_reason.append(f"daily volume {daily_volume_today:.0f} < relative threshold ({LOW_VOLUME_RELATIVE_THRESHOLD*100:.0f}% of 7d avg {avg_volume_7d:.0f})")

    # Check for low fill rate
    is_low_fill_rate = False
    fill_rate_reason = ""
    if daily_volume_today is not None and total_sell_order_quantity and total_sell_order_quantity > 0:
        fill_rate = daily_volume_today / total_sell_order_quantity
        if fill_rate < LOW_FILL_RATE_THRESHOLD:
            is_low_fill_rate = True
            fill_rate_reason = f"estimated fill rate {fill_rate:.2f} < threshold {LOW_FILL_RATE_THRESHOLD:.2f}"

    # Either condition can trigger the warning
    if is_low_volume or is_low_fill_rate:
        # Calculate spread for informational purposes
        spread_info = ""
        if highest_buy_price and lowest_sell_price and highest_buy_price > 0:
            spread_buy_based = (lowest_sell_price - highest_buy_price) / highest_buy_price
            spread_info = f" (Spread: {spread_buy_based:.2f} or {spread_buy_based*100:.0f}%)"

        tag = "(Warning) Low turnover trap"
        reasons = []
        if is_low_volume: reasons.append(f"low volume ({', '.join(low_vol_reason)})")
        if is_low_fill_rate: reasons.append(f"low fill rate ({fill_rate_reason})")
        explanation = (
            f"The item suffers from low trading activity ({'; '.join(reasons)}){spread_info}. "
            f"This indicates it might be difficult to actually buy or sell at the displayed prices, potentially trapping capital.\n"
            f"Condition: Low Volume OR Low Fill Rate (< {LOW_FILL_RATE_THRESHOLD})"
        )
        return {"tag": tag, "explanation": explanation}

    return None

def check_price_spike(
    high_price_today: Optional[float],
    avg_high_past_7d: Optional[float],
    volume_today: Optional[float],
    avg_volume_7d: Optional[float]
) -> Optional[Dict[str, str]]:
    if high_price_today and avg_high_past_7d and volume_today is not None and avg_volume_7d:
        if avg_high_past_7d > 0 and avg_volume_7d > 0:
            condition_met = (high_price_today > (PRICE_SPIKE_HIGH_FACTOR * avg_high_past_7d) and
                             volume_today < (PRICE_SPIKE_VOLUME_FACTOR * avg_volume_7d))
            if condition_met:
                tag = "(Anomaly) Price anomaly / possible manipulation"
                explanation = (
                    f"The item's highest price today is significantly above its 7-day average high, while today's trading volume is much lower than the 7-day average. "
                    f"This could indicate a price spike due to low liquidity or potential market manipulation.\n"
                    f"Condition: (High Price Today > {PRICE_SPIKE_HIGH_FACTOR:.1f}x 7d Avg High) AND (Volume Today < {PRICE_SPIKE_VOLUME_FACTOR:.1f}x 7d Avg Volume)"
                )
                return {"tag": tag, "explanation": explanation}
    return None

def check_dead_listings(
    current_sell_orders: List[Dict[str, Any]],
    historical_data_last_3_days: List[Dict[str, Any]],
    daily_volume_today: Optional[float],
    avg_volume_7d: Optional[float],
    item_name: Optional[str]
) -> Optional[Dict[str, str]]:
    old_sell_orders_exist = False
    now_utc = datetime.now(timezone.utc)
    if current_sell_orders:
        for order in current_sell_orders:
            issued_str = order.get('issued')
            if issued_str:
                try:
                    issued_dt = datetime.fromisoformat(issued_str.replace('Z', '+00:00'))
                    if (now_utc - issued_dt) > timedelta(hours=DEAD_LISTINGS_ORDER_AGE_HOURS):
                        old_sell_orders_exist = True
                        break
                except ValueError:
                    print(f"Warning: Could not parse order issued timestamp '{issued_str}'")
                    continue
    if not old_sell_orders_exist: return None

    avg_price_flat = False
    if len(historical_data_last_3_days) >= 2:
        prices = [d['average'] for d in historical_data_last_3_days if 'average' in d]
        if len(prices) >= 2:
            price_changes = [abs(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] > 0]
            if all(change < DEAD_LISTINGS_AVG_PRICE_FLAT_PERCENTAGE for change in price_changes):
                avg_price_flat = True
    if not avg_price_flat: return None

    is_low_volume = False
    low_vol_reason_dl = []
    if daily_volume_today is not None:
        # Only check relative volume compared to 7-day average
        if avg_volume_7d and avg_volume_7d > 0 and (daily_volume_today / avg_volume_7d) < LOW_VOLUME_RELATIVE_THRESHOLD:
            is_low_volume = True
            low_vol_reason_dl.append(f"daily vol < {LOW_VOLUME_RELATIVE_THRESHOLD*100:.0f}% of 7d avg")

    if is_low_volume:
        tag = "(Info) Dead market"
        explanation = (
            f"Sell orders have persisted for over {DEAD_LISTINGS_ORDER_AGE_HOURS} hours, the average price has been flat (change < {DEAD_LISTINGS_AVG_PRICE_FLAT_PERCENTAGE*100:.0f}% over 3 days), and current volume is low ({', '.join(low_vol_reason_dl)}). "
            f"This suggests the item is stale and not actively trading.\n"
            f"Condition: (Old Sell Orders > {DEAD_LISTINGS_ORDER_AGE_HOURS}h) AND (Avg Price Flat) AND (Low Volume)"
        )
        return {"tag": tag, "explanation": explanation}
    return None

def check_order_staleness(
    current_buy_orders: List[Dict[str, Any]],
    current_sell_orders: List[Dict[str, Any]]
) -> Optional[Dict[str, str]]:
    now_utc = datetime.now(timezone.utc)

    def get_top_n_stale_price_levels(orders: List[Dict[str, Any]], n: int, order_type_is_buy: bool) -> bool:
        if not orders: return False
        price_levels: Dict[float, datetime] = {}
        for order in orders:
            price = order.get('price')
            issued_str = order.get('issued')
            if price is not None and issued_str:
                try:
                    issued_dt = datetime.fromisoformat(issued_str.replace('Z', '+00:00'))
                    if price not in price_levels or issued_dt < price_levels[price]:
                        price_levels[price] = issued_dt
                except ValueError: continue
        if not price_levels: return False
        sorted_prices = sorted(price_levels.keys(), reverse=order_type_is_buy)
        stale_count = 0
        for i, price in enumerate(sorted_prices[:n]):
            if (now_utc - price_levels[price]) > timedelta(hours=ORDER_STALENESS_AGE_HOURS):
                stale_count += 1
        return stale_count >= min(n, len(sorted_prices))

    top_buy_stale = get_top_n_stale_price_levels(current_buy_orders, ORDER_STALENESS_TOP_N_ORDERS, True)
    top_sell_stale = get_top_n_stale_price_levels(current_sell_orders, ORDER_STALENESS_TOP_N_ORDERS, False)

    if top_buy_stale and top_sell_stale:
        tag = "(Info) Stale orders"
        explanation = (
            f"The top {ORDER_STALENESS_TOP_N_ORDERS} buy and sell order price levels have not changed for over {ORDER_STALENESS_AGE_HOURS} hours. "
            f"This indicates very low trading activity or that nobody is updating orders at the current best prices.\n"
            f"Condition: Top {ORDER_STALENESS_TOP_N_ORDERS} Buy/Sell Price Levels Unchanged > {ORDER_STALENESS_AGE_HOURS}h"
        )
        return {"tag": tag, "explanation": explanation}
    return None

def check_healthy_liquidity(
    highest_buy_price: Optional[float],
    lowest_sell_price: Optional[float],
    historical_data: List[Dict[str, Any]],
    avg_volume_7d: Optional[float],
    item_name: Optional[str]
) -> Optional[Dict[str, str]]:
    if not (highest_buy_price and lowest_sell_price and historical_data and avg_volume_7d): return None
    if lowest_sell_price <= 0: return None

    spread_sell_based = (lowest_sell_price - highest_buy_price) / lowest_sell_price
    if spread_sell_based >= HEALTHY_LIQUIDITY_SPREAD_THRESHOLD_SELL_BASED: return None

    if len(historical_data) < HEALTHY_LIQUIDITY_VOLUME_CONSISTENCY_DAYS: return None
    consistent_volume = True
    for day_data in historical_data[-HEALTHY_LIQUIDITY_VOLUME_CONSISTENCY_DAYS:]:
        daily_vol = day_data.get('volume')
        if daily_vol is None or avg_volume_7d == 0: consistent_volume = False; break
        if (daily_vol / avg_volume_7d) < HEALTHY_LIQUIDITY_VOLUME_FACTOR: consistent_volume = False; break
    if not consistent_volume: return None

    avg_vol_threshold = HEALTHY_LIQUIDITY_AVG_VOLUME_SHIPS_MODULES if _is_item_ship_or_rare(item_name) else HEALTHY_LIQUIDITY_AVG_VOLUME_DEFAULT
    if avg_volume_7d < avg_vol_threshold: return None

    avg_price_7d_hist = _get_historical_average(historical_data, 'average', 7)
    if not avg_price_7d_hist: return None

    price_dev_ok = (abs(avg_price_7d_hist - highest_buy_price) / highest_buy_price < HEALTHY_LIQUIDITY_PRICE_DEVIATION_THRESHOLD and \
                    abs(avg_price_7d_hist - lowest_sell_price) / lowest_sell_price < HEALTHY_LIQUIDITY_PRICE_DEVIATION_THRESHOLD)
    if not price_dev_ok: return None

    tag = "(OK) Liquid and safe"
    explanation = (
        f"The item shows signs of healthy liquidity: a tight market spread (< {HEALTHY_LIQUIDITY_SPREAD_THRESHOLD_SELL_BASED*100:.0f}% sell-based), "
        f"consistently high volume (last {HEALTHY_LIQUIDITY_VOLUME_CONSISTENCY_DAYS} days > {HEALTHY_LIQUIDITY_VOLUME_FACTOR*100:.0f}% of 7d avg), "
        f"sufficient 7-day average volume (> {avg_vol_threshold:.0f}), and the 7-day average price is close (within {HEALTHY_LIQUIDITY_PRICE_DEVIATION_THRESHOLD*100:.0f}%) to current top buy/sell prices. "
        f"This suggests fast turnover and safer trading conditions.\n"
        f"Conditions: Spread < {HEALTHY_LIQUIDITY_SPREAD_THRESHOLD_SELL_BASED*100:.0f}%; Volume Consistently High; Avg Volume > Threshold; Avg Price Near Top Orders."
    )
    return {"tag": tag, "explanation": explanation}

def _check_wall_condition(
    orders: List[Dict[str, Any]],
    total_quantity_all_orders: int,
    current_best_price_for_side: float,
    historical_data: List[Dict[str, Any]],
    is_sell_wall: bool
) -> bool:
    if not orders or total_quantity_all_orders == 0 or not current_best_price_for_side: return False
    sorted_orders = sorted(orders, key=lambda o: o['price'], reverse=(not is_sell_wall))
    top_n_quantity = 0
    distinct_prices_count = 0
    last_price = None
    for order in sorted_orders:
        price = order.get('price')
        volume = order.get('volume_remain')
        if price is None or volume is None: continue
        if price != last_price:
            if distinct_prices_count >= WALL_TOP_N_ORDERS: break
            distinct_prices_count += 1
            last_price = price
        top_n_quantity += volume
    if total_quantity_all_orders > 0 and (top_n_quantity / total_quantity_all_orders) < WALL_QUANTITY_PERCENTAGE_THRESHOLD:
        return False
    avg_price_recent = _get_historical_average(historical_data, 'average', WALL_PRICE_COMPARISON_DAYS)
    if not avg_price_recent: return False
    if is_sell_wall and current_best_price_for_side <= avg_price_recent: return False
    if not is_sell_wall and current_best_price_for_side <= avg_price_recent: return False # Corrected: buy wall supports price, so current buy should be > avg
    return True

def check_sell_wall_pressure(
    current_sell_orders: List[Dict[str, Any]],
    lowest_sell_price: Optional[float],
    historical_data: List[Dict[str, Any]]
) -> Optional[Dict[str, str]]:
    total_sell_quantity = sum(o.get('volume_remain', 0) for o in current_sell_orders)
    if lowest_sell_price and _check_wall_condition(current_sell_orders, total_sell_quantity, lowest_sell_price, historical_data, True):
        tag = "(Info) Sell wall"
        explanation = (
            f"A few ({WALL_TOP_N_ORDERS}) sell orders make up a large portion ({WALL_QUANTITY_PERCENTAGE_THRESHOLD*100:.0f}%+) of the total sell quantity, and the current sell price is higher than the recent average. "
            f"This indicates a 'wall' of sell orders that might be blocking upward price movement.\n"
            f"Condition: Top {WALL_TOP_N_ORDERS} Sell Orders >= {WALL_QUANTITY_PERCENTAGE_THRESHOLD*100:.0f}% of Total Sell Quantity AND Price > Recent Avg"
        )
        return {"tag": tag, "explanation": explanation}
    return None

def check_buy_wall_support(
    current_buy_orders: List[Dict[str, Any]],
    highest_buy_price: Optional[float],
    historical_data: List[Dict[str, Any]]
) -> Optional[Dict[str, str]]:
    total_buy_quantity = sum(o.get('volume_remain', 0) for o in current_buy_orders)
    if highest_buy_price and _check_wall_condition(current_buy_orders, total_buy_quantity, highest_buy_price, historical_data, False):
        tag = "(Info) Buy wall"
        explanation = (
            f"A few ({WALL_TOP_N_ORDERS}) buy orders make up a large portion ({WALL_QUANTITY_PERCENTAGE_THRESHOLD*100:.0f}%+) of the total buy quantity, and the current buy price is higher than the recent average. "
            f"This indicates strong demand or a 'wall' of buy orders supporting the price.\n"
            f"Condition: Top {WALL_TOP_N_ORDERS} Buy Orders >= {WALL_QUANTITY_PERCENTAGE_THRESHOLD*100:.0f}% of Total Buy Quantity AND Price > Recent Avg"
        )
        return {"tag": tag, "explanation": explanation}
    return None

def check_volume_collapse(
    daily_volume_today: Optional[float],
    avg_volume_7d: Optional[float]
) -> Optional[Dict[str, str]]:
    if daily_volume_today is not None and avg_volume_7d and avg_volume_7d > 0:
        if (daily_volume_today / avg_volume_7d) < VOLUME_COLLAPSE_FACTOR:
            tag = "(Alert) Volume crash"
            explanation = (
                f"Today's trading volume is less than {VOLUME_COLLAPSE_FACTOR*100:.0f}% of the 7-day average volume. "
                f"This sharp decrease suggests the market for this item is drying up, which could be a signal to withdraw.\n"
                f"Condition: Daily Volume < {VOLUME_COLLAPSE_FACTOR*100:.0f}% of 7d Avg Volume"
            )
            return {"tag": tag, "explanation": explanation}
    return None

def check_spread_compression(
    historical_data: List[Dict[str, Any]]
) -> Optional[Dict[str, str]]:
    # This check remains problematic due to ESI history data limitations.
    # It requires daily buy/sell prices, not just avg/high/low.
    tag = "(Info) Spread Compression (Data Limited)"
    explanation = (
        f"This check aims to detect if the market spread ((sell-buy)/sell) has been steadily decreasing while volume increases over {SPREAD_COMPRESSION_DAYS}+ days, indicating a 'hot' market. "
        f"However, standard ESI daily history does not provide daily buy/sell prices, only overall daily average, high, and low. "
        f"Therefore, this specific check cannot be accurately performed with the available data."
    )
    # To avoid cluttering the UI with this every time, we might only return it if specifically requested
    # For now, let's not add it to the default list of tags.
    # If you want to show it: return {"tag": tag, "explanation": explanation}
    return None


# --- Main Analyzer Function ---

def analyze_item_market(
    current_orders: Dict[str, List[Dict[str, Any]]],
    historical_data: List[Dict[str, Any]],
    item_id: int,
    item_name: Optional[str]
) -> List[Dict[str, str]]:
    """
    Analyzes market data for an item and returns a list of detected pattern tags with explanations.

    Args:
        current_orders: Dictionary with 'buy' and 'sell' keys, each containing a list of order dictionaries
        historical_data: List of historical market data dictionaries
        item_id: The EVE Online item type ID
        item_name: The name of the item (optional)

    Returns:
        A list of dictionaries, each with "tag" and "explanation" keys.
    """
    results: List[Dict[str, str]] = []
    if not current_orders or not historical_data:
        results.append({
            "tag": "(Warning) Insufficient data",
            "explanation": "Could not perform full analysis due to missing current market orders or historical data."
        })
        return results

    highest_buy_price, lowest_sell_price = _get_current_best_prices(current_orders)
    avg_price_7d = _get_historical_average(historical_data, 'average', 7)
    avg_volume_7d = _get_historical_average(historical_data, 'volume', 7)
    avg_high_past_7d = _get_historical_average(historical_data, 'highest', 7)

    most_recent_day = _get_most_recent_completed_day_data(historical_data)
    daily_volume_today = most_recent_day.get('volume') if most_recent_day else None
    high_price_today = most_recent_day.get('highest') if most_recent_day else None

    current_sell_orders_list = current_orders.get('sell', [])
    current_buy_orders_list = current_orders.get('buy', [])
    total_sell_order_quantity = sum(o.get('volume_remain', 0) for o in current_sell_orders_list)
    historical_data_last_3_days = historical_data[-3:] if len(historical_data) >=3 else historical_data

    analysis_functions = [
        check_fake_margin, check_low_turnover, check_price_spike, check_dead_listings,
        check_order_staleness, check_healthy_liquidity, check_sell_wall_pressure,
        check_buy_wall_support, check_volume_collapse
        # check_spread_compression is omitted by default due to data limitations
    ]

    # Arguments for each function might vary, so we call them individually with specific args.
    # This is a bit verbose but clear. A more dynamic approach could use inspect.

    res = check_fake_margin(highest_buy_price, lowest_sell_price, avg_price_7d)
    if res: results.append(res)

    res = check_low_turnover(highest_buy_price, lowest_sell_price, daily_volume_today, avg_volume_7d, total_sell_order_quantity, item_name)
    if res: results.append(res)

    res = check_price_spike(high_price_today, avg_high_past_7d, daily_volume_today, avg_volume_7d)
    if res: results.append(res)

    res = check_dead_listings(current_sell_orders_list, historical_data_last_3_days, daily_volume_today, avg_volume_7d, item_name)
    if res: results.append(res)

    res = check_order_staleness(current_buy_orders_list, current_sell_orders_list)
    if res: results.append(res)

    res = check_healthy_liquidity(highest_buy_price, lowest_sell_price, historical_data, avg_volume_7d, item_name)
    if res: results.append(res)

    res = check_sell_wall_pressure(current_sell_orders_list, lowest_sell_price, historical_data)
    if res: results.append(res)

    res = check_buy_wall_support(current_buy_orders_list, highest_buy_price, historical_data)
    if res: results.append(res)

    res = check_volume_collapse(daily_volume_today, avg_volume_7d)
    if res: results.append(res)

    # Spread compression note:
    # spread_comp_res = check_spread_compression(historical_data)
    # if spread_comp_res: results.append(spread_comp_res)


    if not results: # If no specific patterns were detected by the above checks
        results.append({
            "tag": "(OK) No specific adverse patterns detected",
            "explanation": "Based on the implemented checks, the market for this item does not show any major adverse patterns. Standard caution is always advised."
        })

    return results
