import sqlite3
from decimal import Decimal, InvalidOperation
from collections import defaultdict
from typing import Dict, List, Any, Optional

from models.database import DatabaseManager
# Potentially need strategy names defined elsewhere or repeat them here
from controllers.strategy_controller import (
    STRATEGY_MARGIN, STRATEGY_SPECULATION, STRATEGY_ARBITRAGE,
    STRATEGY_LOSS, STRATEGY_UNCLASSIFIED
)

# Define a structure for performance results
class StrategyPerformance:
    def __init__(self, strategy_name: str):
        self.strategy_name = strategy_name
        self.total_profit = Decimal('0')
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_volume_sold = Decimal('0') # Total ISK value of sales
        # ROI calculation needs capital invested - harder to track accurately without linking buys/sells
        # Win rate and avg profit are more straightforward from transaction data

    @property
    def win_rate(self) -> float:
        if self.total_trades == 0:
            return 0.0
        return (self.winning_trades / self.total_trades) * 100

    @property
    def average_profit_per_trade(self) -> Decimal:
        if self.total_trades == 0:
            return Decimal('0')
        return self.total_profit / self.total_trades

    def add_trade(self, profit: Decimal, sell_value: Decimal):
        self.total_trades += 1
        self.total_profit += profit
        self.total_volume_sold += sell_value
        if profit > 0:
            self.winning_trades += 1
        elif profit < 0:
            self.losing_trades += 1
        # Trades with zero profit are counted in total but not win/loss

    def to_dict(self) -> Dict[str, Any]:
         return {
              "strategy": self.strategy_name,
              "total_profit": self.total_profit,
              "total_trades": self.total_trades,
              "winning_trades": self.winning_trades,
              "losing_trades": self.losing_trades,
              "win_rate_percent": self.win_rate,
              "avg_profit_per_trade": self.average_profit_per_trade,
              "total_volume_sold": self.total_volume_sold,
         }


class PerformanceController:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def get_performance_by_strategy(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict[str, StrategyPerformance]:
        """
        Calculates performance metrics grouped by strategy based on SELL transactions.
        Filters by date range if provided (YYYY-MM-DD format).
        """
        print(f"Calculating performance by strategy (Range: {start_date} to {end_date})...")
        performance_results: Dict[str, StrategyPerformance] = defaultdict(lambda: StrategyPerformance(STRATEGY_UNCLASSIFIED))

        query = """
            SELECT
                strategy,
                profit,
                net_amount -- Use net_amount of sell transaction as sell value
            FROM transactions
            WHERE transaction_type = 'SELL'
              AND profit IS NOT NULL -- Only include transactions where profit was calculated
              AND strategy IS NOT NULL -- Only include classified transactions
        """
        params = []
        if start_date:
            query += " AND date(timestamp) >= date(?)"
            params.append(start_date)
        if end_date:
            query += " AND date(timestamp) <= date(?)"
            params.append(end_date)

        try:
            rows = self.db.execute_query(query, tuple(params), fetch_all=True)

            if not rows:
                print("No relevant transaction data found for performance analysis.")
                return {}

            print(f"Processing {len(rows)} SELL transactions for performance analysis...")
            for row in rows:
                strategy = row['strategy']
                try:
                    # Ensure profit and net_amount are Decimals
                    profit = Decimal(row['profit'])
                    # Net amount for sell is positive revenue after fees/tax
                    sell_value = Decimal(row['net_amount'])
                    if sell_value < 0: # Should be positive for sell
                         print(f"Warning: Negative net_amount {sell_value} found for sell transaction (Strategy: {strategy}). Using abs().")
                         sell_value = abs(sell_value)

                except (InvalidOperation, TypeError, ValueError) as e:
                    print(f"Warning: Skipping row due to invalid data type for profit/net_amount (Strategy: {strategy}): {e}")
                    continue

                # Initialize performance object if not seen before
                if strategy not in performance_results:
                    performance_results[strategy] = StrategyPerformance(strategy)

                # Add trade data to the corresponding strategy
                performance_results[strategy].add_trade(profit, sell_value)

            print("Performance analysis complete.")
            # Convert defaultdict back to regular dict for return
            return dict(performance_results)

        except Exception as e:
            print(f"An error occurred during performance calculation: {e}")
            return {} # Return empty dict on error

    def get_overall_performance(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict[str, Any]:
         """ Calculates overall performance across all strategies. """
         print(f"Calculating overall performance (Range: {start_date} to {end_date})...")
         overall = StrategyPerformance("Overall")
         strategy_performance = self.get_performance_by_strategy(start_date, end_date)

         if not strategy_performance:
              print("No strategy performance data to aggregate.")
              return overall.to_dict() # Return default overall dict

         for strategy_result in strategy_performance.values():
              overall.total_profit += strategy_result.total_profit
              overall.total_trades += strategy_result.total_trades
              overall.winning_trades += strategy_result.winning_trades
              overall.losing_trades += strategy_result.losing_trades
              overall.total_volume_sold += strategy_result.total_volume_sold

         print("Overall performance calculation complete.")
         return overall.to_dict()


# Example Usage
if __name__ == '__main__':
    print("--- Performance Controller Example ---")
    db_manager = DatabaseManager()
    performance_controller = PerformanceController(db_manager)

    print("\n--- Calculating Performance by Strategy (All Time) ---")
    perf_by_strategy = performance_controller.get_performance_by_strategy()

    if perf_by_strategy:
        for strategy, metrics in perf_by_strategy.items():
            print(f"\nStrategy: {strategy}")
            print(f"  Total Profit: {metrics.total_profit:,.2f} ISK")
            print(f"  Total Trades: {metrics.total_trades}")
            print(f"  Win Rate: {metrics.win_rate:.2f}%")
            print(f"  Avg Profit/Trade: {metrics.average_profit_per_trade:,.2f} ISK")
            print(f"  Total Volume Sold: {metrics.total_volume_sold:,.2f} ISK")
    else:
        print("No performance data found.")

    print("\n--- Calculating Overall Performance (All Time) ---")
    overall_perf = performance_controller.get_overall_performance()
    if overall_perf['total_trades'] > 0:
         print(f"\nOverall Performance:")
         print(f"  Total Profit: {overall_perf['total_profit']:,.2f} ISK")
         print(f"  Total Trades: {overall_perf['total_trades']}")
         print(f"  Win Rate: {overall_perf['win_rate_percent']:.2f}%")
         print(f"  Avg Profit/Trade: {overall_perf['avg_profit_per_trade']:,.2f} ISK")
         print(f"  Total Volume Sold: {overall_perf['total_volume_sold']:,.2f} ISK")
    else:
         print("No overall performance data found.")

    # Example with date range
    # print("\n--- Calculating Performance by Strategy (Date Range Example) ---")
    # perf_by_strategy_range = performance_controller.get_performance_by_strategy(start_date='2024-01-01', end_date='2024-01-31')
    # # ... print results ...
