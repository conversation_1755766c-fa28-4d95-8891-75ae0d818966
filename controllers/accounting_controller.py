from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, Dict, List

# Import models
from models.database import DatabaseManager
from models.transaction import Transaction
from api.esi_client import ESIClient  # Needed for fetching skills

# Constants from design doc (or fetch dynamically)
BASE_SALES_TAX_RATE = Decimal('0.075') # 7.5%
BASE_BROKER_FEE_RATE = Decimal('0.03') # 3%
ACCOUNTING_SKILL_ID = 16622 # EVE Online Skill ID for Accounting
BROKER_RELATIONS_SKILL_ID = 3446 # EVE Online Skill ID for Broker Relations

# Jita constants (can be moved to a config file)
JITA_STATION_ID = ********

class AccountingController:
    def __init__(self, db_manager: DatabaseManager, esi_client: Optional[ESIClient] = None):
        self.db = db_manager
        self.esi_client = esi_client
        # Cache for skill levels to avoid repeated ESI calls
        self._skill_cache: Dict[int, int] = {} # {skill_id: level}
        # Create an inventory controller instance
        from controllers.inventory_controller import InventoryController
        self.inventory = InventoryController(db_manager)

    def _get_skill_level(self, character_id: int, skill_id: int) -> int:
        """ Fetches skill level from ESI, using cache if available. """
        if not self.esi_client:
            print("Warning: ESIClient not provided to AccountingController. Cannot fetch skill levels.")
            return 0 # Default to level 0 if no ESI client

        # Check cache first
        if skill_id in self._skill_cache:
            return self._skill_cache[skill_id]

        print(f"Fetching skills for character {character_id}...")
        # Ensure the client is set for the correct character
        self.esi_client.character_id = character_id
        skills_data = self.esi_client.get_character_skills()

        if skills_data and 'skills' in skills_data:
            skill_level = 0
            for skill in skills_data['skills']:
                # Cache all relevant skills found
                if skill['skill_id'] in [ACCOUNTING_SKILL_ID, BROKER_RELATIONS_SKILL_ID]:
                     self._skill_cache[skill['skill_id']] = skill.get('active_skill_level', 0)

                if skill['skill_id'] == skill_id:
                    skill_level = skill.get('active_skill_level', 0)
                    # No break here, continue caching other skills

            print(f"Skill {skill_id} level: {skill_level}")
            # Even if the specific skill wasn't found, cache the attempt (as level 0)
            if skill_id not in self._skill_cache:
                 self._skill_cache[skill_id] = 0
            return self._skill_cache[skill_id]
        else:
            print(f"Could not fetch skills for character {character_id}.")
            # Cache the failure for this specific skill ID to avoid retrying immediately
            self._skill_cache[skill_id] = 0
            return 0

    def calculate_sales_tax(self, base_amount: Decimal, character_id: int, accounting_level: Optional[int] = None) -> Decimal:
        """ Calculates sales tax based on Accounting skill level. """
        if accounting_level is None:
            accounting_level = self._get_skill_level(character_id, ACCOUNTING_SKILL_ID)

        # Formula: final_tax = base_tax * (1 - (0.11 * accounting_level))
        skill_reduction = Decimal('0.11') * Decimal(accounting_level)
        final_tax_rate = BASE_SALES_TAX_RATE * (Decimal('1') - skill_reduction)

        # Ensure tax rate isn't negative (shouldn't happen with EVE skills)
        final_tax_rate = max(Decimal('0'), final_tax_rate)

        # Calculate tax amount and round to 2 decimal places (ISK standard)
        tax_amount = (base_amount * final_tax_rate).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        print(f"Sales Tax Calculation: Base={base_amount}, AccLvl={accounting_level}, Rate={final_tax_rate:.4f}, Tax={tax_amount}")
        return tax_amount

    def calculate_broker_fee(self, base_amount: Decimal, character_id: int, broker_relations_level: Optional[int] = None, faction_standing: Decimal = Decimal('0'), corp_standing: Decimal = Decimal('0')) -> Decimal:
        """ Calculates broker fee based on Broker Relations skill and standings. """
        # Note: Standings calculation can be complex (NPC corp vs player corp at station).
        # This is a simplified version assuming NPC station (Jita 4-4) and character standings.
        # Fetching actual standings would require more ESI calls/scopes.
        # For now, we'll use provided standings or default to 0.

        if broker_relations_level is None:
            broker_relations_level = self._get_skill_level(character_id, BROKER_RELATIONS_SKILL_ID)

        # Formula: broker_fee = 3% - (0.3% * broker_relations_level) - (0.03% * faction_standing) - (0.02% * corp_standing)
        # Convert percentages to decimals for calculation
        skill_reduction = Decimal('0.003') * Decimal(broker_relations_level)
        faction_standing_reduction = Decimal('0.0003') * faction_standing
        corp_standing_reduction = Decimal('0.0002') * corp_standing # Check if this applies correctly in Jita 4-4

        effective_rate = BASE_BROKER_FEE_RATE - skill_reduction - faction_standing_reduction - corp_standing_reduction

        # Apply minimum broker fee rate (1% according to design doc, but check EVE rules - often 0.5% or lower possible)
        # Let's assume a minimum based on EVE mechanics, e.g., 0.5% might be achievable
        minimum_rate = Decimal('0.005') # Example minimum 0.5%
        final_broker_rate = max(minimum_rate, effective_rate)

        # Calculate fee amount and round
        fee_amount = (base_amount * final_broker_rate).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        print(f"Broker Fee Calculation: Base={base_amount}, BRLvl={broker_relations_level}, Rate={final_broker_rate:.4f}, Fee={fee_amount}")
        return fee_amount

    # --- Inventory Access ---

    def get_inventory_item(self, item_id: int, location_id: int) -> Optional[Dict]:
        """
        Get inventory information for an item at a specific location.
        This is a bridge method that uses the InventoryController to get inventory data.

        Args:
            item_id: The item ID to look up
            location_id: The location ID where the item is stored

        Returns:
            A dictionary with inventory information or None if not found
        """
        try:
            # Get the character ID from the ESI client
            character_id = self.esi_client.character_id if self.esi_client else None

            if not character_id:
                print("Warning: No character ID available for inventory lookup")
                return None

            # Get available batches for this item at this location
            batches = self.inventory.get_available_batches(item_id, location_id, character_id)

            if not batches:
                print(f"No inventory batches found for item {item_id} at location {location_id}")
                return None

            # Combine batches into a single inventory item representation
            total_quantity = sum(batch.quantity_remaining for batch in batches)
            total_cost = sum(batch.unit_cost * batch.quantity_remaining for batch in batches)
            avg_unit_cost = total_cost / total_quantity if total_quantity > 0 else Decimal('0')

            # Use the oldest batch's purchase date as the acquisition date
            oldest_batch = min(batches, key=lambda b: b.purchase_date if b.purchase_date else datetime.max.replace(tzinfo=timezone.utc))
            acquisition_date = oldest_batch.purchase_date

            return {
                'item_id': item_id,
                'location_id': location_id,
                'quantity': total_quantity,
                'avg_unit_cost': avg_unit_cost,
                'total_cost': total_cost,
                'acquisition_date': acquisition_date
            }
        except Exception as e:
            print(f"Error getting inventory item {item_id} at location {location_id}: {e}")
            import traceback
            print(traceback.format_exc())
            return None

    # --- Profit Calculation ---

    def calculate_profit_for_sell_direct(self, sell_transaction: Transaction) -> Optional[Decimal]:
        """
        Calculates profit for a SELL transaction without using inventory.
        This method estimates profit based on recent BUY transactions for the same item.
        Updates the transaction record with the calculated profit.
        Returns the calculated profit, or None if calculation fails.

        Profit calculation includes:
        - Cost of goods sold (based on average buy price)
        - Broker's fee (paid when placing the sell order)
        - Transaction tax (paid when the sell order is fulfilled)
        - Relist fees (if the order was relisted)
        """

        if sell_transaction.transaction_type != 'SELL':
            print("Error: calculate_profit_for_sell_direct called for non-SELL transaction.")
            return None

        print(f"Calculating profit for SELL transaction {sell_transaction.tx_id} (Item: {sell_transaction.item_id})")

        # Find recent BUY transactions for the same item to estimate cost basis
        query = """
        SELECT AVG(unit_price) as avg_cost
        FROM transactions
        WHERE item_id = ? AND transaction_type = 'BUY' AND timestamp <= ?
        ORDER BY timestamp DESC
        LIMIT 10
        """

        cost_basis_row = self.db.execute_query(
            query,
            (sell_transaction.item_id, sell_transaction.timestamp),
            fetch_one=True
        )

        if not cost_basis_row or cost_basis_row['avg_cost'] is None:
            print(f"Warning: No BUY transactions found for item {sell_transaction.item_id}. Using sell price as cost basis.")
            # Fallback: use 90% of sell price as cost basis (arbitrary estimate)
            cost_basis_per_unit = sell_transaction.unit_price * Decimal('0.9')
        else:
            cost_basis_per_unit = Decimal(str(cost_basis_row['avg_cost']))

        # Calculate cost of goods sold
        cost_of_goods_sold = (sell_transaction.quantity * cost_basis_per_unit).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        # Calculate gross revenue (before fees)
        gross_revenue = sell_transaction.unit_price * sell_transaction.quantity

        # Get broker fee and sales tax from transaction if available, otherwise calculate them
        broker_fee = sell_transaction.broker_fee
        if broker_fee is None or broker_fee == Decimal('0'):
            # Calculate broker fee based on character skills
            broker_fee = self.calculate_broker_fee(gross_revenue, sell_transaction.character_id)
            # Update the transaction with the calculated broker fee
            self.db.execute_update(
                "UPDATE transactions SET broker_fee = ? WHERE tx_id = ?",
                (broker_fee, sell_transaction.tx_id)
            )
            sell_transaction.broker_fee = broker_fee

        sales_tax = sell_transaction.sales_tax
        if sales_tax is None or sales_tax == Decimal('0'):
            # Calculate sales tax based on character skills
            sales_tax = self.calculate_sales_tax(gross_revenue, sell_transaction.character_id)
            # Update the transaction with the calculated sales tax
            self.db.execute_update(
                "UPDATE transactions SET sales_tax = ? WHERE tx_id = ?",
                (sales_tax, sell_transaction.tx_id)
            )
            sell_transaction.sales_tax = sales_tax

        # Net revenue from ESI wallet transaction (is_buy=false) is positive:
        # unit_price * quantity - broker_fee - sales_tax
        # If net_amount is already provided by ESI, use that, otherwise calculate it
        net_revenue = sell_transaction.net_amount
        if net_revenue < 0:
            print(f"Warning: Net amount for SELL transaction {sell_transaction.tx_id} is negative ({net_revenue}). Using abs() value.")
            net_revenue = abs(net_revenue)

        # If net_amount doesn't match our calculation (within a small tolerance), recalculate it
        calculated_net = gross_revenue - broker_fee - sales_tax
        if abs(net_revenue - calculated_net) > Decimal('0.1'):
            print(f"Warning: Net amount from transaction ({net_revenue}) doesn't match calculated value ({calculated_net}). Using calculated value.")
            net_revenue = calculated_net
            # Update the transaction with the calculated net amount
            self.db.execute_update(
                "UPDATE transactions SET net_amount = ? WHERE tx_id = ?",
                (net_revenue, sell_transaction.tx_id)
            )
            sell_transaction.net_amount = net_revenue

        # Check for relisting fees associated with the order
        relist_fees = Decimal('0')
        if sell_transaction.order_id:
            try:
                order_row = self.db.execute_query("SELECT relist_fees FROM orders WHERE order_id = ?", (sell_transaction.order_id,), fetch_one=True)
                if order_row and order_row['relist_fees']:
                    # Ensure conversion from DB storage (likely string) to Decimal
                    relist_fees = Decimal(order_row['relist_fees'])
            except Exception as e:
                print(f"Error fetching relist fees for order {sell_transaction.order_id}: {e}")

        # Profit = Net Revenue - Cost of Goods Sold - Relist Fees
        # Note: Net Revenue already accounts for broker_fee and sales_tax
        profit = net_revenue - cost_of_goods_sold - relist_fees
        profit = profit.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        print(f"Profit Calc TX {sell_transaction.tx_id}: GrossRev={gross_revenue}, BrokerFee={broker_fee}, SalesTax={sales_tax}, NetRev={net_revenue}, COGS={cost_of_goods_sold} (Qty={sell_transaction.quantity} @ Cost={cost_basis_per_unit}), RelistFee={relist_fees}, Profit={profit}")

        # Update the transaction record in the database
        try:
            update_query = "UPDATE transactions SET profit = ? WHERE tx_id = ?"
            # Ensure profit is stored correctly (adapter should handle Decimal -> str)
            self.db.execute_update(update_query, (profit, sell_transaction.tx_id))
            print(f"Updated profit for transaction {sell_transaction.tx_id} to {profit}")
            # Update the transaction object in memory as well
            sell_transaction.profit = profit
            return profit
        except Exception as e:
            print(f"Error updating profit for transaction {sell_transaction.tx_id}: {e}")
            return None


    def recalculate_all_profits(self, progress_callback=None):
        """
        Recalculate profits for all SELL transactions in the database.
        This is useful when implementing fee accounting to update historical profit data.

        Args:
            progress_callback: Optional callback function to report progress (percent, message)

        Returns:
            Tuple of (success_count, error_count)
        """
        print("Recalculating profits for all SELL transactions...")

        # Get all SELL transactions
        query = """
        SELECT * FROM transactions
        WHERE transaction_type = 'SELL'
        ORDER BY timestamp ASC
        """

        transactions = self.db.execute_query(query, fetch_all=True)
        if not transactions:
            print("No SELL transactions found.")
            return (0, 0)

        total_count = len(transactions)
        success_count = 0
        error_count = 0

        print(f"Found {total_count} SELL transactions to process.")

        for i, tx_row in enumerate(transactions):
            try:
                # Convert row to Transaction object
                tx = Transaction.from_dict(dict(tx_row))

                # Recalculate profit
                profit = self.calculate_profit_for_sell_direct(tx)

                if profit is not None:
                    success_count += 1
                else:
                    error_count += 1

                # Report progress every 10 transactions
                if i % 10 == 0 and progress_callback:
                    progress_callback(int(100 * i / total_count), f"Processed {i}/{total_count} transactions")

            except Exception as e:
                print(f"Error recalculating profit for transaction {tx_row['tx_id']}: {e}")
                import traceback
                print(traceback.format_exc())
                error_count += 1

        if progress_callback:
            progress_callback(100, f"Completed. {success_count} successful, {error_count} errors.")

        print(f"Profit recalculation complete. {success_count} successful, {error_count} errors.")
        return (success_count, error_count)


# Example Usage (requires DB setup and potentially ESI auth)
if __name__ == '__main__':
    print("--- Accounting Controller Example ---")
    db_manager = DatabaseManager() # Assumes eve_trader.db exists

    # To test skill-based fees, you need an ESIClient and a valid character ID
    # Replace with your actual character ID that has tokens in the DB
    TEST_CHARACTER_ID = 123456
    try:
        # Check if token exists before creating ESIClient that needs it
        token_exists = db_manager.execute_query("SELECT 1 FROM api_tokens WHERE character_id = ?", (TEST_CHARACTER_ID,), fetch_one=True)
        if token_exists:
            print(f"Token found for character {TEST_CHARACTER_ID}. Initializing ESIClient.")
            # Need to instantiate ESIAuthenticator first if ESIClient relies on it implicitly
            from api.auth import ESIAuthenticator
            authenticator = ESIAuthenticator() # Assumes auth.py is configured
            esi_client = ESIClient(character_id=TEST_CHARACTER_ID, authenticator=authenticator)
            controller = AccountingController(db_manager, esi_client)

            print("\n--- Testing Fee Calculations ---")
            test_amount = Decimal('1000000.00') # 1 Million ISK

            # Test Sales Tax (assuming skill level is fetched)
            sales_tax = controller.calculate_sales_tax(test_amount, TEST_CHARACTER_ID)
            print(f"Calculated Sales Tax on {test_amount}: {sales_tax}")

            # Test Broker Fee (assuming skill level is fetched, using 0 standings)
            broker_fee = controller.calculate_broker_fee(test_amount, TEST_CHARACTER_ID)
            print(f"Calculated Broker Fee on {test_amount}: {broker_fee}")

            # Test with explicit skill levels (avoids ESI call if known)
            sales_tax_l5 = controller.calculate_sales_tax(test_amount, TEST_CHARACTER_ID, accounting_level=5)
            print(f"Calculated Sales Tax (L5 Acc) on {test_amount}: {sales_tax_l5}")
            broker_fee_l5 = controller.calculate_broker_fee(test_amount, TEST_CHARACTER_ID, broker_relations_level=5)
            print(f"Calculated Broker Fee (L5 BR) on {test_amount}: {broker_fee_l5}")

        else:
             print(f"No token found for character {TEST_CHARACTER_ID}. Skipping ESI-dependent tests.")
             # Test without ESIClient (will default skills to 0)
             controller = AccountingController(db_manager)
             print("\n--- Testing Fee Calculations (No ESI - Skills=0) ---")
             test_amount = Decimal('1000000.00')
             sales_tax = controller.calculate_sales_tax(test_amount, 0) # Char ID doesn't matter here
             print(f"Calculated Sales Tax on {test_amount}: {sales_tax}")
             broker_fee = controller.calculate_broker_fee(test_amount, 0)
             print(f"Calculated Broker Fee on {test_amount}: {broker_fee}")

    except ImportError as e:
        print(f"Import Error, likely missing ESIClient/Auth setup or incorrect relative paths: {e}")
    except Exception as e:
        print(f"An error occurred during AccountingController testing: {e}")
