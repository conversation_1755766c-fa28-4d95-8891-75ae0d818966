"""
Daily Profit Controller

This module provides a controller for retrieving and processing daily profit data.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal
import logging

from models.database import DatabaseManager

# Set up logging
logger = logging.getLogger(__name__)


class DailyProfitController:
    """Controller for retrieving and processing daily profit data."""

    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize the daily profit controller.

        Args:
            db_manager: Database manager instance
        """
        self.db = db_manager

    def get_daily_profit_data(self, time_range: str) -> List[Dict[str, Any]]:
        """
        Get daily profit data for the specified time range.

        Args:
            time_range: Time range identifier ('7d', '30d', '90d', '180d', '1y', 'all')

        Returns:
            List of daily profit data dictionaries
        """
        # Calculate start date based on time range
        start_date = self._calculate_start_date(time_range)

        # First try to get profit data directly if the profit column exists
        try:
            # Check if the profit column exists in the transactions table
            check_query = """
            PRAGMA table_info(transactions)
            """
            columns = self.db.execute_query(check_query, fetch_all=True)
            has_profit_column = any(col['name'] == 'profit' for col in columns)

            if has_profit_column:
                print("Using profit column from transactions table")
                # Build query based on whether we have a start date
                if start_date:
                    query = """
                    SELECT
                        DATE(timestamp) as date,
                        SUM(profit) as profit
                    FROM transactions
                    WHERE
                        transaction_type = 'SELL' AND
                        timestamp >= ?
                    GROUP BY DATE(timestamp)
                    ORDER BY date
                    """
                    params = (start_date.strftime('%Y-%m-%d'),)
                else:
                    # All time
                    query = """
                    SELECT
                        DATE(timestamp) as date,
                        SUM(profit) as profit
                    FROM transactions
                    WHERE transaction_type = 'SELL'
                    GROUP BY DATE(timestamp)
                    ORDER BY date
                    """
                    params = ()

                # Execute query
                results = self.db.execute_query(query, params, fetch_all=True)

                # Process results
                daily_profit_data = []

                for row in results:
                    # Convert date string to datetime object
                    date_str = row['date']
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')

                    daily_profit_data.append({
                        'date': date_obj,
                        'profit': row['profit'] or 0  # Handle NULL values
                    })

                # If we have data, return it
                if daily_profit_data:
                    return daily_profit_data

            # If we don't have a profit column or no data was found, calculate profit from transactions
            print("Calculating profit from transaction data")

            # For sell transactions, profit is the total price
            # For buy transactions, profit is negative total price
            if start_date:
                query = """
                SELECT
                    DATE(timestamp) as date,
                    SUM(CASE WHEN transaction_type = 'SELL' THEN total_price ELSE -total_price END) as profit
                FROM transactions
                WHERE timestamp >= ?
                GROUP BY DATE(timestamp)
                ORDER BY date
                """
                params = (start_date.strftime('%Y-%m-%d'),)
            else:
                # All time
                query = """
                SELECT
                    DATE(timestamp) as date,
                    SUM(CASE WHEN transaction_type = 'SELL' THEN total_price ELSE -total_price END) as profit
                FROM transactions
                GROUP BY DATE(timestamp)
                ORDER BY date
                """
                params = ()

            # Execute query
            results = self.db.execute_query(query, params, fetch_all=True)

            # Process results
            daily_profit_data = []

            for row in results:
                # Convert date string to datetime object
                date_str = row['date']
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')

                daily_profit_data.append({
                    'date': date_obj,
                    'profit': row['profit'] or 0  # Handle NULL values
                })

            return daily_profit_data

        except Exception as e:
            print(f"Error getting daily profit data: {e}")
            import traceback
            print(traceback.format_exc())

            # Return empty list in case of error
            return []

    def get_daily_profit_for_range(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """
        Get daily profit data for a specific date range.

        Args:
            start_date: Start date
            end_date: End date

        Returns:
            List of daily profit data dictionaries
        """
        try:
            # Check if the profit column exists in the transactions table
            check_query = """
            PRAGMA table_info(transactions)
            """
            columns = self.db.execute_query(check_query, fetch_all=True)
            has_profit_column = any(col['name'] == 'profit' for col in columns)

            # Format dates for SQLite
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')

            if has_profit_column:
                print("Using profit column from transactions table for date range")
                query = """
                SELECT
                    DATE(timestamp) as date,
                    SUM(profit) as profit
                FROM transactions
                WHERE
                    transaction_type = 'SELL' AND
                    timestamp >= ? AND
                    timestamp <= ?
                GROUP BY DATE(timestamp)
                ORDER BY date
                """

                # Execute query
                results = self.db.execute_query(query, (start_date_str, end_date_str), fetch_all=True)

                # Process results
                daily_profit_data = []

                for row in results:
                    # Convert date string to datetime object
                    date_str = row['date']
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')

                    daily_profit_data.append({
                        'date': date_obj,
                        'profit': row['profit'] or 0  # Handle NULL values
                    })

                # If we have data, return it
                if daily_profit_data:
                    return daily_profit_data

            # If we don't have a profit column or no data was found, calculate profit from transactions
            print("Calculating profit from transaction data for date range")

            # For sell transactions, profit is the total price
            # For buy transactions, profit is negative total price
            query = """
            SELECT
                DATE(timestamp) as date,
                SUM(CASE WHEN transaction_type = 'SELL' THEN total_price ELSE -total_price END) as profit
            FROM transactions
            WHERE
                timestamp >= ? AND
                timestamp <= ?
            GROUP BY DATE(timestamp)
            ORDER BY date
            """

            # Execute query
            results = self.db.execute_query(query, (start_date_str, end_date_str), fetch_all=True)

            # Process results
            daily_profit_data = []

            for row in results:
                # Convert date string to datetime object
                date_str = row['date']
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')

                daily_profit_data.append({
                    'date': date_obj,
                    'profit': row['profit'] or 0  # Handle NULL values
                })

            return daily_profit_data

        except Exception as e:
            print(f"Error getting daily profit data for range: {e}")
            import traceback
            print(traceback.format_exc())

            # Return empty list in case of error
            return []

    def _calculate_start_date(self, time_range: str) -> Optional[datetime]:
        """
        Calculate the start date based on the time range.

        Args:
            time_range: Time range identifier

        Returns:
            Start date or None for all time
        """
        today = datetime.now()

        if time_range == '7d':
            return today - timedelta(days=7)
        elif time_range == '30d':
            return today - timedelta(days=30)
        elif time_range == '90d':
            return today - timedelta(days=90)
        elif time_range == '180d':
            return today - timedelta(days=180)
        elif time_range == '1y':
            return today - timedelta(days=365)
        else:
            # 'all' or any other value
            return None
