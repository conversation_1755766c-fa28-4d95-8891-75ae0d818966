from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Callable

from models.database import DatabaseManager
from api.esi_client import ESIClient

# Type for progress callback
ProgressCallback = Callable[[int, str], None]

class NetWorthController:
    """
    Controller for managing net worth data.

    This controller handles:
    - Retrieving wallet balance from ESI
    - Calculating total value of buy orders (escrow)
    - Calculating total value of sell orders
    - Calculating total net worth
    - Storing historical net worth data
    - Retrieving historical net worth data for charts
    """

    def __init__(self, db_manager: DatabaseManager, esi_client: ESIClient):
        """
        Initialize the NetWorthController.

        Args:
            db_manager: Database manager instance
            esi_client: ESI client instance
        """
        self.db = db_manager
        self.esi = esi_client
        self.character_id = esi_client.character_id if esi_client else None

    def _sync_character_id(self) -> None:
        """Ensure the controller's character_id matches the ESI client's."""
        if self.esi and getattr(self.esi, "character_id", None) != self.character_id:
            self.character_id = self.esi.character_id

    def get_current_net_worth(self, force_refresh: bool = True) -> Dict[str, Decimal]:
        """
        Calculate and return the current net worth components.

        Args:
            force_refresh: Whether to force refresh data from ESI

        Returns:
            Dictionary with wallet_balance, escrow_total, sell_orders_total, and net_worth_total
        """
        self._sync_character_id()

        # Get wallet balance
        wallet_balance = self._get_wallet_balance(force_refresh)
        if wallet_balance is None:
            wallet_balance = Decimal('0')

        # Get buy orders (for escrow)
        escrow_total = self._get_escrow_total(force_refresh)

        # Get sell orders total
        sell_orders_total = self._get_sell_orders_total(force_refresh)

        # Calculate total net worth
        net_worth_total = wallet_balance + escrow_total + sell_orders_total

        return {
            'wallet_balance': wallet_balance,
            'escrow_total': escrow_total,
            'sell_orders_total': sell_orders_total,
            'net_worth_total': net_worth_total
        }

    def record_net_worth_snapshot(self, force_refresh: bool = True) -> bool:
        """
        Record the current net worth as a snapshot in the database.

        Args:
            force_refresh: Whether to force refresh data from ESI

        Returns:
            True if successful, False otherwise
        """
        self._sync_character_id()
        if not self.character_id:
            print("Error: Character ID not set for net worth snapshot.")
            return False

        # Get current net worth
        net_worth = self.get_current_net_worth(force_refresh)

        # Insert into database
        try:
            inserted_id = self.db.execute_update(
                """
                INSERT INTO net_worth_history (
                    character_id,
                    wallet_balance,
                    escrow_total,
                    sell_orders_total,
                    net_worth_total
                ) VALUES (?, ?, ?, ?, ?)
                """,
                (
                    self.character_id,
                    net_worth['wallet_balance'],
                    net_worth['escrow_total'],
                    net_worth['sell_orders_total'],
                    net_worth['net_worth_total']
                )
            )

            if inserted_id is not None:
                print(
                    f"Recorded net worth snapshot: {net_worth['net_worth_total']:,.2f} ISK"
                )
                return True
            return False
        except Exception as e:
            print(f"Error recording net worth snapshot: {e}")
            return False

    def get_net_worth_history(self, days: int = 7) -> List[Dict]:
        """
        Get historical net worth data for the specified number of days.

        Args:
            days: Number of days of history to retrieve

        Returns:
            List of dictionaries with timestamp and net worth components
        """
        self._sync_character_id()
        if not self.character_id:
            print("Error: Character ID not set for net worth history.")
            return []

        try:
            if days <= 0:
                query = """
                SELECT
                    timestamp,
                    wallet_balance,
                    escrow_total,
                    sell_orders_total,
                    net_worth_total
                FROM net_worth_history
                WHERE character_id = ?
                ORDER BY timestamp ASC
                """
                params = (self.character_id,)
            else:
                query = """
                SELECT
                    timestamp,
                    wallet_balance,
                    escrow_total,
                    sell_orders_total,
                    net_worth_total
                FROM net_worth_history
                WHERE character_id = ?
                AND timestamp >= datetime('now', ?)
                ORDER BY timestamp ASC
                """
                params = (self.character_id, f"-{days} days")

            rows = self.db.execute_query(query, params, fetch_all=True)

            if not rows:
                return []

            return [dict(row) for row in rows]
        except Exception as e:
            print(f"Error retrieving net worth history: {e}")
            return []

    def _get_wallet_balance(self, force_refresh: bool = True) -> Optional[Decimal]:
        """
        Get the character's wallet balance from ESI.

        Args:
            force_refresh: Whether to force refresh data from ESI

        Returns:
            Wallet balance as Decimal, or None if retrieval fails
        """
        if not self.esi or not self.character_id:
            print("Error: ESI client or character ID not set for wallet balance.")
            return None

        try:
            balance = self.esi.get_wallet_balance(force_refresh=force_refresh)
            if balance is not None:
                return Decimal(str(balance))
            return None
        except Exception as e:
            print(f"Error retrieving wallet balance: {e}")
            return None

    def _get_escrow_total(self, force_refresh: bool = True) -> Decimal:
        """
        Calculate the total ISK in escrow from buy orders.

        Args:
            force_refresh: Whether to force refresh data from ESI

        Returns:
            Total escrow amount as Decimal
        """
        if not self.esi or not self.character_id:
            print("Error: ESI client or character ID not set for escrow calculation.")
            return Decimal('0')

        try:
            orders = self.esi.get_character_orders(force_refresh=force_refresh)
            if not orders:
                return Decimal('0')

            # Sum escrow from buy orders
            escrow_total = Decimal('0')
            for order in orders:
                if order.get('is_buy_order', False) and order.get('escrow'):
                    escrow_total += Decimal(str(order['escrow']))

            return escrow_total
        except Exception as e:
            print(f"Error calculating escrow total: {e}")
            return Decimal('0')

    def _get_sell_orders_total(self, force_refresh: bool = True) -> Decimal:
        """
        Calculate the total value of active sell orders.

        Args:
            force_refresh: Whether to force refresh data from ESI

        Returns:
            Total value of sell orders as Decimal
        """
        if not self.esi or not self.character_id:
            print("Error: ESI client or character ID not set for sell orders calculation.")
            return Decimal('0')

        try:
            orders = self.esi.get_character_orders(force_refresh=force_refresh)
            if not orders:
                return Decimal('0')

            # Sum value of sell orders
            sell_orders_total = Decimal('0')
            for order in orders:
                if not order.get('is_buy_order', True):  # Sell orders have is_buy_order=false
                    price = Decimal(str(order.get('price', 0)))
                    volume = Decimal(str(order.get('volume_remain', 0)))
                    sell_orders_total += price * volume

            return sell_orders_total
        except Exception as e:
            print(f"Error calculating sell orders total: {e}")
            return Decimal('0')
