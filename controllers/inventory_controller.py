from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP
from typing import List, Optional, Dict, Tuple

from models.database import DatabaseManager
from models.transaction import Transaction

class InventoryBatch:
    """Represents a batch of items purchased at the same time and price."""
    def __init__(self,
                 batch_id: Optional[int] = None,
                 item_id: int = 0,
                 item_name: str = "",
                 purchase_date: datetime = None,
                 quantity_original: int = 0,
                 quantity_remaining: int = 0,
                 unit_cost: Decimal = Decimal('0'),
                 total_cost: Decimal = Decimal('0'),
                 location_id: int = 0,
                 character_id: int = 0,
                 order_id: Optional[int] = None,
                 trade_execution_id: Optional[int] = None,
                 strategy_intent: Optional[str] = None,
                 transaction_id: Optional[int] = None):
        self.batch_id = batch_id
        self.item_id = item_id
        self.item_name = item_name
        self.purchase_date = purchase_date or datetime.now()
        self.quantity_original = quantity_original
        self.quantity_remaining = quantity_remaining
        self.unit_cost = unit_cost
        self.total_cost = total_cost
        self.location_id = location_id
        self.character_id = character_id
        self.order_id = order_id
        self.trade_execution_id = trade_execution_id
        self.strategy_intent = strategy_intent
        self.transaction_id = transaction_id

    def to_dict(self) -> Dict:
        """Convert to dictionary for database operations."""
        return {
            'batch_id': self.batch_id,
            'item_id': self.item_id,
            'item_name': self.item_name,
            'purchase_date': self.purchase_date.isoformat() if self.purchase_date else None,
            'quantity_original': self.quantity_original,
            'quantity_remaining': self.quantity_remaining,
            'unit_cost': str(self.unit_cost),
            'total_cost': str(self.total_cost),
            'location_id': self.location_id,
            'character_id': self.character_id,
            'order_id': self.order_id,
            'trade_execution_id': self.trade_execution_id,
            'strategy_intent': self.strategy_intent,
            'transaction_id': self.transaction_id
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'InventoryBatch':
        """Create an InventoryBatch from a dictionary (e.g., DB row)."""
        # Handle datetime conversion
        if 'purchase_date' in data and data['purchase_date']:
            if isinstance(data['purchase_date'], str):
                data['purchase_date'] = datetime.fromisoformat(data['purchase_date'].replace('Z', '+00:00'))

        # Handle Decimal conversion
        if 'unit_cost' in data and not isinstance(data['unit_cost'], Decimal):
            data['unit_cost'] = Decimal(str(data['unit_cost']))
        if 'total_cost' in data and not isinstance(data['total_cost'], Decimal):
            data['total_cost'] = Decimal(str(data['total_cost']))

        return cls(**data)


class InventoryController:
    """Controller for FIFO inventory tracking."""

    def __init__(self, db_manager: DatabaseManager, accounting_controller=None):
        self.db = db_manager
        self.accounting = accounting_controller
        self._ensure_tables_exist()

    def _ensure_tables_exist(self):
        """Ensure the required tables exist."""
        # Check if inventory_batches table exists
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_batches'",
            fetch_one=True
        )

        if not result:
            print("Creating inventory tracking tables...")
            with open('add_fifo_tracking.sql', 'r') as f:
                sql = f.read()
                self.db.execute_script(sql)
            print("Inventory tracking tables created.")

    def process_buy_transaction(self, transaction: Transaction, strategy_intent: Optional[str] = None) -> int:
        """
        Process a BUY transaction by creating a new inventory batch.

        Args:
            transaction: The BUY transaction
            strategy_intent: Optional strategy intent for this purchase

        Returns:
            The batch_id of the created batch
        """
        if transaction.transaction_type != 'BUY':
            raise ValueError("Only BUY transactions can be processed with this method")

        # Create a new inventory batch
        batch = InventoryBatch(
            item_id=transaction.item_id,
            item_name=transaction.item_name,
            purchase_date=transaction.timestamp,
            quantity_original=transaction.quantity,
            quantity_remaining=transaction.quantity,
            unit_cost=transaction.unit_price,
            total_cost=transaction.total_price,
            location_id=transaction.location_id,
            character_id=transaction.character_id,
            order_id=transaction.order_id,
            strategy_intent=strategy_intent,
            transaction_id=transaction.tx_id
        )

        # Insert into database
        batch_dict = batch.to_dict()
        columns = ', '.join(k for k in batch_dict.keys() if k != 'batch_id' or batch_dict[k] is not None)
        placeholders = ', '.join(f':{k}' for k in batch_dict.keys() if k != 'batch_id' or batch_dict[k] is not None)

        query = f"INSERT INTO inventory_batches ({columns}) VALUES ({placeholders})"
        batch_id = self.db.execute_update(query, {k: v for k, v in batch_dict.items() if k != 'batch_id' or v is not None})

        print(f"Created inventory batch {batch_id} for transaction {transaction.tx_id}")
        return batch_id

    def process_sell_transaction(self, transaction: Transaction) -> Tuple[Decimal, List[Dict]]:
        """
        Process a SELL transaction using FIFO inventory tracking.

        Args:
            transaction: The SELL transaction

        Returns:
            Tuple of (total_profit, list of batch links)
        """
        if transaction.transaction_type != 'SELL':
            raise ValueError("Only SELL transactions can be processed with this method")

        # Start a database transaction to ensure atomicity
        with self.db.get_connection() as conn:
            cursor = conn.cursor()

            try:
                # Get available batches for this item at this location, ordered by purchase date (FIFO)
                cursor.execute("""
                    SELECT * FROM inventory_batches
                    WHERE item_id = ? AND location_id = ? AND character_id = ? AND quantity_remaining > 0
                    ORDER BY purchase_date ASC
                """, (transaction.item_id, transaction.location_id, transaction.character_id))

                batch_rows = cursor.fetchall()
                if not batch_rows:
                    print(f"Warning: No inventory batches found for item {transaction.item_id} at location {transaction.location_id}")
                    return Decimal('0'), []

                # Convert rows to InventoryBatch objects
                batches = []
                for row in batch_rows:
                    batch_dict = {key: row[key] for key in row.keys()}
                    batches.append(InventoryBatch.from_dict(batch_dict))

                remaining_to_sell = transaction.quantity
                total_cost_basis = Decimal('0')
                batch_links = []

                # Calculate gross revenue (before fees)
                gross_revenue = transaction.unit_price * transaction.quantity

                # Get or calculate broker fee and sales tax
                from controllers.accounting_controller import AccountingController

                # Get broker fee and sales tax from transaction if available
                broker_fee = transaction.broker_fee or Decimal('0')
                sales_tax = transaction.sales_tax or Decimal('0')

                # If fees are not set, try to calculate them
                if (broker_fee == Decimal('0') or sales_tax == Decimal('0')) and hasattr(self, 'accounting'):
                    # If we have an accounting controller instance, use it
                    if broker_fee == Decimal('0'):
                        broker_fee = self.accounting.calculate_broker_fee(gross_revenue, transaction.character_id)
                        # Update the transaction with the calculated broker fee
                        cursor.execute(
                            "UPDATE transactions SET broker_fee = ? WHERE tx_id = ?",
                            (str(broker_fee), transaction.tx_id)
                        )
                        transaction.broker_fee = broker_fee

                    if sales_tax == Decimal('0'):
                        sales_tax = self.accounting.calculate_sales_tax(gross_revenue, transaction.character_id)
                        # Update the transaction with the calculated sales tax
                        cursor.execute(
                            "UPDATE transactions SET sales_tax = ? WHERE tx_id = ?",
                            (str(sales_tax), transaction.tx_id)
                        )
                        transaction.sales_tax = sales_tax

                # Calculate net revenue (after fees)
                net_revenue = gross_revenue - broker_fee - sales_tax

                # If net_amount doesn't match our calculation (within a small tolerance), update it
                if abs(transaction.net_amount - net_revenue) > Decimal('0.1'):
                    print(f"Warning: Net amount from transaction ({transaction.net_amount}) doesn't match calculated value ({net_revenue}). Using calculated value.")
                    cursor.execute(
                        "UPDATE transactions SET net_amount = ? WHERE tx_id = ?",
                        (str(net_revenue), transaction.tx_id)
                    )
                    transaction.net_amount = net_revenue
                else:
                    # Use the transaction's net_amount
                    net_revenue = transaction.net_amount

                # Calculate sell price per unit after fees
                sell_price_per_unit = net_revenue / transaction.quantity

                # Process batches in FIFO order until we've accounted for all sold quantity
                for batch in batches:
                    if remaining_to_sell <= 0:
                        break

                    # Determine how many to take from this batch
                    quantity_from_batch = min(batch.quantity_remaining, remaining_to_sell)
                    remaining_to_sell -= quantity_from_batch

                    # Calculate cost basis for this portion
                    cost_basis = batch.unit_cost * quantity_from_batch
                    total_cost_basis += cost_basis

                    # Calculate profit for this portion
                    revenue = sell_price_per_unit * quantity_from_batch
                    profit = revenue - cost_basis

                    # Create batch link record
                    batch_link = {
                        'transaction_id': transaction.tx_id,
                        'batch_id': batch.batch_id,
                        'quantity': quantity_from_batch,
                        'unit_cost': batch.unit_cost,
                        'profit_contribution': profit
                    }
                    batch_links.append(batch_link)

                    # Update batch quantity remaining in the database
                    cursor.execute(
                        "UPDATE inventory_batches SET quantity_remaining = quantity_remaining - ? WHERE batch_id = ?",
                        (quantity_from_batch, batch.batch_id)
                    )

                    # Store the batch link
                    columns = ', '.join(batch_link.keys())
                    placeholders = ', '.join('?' for _ in batch_link.keys())
                    cursor.execute(
                        f"INSERT INTO transaction_batch_links ({columns}) VALUES ({placeholders})",
                        tuple(batch_link.values())
                    )

                    # If this batch is now empty, we could mark it as fully consumed
                    if batch.quantity_remaining - quantity_from_batch <= 0:
                        print(f"Batch {batch.batch_id} fully consumed")

                # Calculate total profit
                # Net revenue already accounts for broker_fee and sales_tax
                total_profit = net_revenue - total_cost_basis

                # Log the profit calculation details
                print(f"Profit Calc TX {transaction.tx_id}: GrossRev={gross_revenue}, BrokerFee={broker_fee}, SalesTax={sales_tax}, NetRev={net_revenue}, COGS={total_cost_basis}, Profit={total_profit}")

                # Update the transaction with the calculated profit
                cursor.execute(
                    "UPDATE transactions SET profit = ? WHERE tx_id = ?",
                    (str(total_profit), transaction.tx_id)
                )

                # If we couldn't account for all quantity, log a warning
                if remaining_to_sell > 0:
                    print(f"Warning: Could not account for {remaining_to_sell} units in transaction {transaction.tx_id}")

                # Commit the transaction
                conn.commit()

                return total_profit, batch_links

            except Exception as e:
                # Rollback in case of error
                conn.rollback()
                print(f"Error processing sell transaction: {e}")
                import traceback
                print(traceback.format_exc())
                raise

    def get_available_batches(self, item_id: int, location_id: int, character_id: int) -> List[InventoryBatch]:
        """
        Get available inventory batches for an item at a location, ordered by purchase date (FIFO).

        Args:
            item_id: The item ID
            location_id: The location ID
            character_id: The character ID

        Returns:
            List of available inventory batches
        """
        rows = self.db.execute_query(
            """
            SELECT * FROM inventory_batches
            WHERE item_id = ? AND location_id = ? AND character_id = ? AND quantity_remaining > 0
            ORDER BY purchase_date ASC
            """,
            (item_id, location_id, character_id),
            fetch_all=True
        )

        return [InventoryBatch.from_dict(dict(row)) for row in rows]

    def get_batch_by_id(self, batch_id: int) -> Optional[InventoryBatch]:
        """Get an inventory batch by ID."""
        row = self.db.execute_query(
            "SELECT * FROM inventory_batches WHERE batch_id = ?",
            (batch_id,),
            fetch_one=True
        )

        if not row:
            return None

        return InventoryBatch.from_dict(dict(row))

    def get_transaction_batches(self, transaction_id: int) -> List[Dict]:
        """
        Get all batch links for a transaction.

        Args:
            transaction_id: The transaction ID

        Returns:
            List of batch link records with batch details
        """
        rows = self.db.execute_query(
            """
            SELECT l.*, b.item_name, b.purchase_date, b.strategy_intent, b.order_id, b.trade_execution_id
            FROM transaction_batch_links l
            JOIN inventory_batches b ON l.batch_id = b.batch_id
            WHERE l.transaction_id = ?
            """,
            (transaction_id,),
            fetch_all=True
        )

        return [dict(row) for row in rows]

    def determine_strategy_for_transaction(self, transaction_id: int) -> Optional[str]:
        """
        Determine the strategy for a SELL transaction based on its batch links.
        Uses the strategy_intent from the consumed batches.

        Args:
            transaction_id: The transaction ID

        Returns:
            The determined strategy, or None if it can't be determined
        """
        batch_links = self.get_transaction_batches(transaction_id)

        if not batch_links:
            return None

        # Strategy determination logic:
        # 1. If all batches have the same strategy_intent, use that
        # 2. If mixed, use the strategy_intent of the batch that contributed the most quantity
        # 3. If no strategy_intent is set, return None

        strategies = {}
        total_quantity = 0

        for link in batch_links:
            strategy = link.get('strategy_intent')
            quantity = link.get('quantity', 0)
            total_quantity += quantity

            if strategy:
                strategies[strategy] = strategies.get(strategy, 0) + quantity

        if not strategies:
            return None

        # Find the strategy with the highest quantity
        dominant_strategy = max(strategies.items(), key=lambda x: x[1])[0]

        # If the dominant strategy accounts for at least 75% of the quantity, use it
        if strategies[dominant_strategy] / total_quantity >= 0.75:
            return dominant_strategy

        # Otherwise, it's a mixed strategy
        return "Mixed Strategy"

    def migrate_historical_transactions(self):
        """
        Migrate historical transactions to the FIFO inventory system.
        This should be run once when implementing the FIFO system.
        """
        # Check if migration has already been completed
        migration_status = self.db.execute_query(
            "SELECT completed FROM migration_status WHERE migration_name = 'fifo_inventory_tracking'",
            fetch_one=True
        )

        if migration_status and migration_status['completed']:
            print("FIFO migration has already been completed.")
            return

        print("Starting migration of historical transactions to FIFO inventory system...")

        # Process all BUY transactions first
        buy_transactions = self.db.execute_query(
            "SELECT * FROM transactions WHERE transaction_type = 'BUY' ORDER BY timestamp ASC",
            fetch_all=True
        )

        print(f"Processing {len(buy_transactions)} historical BUY transactions...")
        for row in buy_transactions:
            tx = Transaction.from_dict(dict(row))
            self.process_buy_transaction(tx)

        # Then process all SELL transactions
        sell_transactions = self.db.execute_query(
            "SELECT * FROM transactions WHERE transaction_type = 'SELL' ORDER BY timestamp ASC",
            fetch_all=True
        )

        print(f"Processing {len(sell_transactions)} historical SELL transactions...")
        for row in sell_transactions:
            tx = Transaction.from_dict(dict(row))
            self.process_sell_transaction(tx)

        # Mark migration as completed
        self.db.execute_update(
            "UPDATE migration_status SET completed = 1, completed_date = ? WHERE migration_name = 'fifo_inventory_tracking'",
            (datetime.now().isoformat(),)
        )

        print("Migration of historical transactions to FIFO inventory system completed.")
