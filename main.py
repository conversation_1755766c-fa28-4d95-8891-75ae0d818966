import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox

# --- Ensure correct import paths ---
# Add the project root to the Python path to allow absolute imports
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

# --- Import Core Components ---
try:
    from views.main_window import MainWindow
    from models.database import DatabaseManager
    from api.auth import ESIAuthenticator
    from api.esi_client import ESIClient
    from controllers.accounting_controller import AccountingController
    from controllers.strategy_controller import StrategyController
    from controllers.performance_controller import PerformanceController
    from controllers.data_sync_controller import DataSyncController
    from controllers.trade_execution_controller import TradeExecutionController
    from utils.database_utils import ensure_api_cache_table_exists
    from utils.tinygamer_bridge import is_application_running
except ImportError as e:
    print(f"Error importing application components: {e}")
    print("Please ensure all component files exist and the project structure is correct.")
    print(f"PROJECT_ROOT: {PROJECT_ROOT}")
    print(f"sys.path: {sys.path}")
    sys.exit(1) # Exit if core components can't be imported

def main():
    """ Main application entry point. """
    print("Starting TinyTrader Application...")

    # Create a basic QApplication instance for showing message boxes
    # before the full application initialization
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)

    # Check if another instance is already running
    if is_application_running("TinyTrader"):
        print("Another instance of TinyTrader is already running.")
        QMessageBox.critical(
            None,
            "TinyTrader Already Running",
            "Already running -- close existing TinyTrader before launching"
        )
        sys.exit(1)

    print("No other instances found, continuing startup...")

    # --- Configuration & Dependency Injection ---
    # In a larger app, consider a config file or environment variables
    # For now, use defaults and check for placeholder values.

    # 1. Database Manager
    # Ensure the database exists, run setup if not?
    db_manager = DatabaseManager() # Assumes eve_trader.db is in the root
    if not os.path.exists(db_manager.db_path):
         print(f"Database not found at {db_manager.db_path}.")
         print("Please run setup_database.py in the project root directory first.")
         # Optionally, try to run setup_database.py automatically?
         # try:
         #      import subprocess
         #      setup_script_path = os.path.join(PROJECT_ROOT, 'setup_database.py')
         #      print(f"Attempting to run {setup_script_path}...")
         #      subprocess.run([sys.executable, setup_script_path], check=True, cwd=PROJECT_ROOT)
         #      print("Database setup script completed.")
         #      db_manager = DatabaseManager() # Re-initialize
         # except Exception as setup_e:
         #      print(f"Failed to automatically run setup_database.py: {setup_e}")
         #      sys.exit(1)
         # For now, require manual setup:
         sys.exit(1)

    # Ensure the api_cache table exists
    print("Checking if api_cache table exists...")
    if not ensure_api_cache_table_exists(db_manager.db_path):
        print("Failed to create api_cache table. Some features may not work correctly.")


    # 2. ESI Authentication & Client
    # These require user configuration (Client ID/Secret) in auth.py/esi_client.py
    authenticator = ESIAuthenticator() # Checks for placeholder config internally
    # TODO: Need a way to select character ID if multiple are authenticated
    # For now, assume only one character or handle selection later.
    # Let's try to get the first authenticated character ID from the DB.
    first_char_row = db_manager.execute_query("SELECT character_id FROM api_tokens LIMIT 1", fetch_one=True)
    initial_character_id = first_char_row['character_id'] if first_char_row else None
    if initial_character_id:
         print(f"Found initial character ID: {initial_character_id}")
    else:
         print("No authenticated character found in database. Authentication will be required.")

    esi_client = ESIClient(character_id=initial_character_id, authenticator=authenticator) # Checks user agent config

    # 3. Controllers
    accounting_controller = AccountingController(db_manager, esi_client)
    strategy_controller = StrategyController(db_manager, accounting_controller)
    performance_controller = PerformanceController(db_manager)
    data_sync_controller = DataSyncController(
        db_manager=db_manager,
        esi_client=esi_client,
        accounting_controller=accounting_controller,
        strategy_controller=strategy_controller
    )

    # --- Setup PyQt Application ---
    # Use the existing QApplication instance created earlier
    app = QApplication.instance()

    # --- Create and Show Main Window ---
    # Pass necessary controllers/managers to the MainWindow via dependency injection
    window = MainWindow(
        authenticator=authenticator,
        esi_client=esi_client,
        data_sync_controller=data_sync_controller
    )

    window.show()
    print("Main window displayed.")

    # --- Start Event Loop ---
    print("Starting application event loop...")
    exit_code = app.exec_()
    print(f"Application finished with exit code: {exit_code}")
    sys.exit(exit_code)

if __name__ == '__main__':
    main()
