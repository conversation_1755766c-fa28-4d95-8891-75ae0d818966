#!/usr/bin/env python3
"""
Utility script to view transaction batch links for a specific transaction.
This helps visualize how FIFO inventory tracking is working.
"""

import os
import sys
import sqlite3
from datetime import datetime
from decimal import Decimal

# Add the parent directory to the path so we can import the eve_trader package
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from eve_trader.models.database import DatabaseManager
from eve_trader.models.transaction import Transaction
from eve_trader.controllers.inventory_controller import InventoryController

def format_decimal(value):
    """Format a decimal value with commas for thousands."""
    if isinstance(value, str):
        value = Decimal(value)
    return f"{value:,.2f}"

def main():
    print("=== Transaction Batch Link Viewer ===")
    
    # Initialize database manager
    db_path = os.path.join(os.path.dirname(__file__), 'eve_trader.db')
    if not os.path.exists(db_path):
        print(f"Error: Database file not found at {db_path}")
        return False
        
    print(f"Using database at {db_path}")
    db_manager = DatabaseManager(db_path)
    
    # Initialize inventory controller
    inventory = InventoryController(db_manager)
    
    # Check if FIFO tables exist
    result = db_manager.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_batches'",
        fetch_one=True
    )
    
    if not result:
        print("Error: FIFO inventory tracking tables do not exist.")
        print("Please run migrate_to_fifo.py first.")
        return False
    
    # Get transaction ID from user
    while True:
        try:
            tx_id = input("Enter transaction ID to view (or 'list' to see recent SELL transactions): ")
            
            if tx_id.lower() == 'list':
                # Show recent SELL transactions
                rows = db_manager.execute_query(
                    """
                    SELECT tx_id, timestamp, item_name, quantity, unit_price, profit, strategy
                    FROM transactions
                    WHERE transaction_type = 'SELL'
                    ORDER BY timestamp DESC
                    LIMIT 10
                    """,
                    fetch_all=True
                )
                
                if not rows:
                    print("No SELL transactions found.")
                    continue
                    
                print("\nRecent SELL Transactions:")
                print("-" * 80)
                print(f"{'ID':<10} {'Date':<20} {'Item':<20} {'Qty':<8} {'Price':<12} {'Profit':<12} {'Strategy':<15}")
                print("-" * 80)
                
                for row in rows:
                    date = datetime.fromisoformat(row['timestamp'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
                    profit = format_decimal(row['profit']) if row['profit'] else 'N/A'
                    price = format_decimal(row['unit_price'])
                    print(f"{row['tx_id']:<10} {date:<20} {row['item_name'][:20]:<20} {row['quantity']:<8} {price:<12} {profit:<12} {row['strategy'] or 'None':<15}")
                
                print("-" * 80)
                continue
                
            tx_id = int(tx_id)
            break
        except ValueError:
            print("Please enter a valid transaction ID or 'list'.")
    
    # Get transaction details
    tx_row = db_manager.execute_query(
        "SELECT * FROM transactions WHERE tx_id = ?",
        (tx_id,),
        fetch_one=True
    )
    
    if not tx_row:
        print(f"Error: Transaction {tx_id} not found.")
        return False
        
    transaction = Transaction.from_dict(dict(tx_row))
    
    if transaction.transaction_type != 'SELL':
        print(f"Error: Transaction {tx_id} is not a SELL transaction.")
        print("Only SELL transactions have batch links.")
        return False
    
    # Get batch links
    batch_links = inventory.get_transaction_batches(tx_id)
    
    if not batch_links:
        print(f"No batch links found for transaction {tx_id}.")
        print("This transaction may not have been processed with FIFO inventory tracking.")
        return False
    
    # Display transaction details
    print("\nTransaction Details:")
    print("-" * 80)
    date = transaction.timestamp.strftime('%Y-%m-%d %H:%M:%S') if hasattr(transaction.timestamp, 'strftime') else transaction.timestamp
    print(f"ID:             {transaction.tx_id}")
    print(f"Date:           {date}")
    print(f"Item:           {transaction.item_name} (ID: {transaction.item_id})")
    print(f"Quantity:       {transaction.quantity}")
    print(f"Unit Price:     {format_decimal(transaction.unit_price)} ISK")
    print(f"Total Price:    {format_decimal(transaction.total_price)} ISK")
    print(f"Net Amount:     {format_decimal(transaction.net_amount)} ISK")
    print(f"Profit:         {format_decimal(transaction.profit) if transaction.profit else 'N/A'} ISK")
    print(f"Strategy:       {transaction.strategy or 'None'}")
    print("-" * 80)
    
    # Display batch links
    print("\nInventory Batches Consumed:")
    print("-" * 100)
    print(f"{'Batch ID':<10} {'Purchase Date':<20} {'Quantity':<10} {'Unit Cost':<15} {'Profit':<15} {'Strategy Intent':<20}")
    print("-" * 100)
    
    total_quantity = 0
    total_profit = Decimal('0')
    
    for link in batch_links:
        date = datetime.fromisoformat(link['purchase_date'].replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
        unit_cost = format_decimal(link['unit_cost'])
        profit = format_decimal(link['profit_contribution'])
        strategy = link['strategy_intent'] or 'None'
        
        print(f"{link['batch_id']:<10} {date:<20} {link['quantity']:<10} {unit_cost:<15} {profit:<15} {strategy:<20}")
        
        total_quantity += link['quantity']
        total_profit += Decimal(str(link['profit_contribution']))
    
    print("-" * 100)
    print(f"{'Total:':<10} {'':<20} {total_quantity:<10} {'':<15} {format_decimal(total_profit):<15}")
    print("-" * 100)
    
    # Calculate hold duration for each batch
    print("\nHold Duration Analysis:")
    print("-" * 80)
    
    for link in batch_links:
        purchase_date = datetime.fromisoformat(link['purchase_date'].replace('Z', '+00:00'))
        sell_date = transaction.timestamp if hasattr(transaction.timestamp, 'replace') else datetime.fromisoformat(transaction.timestamp.replace('Z', '+00:00'))
        
        hold_duration = sell_date - purchase_date
        hold_days = hold_duration.days
        hold_hours = hold_duration.total_seconds() / 3600
        
        print(f"Batch {link['batch_id']}: Held for {hold_days} days ({hold_hours:.1f} hours)")
    
    print("-" * 80)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\nFailed to view transaction batch links.")
    
    input("\nPress Enter to exit...")
