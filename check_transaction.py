#!/usr/bin/env python3
"""
Script to check a specific transaction and its batch links.
"""

import os
import sys
import sqlite3
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Tuple, Set

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from models.database import DatabaseManager

def check_transaction_batches(db_manager: DatabaseManager, batch_ids: List[int]):
    """
    Check the specified batch IDs and their transaction links.

    Args:
        db_manager: Database manager
        batch_ids: List of batch IDs to check
    """
    print(f"Checking batches: {batch_ids}")

    # Get batch details
    batch_placeholders = ', '.join('?' for _ in batch_ids)
    batch_query = f"""
    SELECT * FROM inventory_batches
    WHERE batch_id IN ({batch_placeholders})
    """

    batches = db_manager.execute_query(batch_query, batch_ids, fetch_all=True)

    if not batches:
        print("No batches found with the specified IDs.")
        return

    print("\n=== Batch Details ===")
    for batch in batches:
        print(f"Batch #{batch['batch_id']}:")
        print(f"  Item: {batch['item_name']} (ID: {batch['item_id']})")
        print(f"  Purchase Date: {batch['purchase_date']}")
        print(f"  Quantity: {batch['quantity_original']} (Remaining: {batch['quantity_remaining']})")
        print(f"  Unit Cost: {batch['unit_cost']}")
        print(f"  Transaction ID: {batch['transaction_id']}")
        print()

    # Get all transaction links for these batches
    links_query = f"""
    SELECT l.*, t.transaction_type, t.quantity as tx_quantity, t.item_name, t.timestamp, t.tx_id
    FROM transaction_batch_links l
    JOIN transactions t ON l.transaction_id = t.tx_id
    WHERE l.batch_id IN ({batch_placeholders})
    ORDER BY t.timestamp ASC
    """

    links = db_manager.execute_query(links_query, batch_ids, fetch_all=True)

    if not links:
        print("No transaction links found for these batches.")
        return

    # Group links by transaction
    tx_links = {}
    for link in links:
        tx_id = link['transaction_id']
        if tx_id not in tx_links:
            tx_links[tx_id] = []
        tx_links[tx_id].append(link)

    print("\n=== Transaction Links ===")
    for tx_id, links in tx_links.items():
        print(f"Transaction #{tx_id}:")
        print(f"  Type: {links[0]['transaction_type']}")
        print(f"  Item: {links[0]['item_name']}")
        print(f"  Date: {links[0]['timestamp']}")
        print(f"  Total Quantity: {links[0]['tx_quantity']}")
        print("  Batch Links:")

        total_quantity = 0
        for link in links:
            print(f"    Batch #{link['batch_id']}: {link['quantity']} units")
            total_quantity += link['quantity']

        # Get all batch links for this transaction
        all_links_query = """
        SELECT l.*, b.item_name, b.purchase_date
        FROM transaction_batch_links l
        JOIN inventory_batches b ON l.batch_id = b.batch_id
        WHERE l.transaction_id = ?
        ORDER BY b.purchase_date ASC
        """

        all_links = db_manager.execute_query(all_links_query, (tx_id,), fetch_all=True)

        # Calculate total quantity from all batch links
        all_total_quantity = sum(link['quantity'] for link in all_links)

        print(f"  Total Quantity from Visible Batches: {total_quantity}")
        print(f"  Total Quantity from All Batches: {all_total_quantity}")

        if all_total_quantity != links[0]['tx_quantity']:
            print(f"  WARNING: Quantity mismatch! Transaction: {links[0]['tx_quantity']}, All Batches: {all_total_quantity}")
        elif total_quantity != all_total_quantity:
            print(f"  NOTE: Some batches not shown. Transaction uses {len(all_links)} batches, but only {len(links)} are visible.")
            print(f"  Other batches used:")

            # Show other batches
            other_batch_ids = set(link['batch_id'] for link in all_links) - set(link['batch_id'] for link in links)
            for link in all_links:
                if link['batch_id'] in other_batch_ids:
                    print(f"    Batch #{link['batch_id']}: {link['quantity']} units")

        print()

def main():
    # Initialize database manager
    db_path = os.path.join(os.path.dirname(__file__), 'eve_trader.db')
    if not os.path.exists(db_path):
        print(f"Error: Database file not found at {db_path}")
        return False

    print(f"Using database at {db_path}")
    db_manager = DatabaseManager(db_path)

    # Get batch IDs from command line arguments
    if len(sys.argv) < 2:
        print("Usage: python check_transaction.py BATCH_ID1 [BATCH_ID2 ...]")
        return False

    try:
        batch_ids = [int(arg) for arg in sys.argv[1:]]
    except ValueError:
        print("Error: Batch IDs must be integers")
        return False

    # Check the transaction and its batch links
    check_transaction_batches(db_manager, batch_ids)

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
