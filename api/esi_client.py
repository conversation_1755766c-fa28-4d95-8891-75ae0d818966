import requests
import time
import json
import sqlite3
from datetime import datetime, timedelta, timezone
from urllib.parse import urlencode
from .auth import ESIAuthenticator # Import the authenticator

# --- Configuration ---
import os

ESI_BASE_URL = 'https://esi.evetech.net'
DEFAULT_USER_AGENT = 'TinyTrader EVE Trading Bot (Contact: YOUR_CONTACT_INFO)' # Replace with your contact info

# Use absolute path for database to avoid relative path issues
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATABASE_NAME = os.path.join(BASE_DIR, 'eve_trader.db')

CACHE_DURATION_SECONDS = 300 # Default cache duration (5 minutes) for market data etc.
RATE_LIMIT_DELAY = 0.5 # Simple delay in seconds between requests to avoid hitting limits

# Jita constants from design doc
JITA_REGION_ID = 10000002
JITA_STATION_ID = 60003760

class ESIClient:
    def __init__(self, character_id=None, authenticator=None, db_path=DATABASE_NAME, user_agent=DEFAULT_USER_AGENT):
        self.character_id = character_id
        self.authenticator = authenticator if authenticator else ESIAuthenticator(db_path=db_path)
        self.db_path = db_path
        self.user_agent = user_agent
        self.last_request_time = 0
        self._check_config()
        self._ensure_api_cache_table()

    def _check_config(self):
        """ Basic check if placeholder config values are still present. """
        if 'YOUR_CONTACT_INFO' in self.user_agent:
            print("Warning: User Agent contact info might not be configured in eve_trader/api/esi_client.py")
        # Authenticator checks its own config

    def _ensure_api_cache_table(self):
        """Ensures the api_cache table exists in the database."""
        conn = self._get_db_connection()
        if not conn: return False

        try:
            cursor = conn.cursor()
            # Check if the api_cache table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_cache'")
            if cursor.fetchone() is None:
                print("api_cache table does not exist. Creating it...")

                # Create api_cache table
                sql_create_api_cache_table = """
                CREATE TABLE api_cache (
                    cache_key TEXT PRIMARY KEY,
                    endpoint TEXT NOT NULL,
                    parameters TEXT NOT NULL,
                    response_data BLOB NOT NULL,
                    cached_at DATETIME NOT NULL,
                    expires_at DATETIME NOT NULL,
                    etag TEXT
                );
                """
                cursor.execute(sql_create_api_cache_table)

                # Create index
                cursor.execute("CREATE INDEX idx_api_cache_expires_at ON api_cache (expires_at)")

                conn.commit()
                print("api_cache table created successfully.")
                return True
            else:
                return True
        except sqlite3.Error as e:
            print(f"Database error checking/creating api_cache table: {e}")
            return False
        finally:
            if conn: conn.close()

    def _get_db_connection(self):
        """ Returns a database connection. """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            return conn
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return None

    def _get_cached_response(self, endpoint, params):
        """ Checks the cache for a valid response. """
        conn = self._get_db_connection()
        if not conn: return None, None, None

        cache_key = self._generate_cache_key(endpoint, params)
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT response_data, etag, expires_at FROM api_cache WHERE cache_key = ?", (cache_key,))
            row = cursor.fetchone()
            if row:
                expires_at = datetime.fromisoformat(row['expires_at'])
                response_data = json.loads(row['response_data'])
                if expires_at > datetime.now(timezone.utc):
                    print(f"Cache HIT for {endpoint} with params {params}")
                    return response_data, row['etag'], None # Return data, etag, and None for stale_data
                else:
                    print(f"Cache STALE for {endpoint} with params {params}")
                    # Return etag and stale data, ESI might return 304 Not Modified
                    return None, row['etag'], response_data
            else:
                print(f"Cache MISS for {endpoint} with params {params}")
                return None, None, None # No cache entry
        except sqlite3.Error as e:
            print(f"Database error reading cache: {e}")
            return None, None, None
        except json.JSONDecodeError as e:
            print(f"Error decoding cached JSON: {e}")
            # Consider deleting the invalid cache entry here
            return None, None, None # Treat as miss
        finally:
            if conn: conn.close()

    def _save_to_cache(self, endpoint, params, response_data, expires_at, etag=None):
        """ Saves a response to the cache. """
        conn = self._get_db_connection()
        if not conn: return False

        cache_key = self._generate_cache_key(endpoint, params)
        now_iso = datetime.now(timezone.utc).isoformat()
        expires_at_iso = expires_at.isoformat()
        params_str = json.dumps(params) if params else "{}"
        response_data_blob = json.dumps(response_data).encode('utf-8') # Store as JSON blob

        try:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO api_cache (cache_key, endpoint, parameters, response_data, cached_at, expires_at, etag)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ON CONFLICT(cache_key) DO UPDATE SET
                    endpoint = excluded.endpoint,
                    parameters = excluded.parameters,
                    response_data = excluded.response_data,
                    cached_at = excluded.cached_at,
                    expires_at = excluded.expires_at,
                    etag = excluded.etag;
            """, (cache_key, endpoint, params_str, response_data_blob, now_iso, expires_at_iso, etag))
            conn.commit()
            print(f"Cached response for {endpoint} with params {params}")
            return True
        except sqlite3.Error as e:
            print(f"Database error saving cache: {e}")
            conn.rollback()
            return False
        finally:
            if conn: conn.close()

    def _generate_cache_key(self, endpoint, params):
        """ Generates a unique key for caching based on endpoint and params. """
        # Simple key generation, might need refinement for complex params
        param_str = ""
        if params:
            # Sort params to ensure consistent key order
            param_str = urlencode(sorted(params.items()))
        return f"{endpoint}?{param_str}"

    def _rate_limit(self):
        """ Enforces a simple delay between requests. """
        now = time.time()
        elapsed = now - self.last_request_time
        if elapsed < RATE_LIMIT_DELAY:
            time.sleep(RATE_LIMIT_DELAY - elapsed)
        self.last_request_time = time.time() # Update last request time *after* potential sleep

    def _make_request(self, method, endpoint, params=None, data=None, requires_auth=False, cache_duration=CACHE_DURATION_SECONDS, force_refresh: bool = True):
        """ Makes a request to the ESI API, handling auth, cache, rate limits, and pagination.
            Note: force_refresh is now True by default to always fetch fresh data from ESI.
        """
        self._rate_limit()
        full_url = f"{ESI_BASE_URL}{endpoint}"
        headers = {'User-Agent': self.user_agent, 'Accept': 'application/json'}
        access_token = None

        if requires_auth:
            if not self.character_id:
                print("Error: Character ID required for authenticated endpoint.")
                print(f"Endpoint: {endpoint}, Method: {method}")
                return None

            # Check if token exists in database
            token_data = self.authenticator.get_token_data(self.character_id)
            if not token_data:
                print(f"Error: No token data found for character {self.character_id}.")
                print("Please authenticate the character first.")
                return None

            access_token = self.authenticator.get_valid_access_token(self.character_id)
            if not access_token:
                print(f"Error: Could not get valid access token for character {self.character_id}.")
                print("Token might be expired and refresh failed.")
                print(f"Token data: {token_data}")
                return None

            headers['Authorization'] = f'Bearer {access_token}'

        # --- Caching Logic ---
        cached_data = None
        etag = None
        stale_data = None

        if not force_refresh:
            cached_data, etag, stale_data = self._get_cached_response(endpoint, params)
            if cached_data:
                return cached_data # Return data directly from cache
            if etag:
                headers['If-None-Match'] = etag # Use ETag for conditional request
        else:
            print(f"Cache SKIPPED (force_refresh=True) for {endpoint} with params {params}")


        print(f"Making {method} request to {full_url} with params {params}")
        all_results = []
        page = 1
        total_pages = 1

        while page <= total_pages:
            if total_pages > 1:
                print(f"Fetching page {page}/{total_pages}...")
                if params is None: params = {}
                params['page'] = page # Add/update page parameter

            try:
                response = requests.request(method, full_url, headers=headers, params=params, json=data, timeout=20) # Increased timeout

                # Handle 304 Not Modified (cache is still valid)
                if response.status_code == 304:
                    print(f"ESI returned 304 Not Modified for {endpoint}. Using cached ETag.")
                    # Use the stale data we already have
                    if stale_data:
                        # Update the cache expiry time
                        cache_expiry = datetime.now(timezone.utc) + timedelta(seconds=cache_duration)
                        self._save_to_cache(endpoint, params, stale_data, cache_expiry, etag)
                        return stale_data
                    else:
                        # If we somehow don't have stale data, try to fetch it again
                        print("Warning: Got 304 but stale_data is None. Trying to re-fetch from cache.")
                        _, _, refetched_stale_data = self._get_cached_response(endpoint, params)
                        if refetched_stale_data:
                            cache_expiry = datetime.now(timezone.utc) + timedelta(seconds=cache_duration)
                            self._save_to_cache(endpoint, params, refetched_stale_data, cache_expiry, etag)
                            return refetched_stale_data
                        else:
                            print("Error: Got 304 but couldn't find original cached data.")
                            return None # Fallback


                response.raise_for_status() # Raise HTTPError for other bad responses (4xx or 5xx)

                # --- Pagination Handling ---
                if 'X-Pages' in response.headers:
                    total_pages = int(response.headers['X-Pages'])
                    print(f"Total pages reported by ESI: {total_pages}")
                else:
                    total_pages = 1 # Assume single page if header is missing

                # --- Cache Saving ---
                new_etag = response.headers.get('ETag')
                expires_header = response.headers.get('Expires')
                if expires_header:
                    try:
                        # ESI 'Expires' format: "Mon, 16 Jan 2023 15:04:05 GMT"
                        cache_expiry = datetime.strptime(expires_header, '%a, %d %b %Y %H:%M:%S %Z').replace(tzinfo=timezone.utc)
                    except ValueError:
                        print(f"Warning: Could not parse Expires header '{expires_header}'. Using default duration.")
                        cache_expiry = datetime.now(timezone.utc) + timedelta(seconds=cache_duration)
                else:
                    cache_expiry = datetime.now(timezone.utc) + timedelta(seconds=cache_duration)

                # Only cache the *first* page response if paginated, or the full response if not.
                # Caching subsequent pages individually can be complex.
                # A common strategy is to cache the combined result after fetching all pages,
                # but that means no caching benefit *during* pagination.
                # Let's cache the first page response for now.
                if page == 1:
                    self._save_to_cache(endpoint, params, response.json(), cache_expiry, new_etag)

                # Accumulate results if paginated
                page_data = response.json()
                if isinstance(page_data, list):
                    all_results.extend(page_data)
                else:
                    # If not a list (e.g., single object response), just return it
                    return page_data

                page += 1
                if page <= total_pages:
                    self._rate_limit() # Apply rate limit between pages

            except requests.exceptions.HTTPError as e:
                print(f"HTTP Error: {e.response.status_code} for URL: {e.response.url}")
                print(f"Response: {e.response.text}")
                # Handle specific errors (e.g., 401/403 might mean token issues)
                if e.response.status_code in [401, 403] and requires_auth:
                    print("Authentication error. Token might be invalid or expired.")
                    # Consider attempting a token refresh here or signaling the UI
                # Handle 420 Error Limit Reached
                if e.response.status_code == 420:
                    print("Error limit reached. Waiting before retrying...")
                    time.sleep(61) # Wait just over a minute
                    # Consider implementing a more robust backoff strategy
                return None # Indicate failure
            except requests.exceptions.RequestException as e:
                print(f"Request Exception: {e}")
                return None # Indicate failure
            except json.JSONDecodeError as e:
                print(f"JSON Decode Error: {e}")
                print(f"Response Text: {response.text}")
                return None

        return all_results # Return combined list for paginated results

    # --- Specific ESI Endpoint Methods ---

    def get_wallet_transactions(self, from_id=None, page=None, force_refresh=True):
        """ /v1/characters/{character_id}/wallet/transactions/

        Args:
            from_id: Optional transaction ID to fetch from
            page: Page number (IGNORED - this endpoint doesn't support standard pagination)
            force_refresh: Whether to force refresh the data from ESI

        Note: This endpoint doesn't support standard pagination with 'page' parameter.
        It uses 'from_id' for pagination, but we're only fetching the first batch
        in our implementation to avoid excessive API calls.
        """
        if not self.character_id: return None
        endpoint = f"/v1/characters/{self.character_id}/wallet/transactions/"
        params = {}
        if from_id:
            params['from_id'] = from_id
        # The page parameter is completely ignored for this endpoint
        # Always fetch fresh data from ESI by using force_refresh=True by default
        return self._make_request('GET', endpoint, params=params, requires_auth=True, cache_duration=3600, force_refresh=force_refresh)

    def get_wallet_journal(self, page=1, force_refresh=True):
        """ /v6/characters/{character_id}/wallet/journal/ """
        if not self.character_id: return None
        endpoint = f"/v6/characters/{self.character_id}/wallet/journal/"
        params = {'page': page}
        # Always fetch fresh data from ESI by using force_refresh=True
        return self._make_request('GET', endpoint, params=params, requires_auth=True, cache_duration=3600, force_refresh=force_refresh)

    def get_character_orders(self, force_refresh: bool = True):
        """ /v2/characters/{character_id}/orders/ """
        if not self.character_id: return None
        endpoint = f"/v2/characters/{self.character_id}/orders/"
        # Always fetch fresh data from ESI by using force_refresh=True by default
        return self._make_request('GET', endpoint, requires_auth=True, cache_duration=120, force_refresh=force_refresh)

    def get_character_order_history(self, page=1, force_refresh=True):
        """ /v1/characters/{character_id}/orders/history/ """
        if not self.character_id: return None
        endpoint = f"/v1/characters/{self.character_id}/orders/history/"
        params = {'page': page}
        # Always fetch fresh data from ESI by using force_refresh=True by default
        return self._make_request('GET', endpoint, params=params, requires_auth=True, cache_duration=3600 * 6, force_refresh=force_refresh)

    def get_market_orders(self, region_id=JITA_REGION_ID, item_id=None, order_type='all', page=1, force_refresh: bool = True):
        """ /v1/markets/{region_id}/orders/ """
        endpoint = f"/v1/markets/{region_id}/orders/"
        params = {'order_type': order_type, 'page': page}
        if item_id:
            params['type_id'] = item_id
        # Always fetch fresh data from ESI by using force_refresh=True by default
        return self._make_request('GET', endpoint, params=params, requires_auth=False, cache_duration=60, force_refresh=force_refresh)

    def get_market_history(self, region_id: int, item_id: int, force_refresh: bool = True):
        """ /v1/markets/{region_id}/history/ """
        endpoint = f"/v1/markets/{region_id}/history/"
        params = {'type_id': item_id}
        # Always fetch fresh data from ESI by using force_refresh=True by default
        return self._make_request('GET', endpoint, params=params, requires_auth=False, cache_duration=3600 * 12, force_refresh=force_refresh)

    def get_market_prices(self, force_refresh=True):
        """ /v1/markets/prices/ """
        endpoint = "/v1/markets/prices/"
        # Always fetch fresh data from ESI by using force_refresh=True by default
        return self._make_request('GET', endpoint, requires_auth=False, cache_duration=600, force_refresh=force_refresh)

    def get_character_skills(self, force_refresh=True):
        """ /v4/characters/{character_id}/skills/ """
        if not self.character_id: return None
        endpoint = f"/v4/characters/{self.character_id}/skills/"
        # Always fetch fresh data from ESI by using force_refresh=True by default
        return self._make_request('GET', endpoint, requires_auth=True, cache_duration=3600 * 24, force_refresh=force_refresh)

    def get_wallet_balance(self, force_refresh=True):
        """ /v1/characters/{character_id}/wallet/

        Returns the character's wallet balance.
        """
        if not self.character_id: return None
        endpoint = f"/v1/characters/{self.character_id}/wallet/"
        # Always fetch fresh data from ESI by using force_refresh=True by default
        return self._make_request('GET', endpoint, requires_auth=True, cache_duration=300, force_refresh=force_refresh)

    # --- Helper to fetch all pages ---
    def get_all_pages(self, method_name, *args, **kwargs):
        """
        Fetches all pages for a paginated ESI endpoint method.
        Assumes the method takes a 'page' parameter.

        Special handling for wallet_transactions which doesn't support standard pagination.
        """
        all_results = []
        page = 1

        # Check if the method exists
        if not hasattr(self, method_name):
            print(f"Error: Method '{method_name}' does not exist in ESIClient.")
            return None

        # Check if character_id is set for authenticated methods
        if method_name in ['get_wallet_transactions', 'get_wallet_journal', 'get_character_orders',
                          'get_character_order_history', 'get_character_skills']:
            if not self.character_id:
                print(f"Error: Character ID required for method '{method_name}'.")
                return None

            # Check if token exists
            token_data = self.authenticator.get_token_data(self.character_id)
            if not token_data:
                print(f"Error: No token data found for character {self.character_id}.")
                print("Please authenticate the character first.")
                return None

        # Special case for wallet transactions which doesn't support standard pagination
        # It uses 'from_id' parameter instead of 'page'
        if method_name == 'get_wallet_transactions':
            print("Wallet transactions endpoint doesn't support standard pagination.")
            print("Fetching first batch of wallet transactions...")

            # Create a copy of kwargs to avoid modifying the original
            call_kwargs = kwargs.copy()

            # Handle force_refresh separately
            force_refresh = True  # Default to True for fresh data
            if 'force_refresh' in call_kwargs:
                force_refresh = call_kwargs.pop('force_refresh')

            method_to_call = getattr(self, method_name)

            try:
                # First call without from_id to get the most recent transactions
                results = method_to_call(force_refresh=force_refresh)

                if results is None or not isinstance(results, list) or not results:
                    print("No wallet transactions found or error occurred.")
                    return []

                print(f"Found {len(results)} wallet transactions.")
                return results  # Return only the first batch - no pagination for wallet transactions

            except Exception as e:
                print(f"Exception while calling {method_name}: {str(e)}")
                return []

        # Standard pagination for other endpoints
        while True:
            print(f"Fetching page {page} for {method_name}...")
            # Create a copy of kwargs to avoid modifying the original
            call_kwargs = kwargs.copy()
            call_kwargs['page'] = page

            # Handle force_refresh separately - don't pass it to methods that don't accept it
            force_refresh = True  # Default to True for fresh data
            if 'force_refresh' in call_kwargs:
                force_refresh = call_kwargs.pop('force_refresh')

            method_to_call = getattr(self, method_name)

            # Check if the method accepts force_refresh parameter
            import inspect
            method_params = inspect.signature(method_to_call).parameters
            if 'force_refresh' in method_params:
                call_kwargs['force_refresh'] = force_refresh

            try:
                results = method_to_call(*args, **call_kwargs)
            except Exception as e:
                print(f"Exception while calling {method_name}: {str(e)}")
                return all_results if all_results else None

            if results is None:
                print(f"Error fetching page {page} for {method_name}. Stopping.")
                # Return whatever was fetched so far, might be partial data
                return all_results if all_results else None

            if not isinstance(results, list) or not results:
                # If results are not a list or an empty list, we're done
                if page == 1 and results: # Handle single-item non-list results on first page
                     all_results.append(results)
                break

            all_results.extend(results)
            # Simple check: if fewer results than expected per page (e.g., 1000 for journal), assume last page
            # ESI doesn't always provide X-Pages, so this is a fallback.
            # A better check might be needed depending on the endpoint.
            if len(results) < 1000: # Common page size limit for many endpoints
                 break
            page += 1
            self._rate_limit() # Rate limit between pages

        print(f"Fetched a total of {len(all_results)} items across {page} page(s) for {method_name}.")
        return all_results


# Example Usage
if __name__ == '__main__':
    # Requires a character to have authenticated previously via auth.py example
    # or manually inserting token data into eve_trader.db
    TEST_CHARACTER_ID = 123456 # Replace with a valid character ID that has tokens in the DB

    print(f"\n--- Initializing ESI Client for Character ID: {TEST_CHARACTER_ID} ---")
    client = ESIClient(character_id=TEST_CHARACTER_ID)

    if client.authenticator.get_token_data(TEST_CHARACTER_ID):
        print("\n--- Getting Character Orders (Cached/Live) ---")
        orders = client.get_character_orders()
        if orders is not None:
            print(f"Found {len(orders)} active orders.")
            # print(orders)
        else:
            print("Failed to get character orders.")

        print("\n--- Getting Wallet Transactions (First Page) ---")
        transactions = client.get_wallet_transactions()
        if transactions is not None:
            print(f"Found {len(transactions)} transactions on first fetch.")
            # print(transactions)
        else:
            print("Failed to get wallet transactions.")

    # print("\n--- Getting All Wallet Journal Entries ---")
    # # Note: This can be many pages and take time!
    # journal_entries = client.get_all_pages('get_wallet_journal')
    # if journal_entries is not None:
    #     print(f"Found {len(journal_entries)} total journal entries.")
    # else:
    #     print("Failed to get all journal entries.")

        print("\n--- Getting Market History (Example: Tritanium in Jita) ---")
        TRITANIUM_ID = 34 # Defined below, but good to have it here for context
        history = client.get_market_history(region_id=JITA_REGION_ID, item_id=TRITANIUM_ID)
        if history is not None:
            print(f"Found {len(history)} days of market history for Tritanium in Jita.")
            # print(history[-5:]) # Print last 5 days
        else:
            print("Failed to get market history for Tritanium.")

    else:
        print(f"\n--- No token data found for Character ID: {TEST_CHARACTER_ID} ---")
        print("Please run auth.py example first or manually add token data.")

    print("\n--- Getting Market Prices (Public) ---")
    prices = client.get_market_prices()
    if prices is not None:
        print(f"Got {len(prices)} market price entries.")
        # print(prices[:5]) # Print first 5
    else:
        print("Failed to get market prices.")

    print("\n--- Getting Jita Market Orders (Example: Tritanium) ---")
    TRITANIUM_ID = 34
    jita_orders = client.get_market_orders(region_id=JITA_REGION_ID, item_id=TRITANIUM_ID, order_type='sell', page=1)
    if jita_orders is not None:
        print(f"Found {len(jita_orders)} Tritanium sell orders on page 1 in Jita.")
        # print(jita_orders[:5])
    else:
        print("Failed to get Jita market orders for Tritanium.")
