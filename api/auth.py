import base64
import requests
import time
import json
import sqlite3
import os # For environment variables
from datetime import datetime, timedelta, timezone
from urllib.parse import urlencode, urlparse, parse_qs
from dotenv import load_dotenv # To load .env file

# --- Load Environment Variables ---
# Construct the path to the .env file relative to this script's location
# auth.py is in api/, .env is in the root
DOTENV_PATH = os.path.join(os.path.dirname(__file__), '..', '.env')
print(f"Attempting to load .env file from: {DOTENV_PATH}")
if os.path.exists(DOTENV_PATH):
    load_dotenv(dotenv_path=DOTENV_PATH)
    print(".env file loaded successfully.")
else:
    print("Warning: .env file not found at expected location. Using default/hardcoded values.")

ESI_CLIENT_ID = 'cce3868760fc4a068f761fd6c0c129f7'
ESI_SECRET_KEY = 'Tgbgqg9Ixcz50UyVXa4vNNH6NniA5VPWMiNTtFAi'
# Make sure this matches the callback URL registered with your ESI application
ESI_CALLBACK_URL = 'http://localhost:8080/callback' # Example callback URL
ESI_AUTHORIZATION_URL = 'https://login.eveonline.com/v2/oauth/authorize/'
ESI_TOKEN_URL = 'https://login.eveonline.com/v2/oauth/token'
ESI_VERIFY_URL = 'https://login.eveonline.com/oauth/verify'

# Get the absolute path to the database
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DATABASE_NAME = os.path.join(BASE_DIR, 'eve_trader.db')
print(f"ESIAuthenticator using database at: {DATABASE_NAME}")

# Scopes required based on designdoc.txt
REQUIRED_SCOPES = [
    "esi-wallet.read_character_wallet.v1",
    "esi-markets.read_character_orders.v1",
    "esi-skills.read_skills.v1",
    # Add any other required scopes here
]

class ESIAuthenticator:
    def __init__(self, client_id=ESI_CLIENT_ID, secret_key=ESI_SECRET_KEY, callback_url=ESI_CALLBACK_URL, db_path=DATABASE_NAME):
        self.client_id = client_id
        self.secret_key = secret_key
        self.callback_url = callback_url
        self.db_path = db_path
        self._check_config()

    def _check_config(self):
        """ Basic check if placeholder config values are still present. """
        if 'YOUR_ESI_CLIENT_ID' in self.client_id or 'YOUR_ESI_SECRET_KEY' in self.secret_key:
            print("Warning: ESI Client ID or Secret Key might not be configured in api/auth.py")

    def _get_db_connection(self):
        """ Returns a database connection. """
        try:
            # Adjust path relative to the script location if needed
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            return conn
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return None

    def get_auth_url(self, scopes=None, state=""):
        """ Generates the ESI authentication URL. """
        if scopes is None:
            scopes = REQUIRED_SCOPES
        scope_string = " ".join(scopes)
        params = {
            'response_type': 'code',
            'redirect_uri': self.callback_url,
            'client_id': self.client_id,
            'scope': scope_string,
            'state': state # Optional state parameter for security
        }
        return f"{ESI_AUTHORIZATION_URL}?{urlencode(params)}"

    def process_callback(self, code):
        """ Processes the callback code to get access and refresh tokens. """
        auth_header = base64.b64encode(f"{self.client_id}:{self.secret_key}".encode()).decode()
        headers = {
            'Authorization': f'Basic {auth_header}',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'login.eveonline.com'
        }
        data = {
            'grant_type': 'authorization_code',
            'code': code
        }
        try:
            response = requests.post(ESI_TOKEN_URL, headers=headers, data=data, timeout=10)
            response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
            token_data = response.json()

            # Verify the token and get character info
            verify_header = {'Authorization': f'Bearer {token_data["access_token"]}'}
            verify_response = requests.get(ESI_VERIFY_URL, headers=verify_header, timeout=10)
            verify_response.raise_for_status()
            character_info = verify_response.json()

            # Calculate expiry time
            expires_in = token_data.get('expires_in', 1200) # Default 20 minutes
            expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)

            # Store token data
            self.save_token_data(
                character_id=character_info['CharacterID'],
                character_name=character_info['CharacterName'],
                access_token=token_data['access_token'],
                refresh_token=token_data['refresh_token'],
                token_type=token_data['token_type'],
                expires_at=expires_at,
                scopes=character_info['Scopes'] # Use scopes from verification
            )
            print(f"Successfully authenticated character: {character_info['CharacterName']} ({character_info['CharacterID']})")
            return character_info['CharacterID']

        except requests.exceptions.RequestException as e:
            print(f"Error during token exchange or verification: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response text: {e.response.text}")
            return None
        except KeyError as e:
            print(f"Missing expected key in response data: {e}")
            return None

    def refresh_access_token(self, character_id):
        """ Refreshes the access token using the stored refresh token. """
        token_info = self.get_token_data(character_id)
        if not token_info or 'refresh_token' not in token_info:
            print(f"No refresh token found for character ID: {character_id}")
            return None

        auth_header = base64.b64encode(f"{self.client_id}:{self.secret_key}".encode()).decode()
        headers = {
            'Authorization': f'Basic {auth_header}',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'login.eveonline.com'
        }
        data = {
            'grant_type': 'refresh_token',
            'refresh_token': token_info['refresh_token']
        }

        try:
            response = requests.post(ESI_TOKEN_URL, headers=headers, data=data, timeout=10)
            response.raise_for_status()
            new_token_data = response.json()

            # Calculate new expiry time
            expires_in = new_token_data.get('expires_in', 1200)
            expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)

            # Update token data in DB
            self.save_token_data(
                character_id=character_id,
                character_name=token_info['character_name'], # Keep original name
                access_token=new_token_data['access_token'],
                refresh_token=new_token_data.get('refresh_token', token_info['refresh_token']), # ESI might not always return a new refresh token
                token_type=new_token_data['token_type'],
                expires_at=expires_at,
                scopes=token_info['scopes'] # Assume scopes remain the same
            )
            print(f"Successfully refreshed token for character ID: {character_id}")
            return new_token_data['access_token']

        except requests.exceptions.RequestException as e:
            print(f"Error refreshing token for character ID {character_id}: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response text: {e.response.text}")
            # Consider invalidating the token if refresh fails permanently
            return None
        except KeyError as e:
            print(f"Missing expected key in refresh response data: {e}")
            return None

    def get_token_data(self, character_id):
        """ Retrieves token data for a specific character from the database. """
        conn = self._get_db_connection()
        if not conn:
            return None
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM api_tokens WHERE character_id = ?", (character_id,))
            row = cursor.fetchone()
            if row:
                # Convert expires_at and last_refreshed back to datetime objects
                token_data = dict(row)
                token_data['expires_at'] = datetime.fromisoformat(token_data['expires_at'])
                token_data['last_refreshed'] = datetime.fromisoformat(token_data['last_refreshed'])
                return token_data
            else:
                return None
        except sqlite3.Error as e:
            print(f"Database error retrieving token: {e}")
            return None
        finally:
            if conn:
                conn.close()

    def save_token_data(self, character_id, character_name, access_token, refresh_token, token_type, expires_at, scopes):
        """ Saves or updates token data in the database. """
        conn = self._get_db_connection()
        if not conn:
            return False
        now_iso = datetime.now(timezone.utc).isoformat()
        expires_at_iso = expires_at.isoformat()

        try:
            cursor = conn.cursor()
            # Use INSERT OR REPLACE (UPSERT) based on the unique character_id
            cursor.execute("""
                INSERT INTO api_tokens (character_id, character_name, access_token, refresh_token, token_type, expires_at, scopes, last_refreshed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON CONFLICT(character_id) DO UPDATE SET
                    character_name = excluded.character_name,
                    access_token = excluded.access_token,
                    refresh_token = excluded.refresh_token,
                    token_type = excluded.token_type,
                    expires_at = excluded.expires_at,
                    scopes = excluded.scopes,
                    last_refreshed = excluded.last_refreshed;
            """, (character_id, character_name, access_token, refresh_token, token_type, expires_at_iso, scopes, now_iso))
            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Database error saving token: {e}")
            conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def get_valid_access_token(self, character_id):
        """ Gets a valid access token, refreshing if necessary. """
        token_info = self.get_token_data(character_id)
        if not token_info:
            print(f"No token data found for character {character_id}.")
            return None

        # Check if token is expired or close to expiring (e.g., within 60 seconds)
        if datetime.now(timezone.utc) >= (token_info['expires_at'] - timedelta(seconds=60)):
            print(f"Token for character {character_id} expired or expiring soon. Refreshing...")
            return self.refresh_access_token(character_id)
        else:
            return token_info['access_token']

# Example Usage (can be run standalone for testing, though needs a web server for callback)
if __name__ == '__main__':
    authenticator = ESIAuthenticator()
    print("ESI Authenticator Initialized.")
    print("Please configure your ESI Client ID and Secret Key in this file.")

    # Example: Generate Auth URL
    auth_url = authenticator.get_auth_url(state="some_random_state_123")
    print("\n--- Authentication URL ---")
    print("Open this URL in your browser to authenticate:")
    print(auth_url)

    # Example: Simulate processing a callback (replace 'test_code' with actual code from ESI)
    # print("\n--- Processing Callback (Example) ---")
    # test_code = "YOUR_CALLBACK_CODE_HERE"
    # if test_code != "YOUR_CALLBACK_CODE_HERE":
    #     char_id = authenticator.process_callback(test_code)
    #     if char_id:
    #         print(f"\n--- Getting Valid Token (Example) ---")
    #         token = authenticator.get_valid_access_token(char_id)
    #         if token:
    #             print(f"Obtained valid access token for character {char_id}: {token[:10]}...") # Print first 10 chars
    #         else:
    #             print(f"Failed to get valid token for character {char_id}")
    # else:
    #     print("Skipping callback processing example as code is not provided.")

    # Example: Refresh token for a known character ID (replace 123456)
    # print("\n--- Refreshing Token (Example) ---")
    # test_char_id = 123456
    # refreshed_token = authenticator.refresh_access_token(test_char_id)
    # if refreshed_token:
    #     print(f"Refreshed token for {test_char_id}: {refreshed_token[:10]}...")
    # else:
    #     print(f"Could not refresh token for {test_char_id}. Maybe no token stored yet?")
