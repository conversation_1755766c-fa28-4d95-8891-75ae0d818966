import http.server
import socketserver
import threading
import webbrowser
import urllib.parse
from typing import Callable, Optional
import time

class CallbackHandler(http.server.BaseHTTPRequestHandler):
    """HTTP request handler for EVE SSO callback."""

    # Class variable to store the authorization code
    auth_code = None
    # Class variable to store the callback function
    callback_func = None
    # Class variable to store any error message
    error_message = None

    def do_GET(self):
        """Handle GET requests to the callback URL."""
        try:
            # Parse the URL and query parameters
            parsed_path = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_path.query)

            # Check if this is the callback path
            if parsed_path.path == '/callback':
                # Check for error in the callback
                if 'error' in query_params:
                    error = query_params['error'][0]
                    CallbackHandler.error_message = f"Authentication error: {error}"
                    self.send_response(400)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    self.wfile.write(f"<html><body><h1>Authentication Error</h1><p>{error}</p><p>You can close this window now.</p></body></html>".encode())
                    return

                # Check for the authorization code
                if 'code' in query_params:
                    code = query_params['code'][0]
                    CallbackHandler.auth_code = code

                    # Send a success response
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    self.wfile.write(b"<html><body><h1>Authentication Successful</h1><p>You can close this window now.</p></body></html>")

                    # Call the callback function if provided
                    if CallbackHandler.callback_func:
                        CallbackHandler.callback_func(code)
                else:
                    # No code found in the callback
                    CallbackHandler.error_message = "No authorization code received"
                    self.send_response(400)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    self.wfile.write(b"<html><body><h1>Authentication Error</h1><p>No authorization code received</p><p>You can close this window now.</p></body></html>")
            else:
                # Not the callback path
                self.send_response(404)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(b"<html><body><h1>404 Not Found</h1></body></html>")
        except Exception as e:
            # Handle any exceptions
            CallbackHandler.error_message = f"Error processing callback: {str(e)}"
            self.send_response(500)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(f"<html><body><h1>Server Error</h1><p>{str(e)}</p></body></html>".encode())

    def log_message(self, format, *args):
        """Override to suppress server logs or redirect them."""
        # This can be customized to log to a file or other destination
        # For now, just print to console
        print(f"CallbackServer: {format % args}")


class CallbackServer:
    """A simple HTTP server to handle EVE SSO callbacks."""

    def __init__(self, port=8080, callback_path='/callback'):
        """Initialize the callback server.

        Args:
            port: The port to listen on (default: 8080)
            callback_path: The path for the callback URL (default: '/callback')
        """
        self.port = port
        self.callback_path = callback_path
        self.server = None
        self.server_thread = None
        self.is_running = False

    def start(self, callback_func: Optional[Callable[[str], None]] = None):
        """Start the callback server in a separate thread.

        Args:
            callback_func: A function to call with the authorization code when received
        """
        if self.is_running:
            print("Callback server is already running")
            return

        # Set the callback function
        CallbackHandler.callback_func = callback_func
        CallbackHandler.auth_code = None
        CallbackHandler.error_message = None

        # Create and start the server in a separate thread
        try:
            self.server = socketserver.TCPServer(("localhost", self.port), CallbackHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True  # Don't keep the program running if main thread exits
            self.server_thread.start()
            self.is_running = True
            print(f"Callback server started on port {self.port}")
        except Exception as e:
            print(f"Error starting callback server: {e}")
            raise

    def stop(self):
        """Stop the callback server."""
        if not self.is_running:
            return

        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.is_running = False
            print("Callback server stopped")

    def wait_for_code(self, timeout=120):
        """Wait for the authorization code to be received.

        Args:
            timeout: Maximum time to wait in seconds (default: 120)

        Returns:
            The authorization code if received, None otherwise
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            if CallbackHandler.auth_code:
                return CallbackHandler.auth_code
            if CallbackHandler.error_message:
                print(f"Error waiting for code: {CallbackHandler.error_message}")
                return None
            time.sleep(0.5)

        print(f"Timeout waiting for authorization code after {timeout} seconds")
        return None


def authenticate_with_eve_sso(auth_url, callback_func=None, timeout=120):
    """Complete EVE SSO authentication flow.

    Args:
        auth_url: The EVE SSO authorization URL to open in the browser
        callback_func: Optional function to call with the authorization code
        timeout: Maximum time to wait for the callback in seconds

    Returns:
        The authorization code if successful, None otherwise
    """
    # Start the callback server
    server = CallbackServer()
    try:
        server.start(callback_func)

        # Open the browser with the auth URL
        print(f"Opening browser with auth URL: {auth_url}")
        webbrowser.open(auth_url)

        # Wait for the callback
        code = server.wait_for_code(timeout)
        return code
    finally:
        # Always stop the server
        server.stop()


# Example usage
if __name__ == "__main__":
    # This is just for testing the server directly
    test_auth_url = "https://login.eveonline.com/v2/oauth/authorize/?response_type=code&redirect_uri=http://localhost:8080/callback&client_id=YOUR_CLIENT_ID&scope=publicData&state=test"

    def handle_code(code):
        print(f"Received authorization code: {code}")

    result = authenticate_with_eve_sso(test_auth_url, handle_code)
    if result:
        print(f"Authentication successful! Code: {result}")
    else:
        print("Authentication failed or timed out")
