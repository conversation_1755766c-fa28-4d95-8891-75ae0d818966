#!/usr/bin/env python3
"""
Add character_id to orders table

This script adds the character_id column to the orders table.
"""

import os
import sys
import sqlite3
from datetime import datetime

# Database file path
DATABASE_FILE = 'eve_trader.db'

def check_table_exists(conn, table_name):
    """Check if a table exists in the database."""
    cursor = conn.cursor()
    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}';")
    return cursor.fetchone() is not None

def check_column_exists(conn, table_name, column_name):
    """Check if a column exists in a table."""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name});")
    columns = cursor.fetchall()
    return any(column[1] == column_name for column in columns)

def check_migration_status(conn, migration_name):
    """Check if a migration has already been completed."""
    try:
        cursor = conn.cursor()
        # First check if the migration_status table exists
        if not check_table_exists(conn, 'migration_status'):
            # Create the migration_status table if it doesn't exist
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS migration_status (
                migration_name TEXT PRIMARY KEY,
                completed BOOLEAN NOT NULL DEFAULT 0,
                completed_date DATETIME
            );
            """)
            conn.commit()
            return False
        
        # Check if the migration has been completed
        cursor.execute(
            "SELECT completed FROM migration_status WHERE migration_name = ?",
            (migration_name,)
        )
        result = cursor.fetchone()
        return result and result[0] == 1
    except Exception as e:
        print(f"Error checking migration status: {e}")
        return False

def execute_sql_script(conn, script_path):
    """Execute an SQL script file."""
    try:
        with open(script_path, 'r') as f:
            sql = f.read()
            cursor = conn.cursor()
            cursor.executescript(sql)
            conn.commit()
            return True
    except Exception as e:
        print(f"Error executing SQL script: {e}")
        return False

def main():
    """Main function."""
    print(f"Adding character_id column to orders table...")
    
    # Connect to the database
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        conn.row_factory = sqlite3.Row
    except sqlite3.Error as e:
        print(f"Error connecting to database: {e}")
        sys.exit(1)
    
    try:
        # Check if orders table exists
        if not check_table_exists(conn, 'orders'):
            print("Error: orders table does not exist.")
            print("Please run the application first to create the database schema.")
            conn.close()
            sys.exit(1)
        
        # Check if migration has already been completed
        migration_name = 'add_character_id_to_orders'
        if check_migration_status(conn, migration_name):
            print(f"Migration '{migration_name}' has already been completed.")
            conn.close()
            sys.exit(0)
        
        # Check if character_id column already exists
        if check_column_exists(conn, 'orders', 'character_id'):
            print("character_id column already exists in orders table.")
            
            # Update migration status
            cursor = conn.cursor()
            cursor.execute(
                "INSERT OR REPLACE INTO migration_status (migration_name, completed, completed_date) VALUES (?, 1, datetime('now'))",
                (migration_name,)
            )
            conn.commit()
            print(f"Migration '{migration_name}' marked as completed.")
            conn.close()
            sys.exit(0)
        
        # Execute the SQL script
        sql_file = 'add_character_id_to_orders.sql'
        if not os.path.exists(sql_file):
            print(f"Error: SQL file not found: {sql_file}")
            conn.close()
            sys.exit(1)
        
        if execute_sql_script(conn, sql_file):
            print(f"Successfully added character_id column to orders table.")
            
            # Update existing orders with the character_id from the first authenticated character
            cursor = conn.cursor()
            cursor.execute("SELECT character_id FROM api_tokens LIMIT 1")
            result = cursor.fetchone()
            if result:
                character_id = result[0]
                cursor.execute("UPDATE orders SET character_id = ? WHERE character_id IS NULL", (character_id,))
                conn.commit()
                print(f"Updated existing orders with character_id: {character_id}")
            
            conn.close()
            sys.exit(0)
        else:
            print("Failed to add character_id column to orders table.")
            conn.close()
            sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        conn.close()
        sys.exit(1)

if __name__ == "__main__":
    main()
