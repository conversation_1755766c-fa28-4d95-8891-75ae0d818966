-- Add FIFO inventory tracking tables

-- Create inventory_batches table to track each purchase batch
CREATE TABLE IF NOT EXISTS inventory_batches (
    batch_id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    purchase_date DATETIME NOT NULL,
    quantity_original INTEGER NOT NULL,
    quantity_remaining INTEGER NOT NULL,
    unit_cost DECIMAL(16,2) NOT NULL,
    total_cost DECIMAL(20,2) NOT NULL,
    location_id INTEGER NOT NULL,
    character_id INTEGER NOT NULL,
    order_id INTEGER,
    trade_execution_id INTEGER,
    strategy_intent TEXT,
    transaction_id INTEGER,  -- Link to the BUY transaction
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (transaction_id) REFERENCES transactions(tx_id)
);

-- Create transaction_batch_links table to track which batches were consumed by each SELL transaction
CREATE TABLE IF NOT EXISTS transaction_batch_links (
    link_id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_id INTEGER NOT NULL,  -- SELL transaction ID
    batch_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    unit_cost DECIMAL(16,2) NOT NULL,  -- Cost basis at time of sale
    profit_contribution DECIMAL(20,2) NOT NULL,  -- Profit from this specific batch
    FOREIGN KEY (transaction_id) REFERENCES transactions(tx_id),
    FOREIGN KEY (batch_id) REFERENCES inventory_batches(batch_id)
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_batches_item_location ON inventory_batches(item_id, location_id);
CREATE INDEX IF NOT EXISTS idx_batches_remaining ON inventory_batches(quantity_remaining);
CREATE INDEX IF NOT EXISTS idx_transaction_links ON transaction_batch_links(transaction_id);

-- Add migration flag to track if historical transactions have been processed
CREATE TABLE IF NOT EXISTS migration_status (
    migration_name TEXT PRIMARY KEY,
    completed BOOLEAN NOT NULL DEFAULT 0,
    completed_date DATETIME
);

-- Insert migration record
INSERT OR IGNORE INTO migration_status (migration_name, completed) VALUES ('fifo_inventory_tracking', 0);
