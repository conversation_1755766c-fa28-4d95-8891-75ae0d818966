# TinyGamer

TinyGamer is an application for automating tasks in video games using computer vision and input automation. 

## Overview

The application is built with a modular architecture that separates core functionality into distinct components:

- **Director**: Root application class that manages threads, UI, and task execution
- **Task**: Modular units of logic that run in their own processes and can be chained together
- **Sensor**: Data collection classes that identify elements on screen
- **SensorRunner**: Execution engine for sensors
- **GlobalState**: Thread-safe data store for sharing information between components

## Requirements

- Python 3.6+
- opencv-python
- pynput
- PyQt5

Install required packages:

```
pip install -r requirements.txt
```

## Directory Structure

```
TinyGamer/
├── BuilderBuddy/        # Sensor creation and testing tool
├── Games/               # Game-specific tasks and sensors
│   └── EveOnline/       # Example game implementation
│       ├── Tasks/       # Game-specific task classes
│       └── Sensors/     # Game-specific sensor definitions
├── global_state.py      # Global state singleton
├── sensor.py            # Sensor class definitions
├── sensor_runner.py     # Sensor execution engine
├── task.py              # Task base class and registry
├── director.py          # Main application controller
├── main.py              # Application entry point
└── requirements.txt     # Package dependencies
```

## Running the Applications

### Main TinyGamer Application

```
python main.py
```

This launches the Director interface, which allows you to select and run tasks. The interface displays:
- Current task status (running/paused)
- Available tasks dropdown
- Log output

#### Hotkeys

- `5` - Cleanly exits the application after stopping all running tasks

### BuilderBuddy Sensor Creator

```
python main.py --builder
```

This launches the BuilderBuddy application, which lets you:
- Create new sensors
- Define sensor parameters
- Test sensors
- Save sensor configurations

## Creating Custom Tasks

Create a new Python file in the appropriate game directory (e.g., `Games/EveOnline/Tasks/`) with a class that extends `Task` and implements the `execute()` method:

```python
from task import Task, TaskRegistry

@TaskRegistry.register
class MyCustomTask(Task):
    def __init__(self, name="MyCustomTask"):
        super().__init__(name)
    
    def execute(self):
        self.logger.info("Executing my custom task")
        
        # Request sensor data
        self.request_sensor_data(["MySensor"])
        
        # Get data from global state
        sensor_data = self.get_sensor_data("MySensor")
        
        # Process data and take action
        # ...
        
        return True, {"result": "Task completed successfully"}
```

## Creating Sensors

1. Launch BuilderBuddy: `python main.py --builder`
2. Define sensor parameters:
   - Select sensor type (OpenCV or Location)
   - Set sensor name
   - Define region of interest
   - Configure type-specific parameters
3. Test the sensor to verify functionality
4. Save the sensor to the appropriate game directory

## Architecture

TinyGamer uses a concurrent processing model:
- Each task runs in its own process for clean termination
- Tasks communicate through a thread-safe GlobalState
- Tasks can spawn subtasks, which run sequentially
- Only one task is "active" at a time

Sensors are JSON-serializable configurations that SensorRunner uses to collect data. The system supports different sensor types:
- OpenCVSensor: Uses template matching to find elements on screen
- LocationSensor: Tracks specific locations in the game

Data flow:
1. Tasks request sensor data through GlobalState
2. SensorRunner executes the appropriate sensors
3. Results are stored in GlobalState
4. Tasks retrieve and process the data
5. Tasks return success/failure and metadata

## Custom Game Support

To add support for a new game:
1. Create a directory under `Games/` with the game name
2. Add `Tasks/` and `Sensors/` subdirectories
3. Create game-specific task implementations
4. Use BuilderBuddy to create necessary sensors
