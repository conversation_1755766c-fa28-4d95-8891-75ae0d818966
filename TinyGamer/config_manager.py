import os
import configparser
import logging

class ConfigManager:
    """
    Utility class to manage application configuration settings.
    Handles reading from and writing to the settings.config file.
    """
    def __init__(self, config_file="settings.config"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.logger = logging.getLogger("ConfigManager")
        
        # Load configuration or create default if not exists
        if not os.path.exists(config_file):
            self.logger.info(f"Config file not found, creating default at: {config_file}")
            self._create_default_config()
        else:
            self.load_config()
    
    def load_config(self):
        """Load configuration from file."""
        try:
            self.config.read(self.config_file)
            self.logger.info(f"Configuration loaded from: {self.config_file}")
        except Exception as e:
            self.logger.error(f"Error loading configuration: {str(e)}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Create default configuration file."""
        try:
            # Ensure Camera section exists
            if not self.config.has_section('Camera'):
                self.config.add_section('Camera')
            
            # Set default camera device number
            self.config.set('Camera', 'CameraDeviceNumber', '0')
            
            # Write to file
            with open(self.config_file, 'w') as f:
                self.config.write(f)
            
            self.logger.info(f"Default configuration created at: {self.config_file}")
        except Exception as e:
            self.logger.error(f"Error creating default configuration: {str(e)}")
    
    def get_camera_device_number(self):
        """Get the configured camera device number."""
        try:
            if self.config.has_section('Camera') and self.config.has_option('Camera', 'CameraDeviceNumber'):
                return self.config.getint('Camera', 'CameraDeviceNumber')
            else:
                self.logger.warning("Camera device number not found in config, using default (0)")
                return 0
        except Exception as e:
            self.logger.error(f"Error getting camera device number: {str(e)}")
            return 0
    
    def set_camera_device_number(self, device_number):
        """Set the camera device number in the configuration."""
        try:
            # Ensure Camera section exists
            if not self.config.has_section('Camera'):
                self.config.add_section('Camera')
            
            # Set camera device number
            self.config.set('Camera', 'CameraDeviceNumber', str(device_number))
            
            # Write to file
            with open(self.config_file, 'w') as f:
                self.config.write(f)
            
            self.logger.info(f"Camera device number updated to: {device_number}")
            return True
        except Exception as e:
            self.logger.error(f"Error setting camera device number: {str(e)}")
            return False
    
    def get_config_value(self, section, option, default=None):
        """Get a configuration value by section and option."""
        try:
            if self.config.has_section(section) and self.config.has_option(section, option):
                return self.config.get(section, option)
            else:
                return default
        except Exception as e:
            self.logger.error(f"Error getting config value [{section}][{option}]: {str(e)}")
            return default
    
    def set_config_value(self, section, option, value):
        """Set a configuration value by section and option."""
        try:
            # Ensure section exists
            if not self.config.has_section(section):
                self.config.add_section(section)
            
            # Set option value
            self.config.set(section, option, str(value))
            
            # Write to file
            with open(self.config_file, 'w') as f:
                self.config.write(f)
            
            self.logger.info(f"Config value updated: [{section}][{option}] = {value}")
            return True
        except Exception as e:
            self.logger.error(f"Error setting config value [{section}][{option}]: {str(e)}")
            return False
