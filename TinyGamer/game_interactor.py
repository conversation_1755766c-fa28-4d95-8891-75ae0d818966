import time
from pynput.mouse import <PERSON><PERSON>, Controller as MouseController
from pynput.keyboard import Key, Controller as KeyboardController
import pyautogui

class GameInteractor:
    """Utility class for interacting with games via mouse and keyboard input"""

    def __init__(self):
        self.mouse = MouseController()
        self.keyboard = KeyboardController()

    def _scale_xy(self, x, y):
        """Scale 4k coordinates (3840x2160) to current screen size."""
        screenshot = pyautogui.screenshot()
        virtualWidth, virtualHeight = pyautogui.size()
        screen_width, screen_height = screenshot.size
        virtualXScale = virtualWidth / screen_width
        virtualYScale = virtualHeight / screen_height

        scaled_x = int(x * virtualXScale)
        scaled_y = int(y * virtualYScale)
        return scaled_x, scaled_y

    def move_to(self, x, y):
        """Move the mouse to the specified coordinates (input in 4k, scaled to current screen)"""
        x, y = self._scale_xy(x, y)
        self.mouse.position = (x, y)

    def click(self, button=Button.left):
        """Click the specified mouse button"""
        self.mouse.press(button)
        self.mouse.release(button)

    def click_down(self, button=Button.left):
        """Click the specified mouse button down"""
        self.mouse.press(button)

    def click_up(self, button=Button.left):
        """Click the specified mouse button up"""
        self.mouse.release(button)

    def click_right(self, button=Button.right):
        """Click the right mouse button"""
        self.mouse.press(button)
        self.mouse.release(button)

    def type_string(self, text):
        """Type the given string"""
        # For prices, type each character with a small delay
        if '.' in text and len(text) < 20 and any(c.isdigit() for c in text):
            # This is likely a price with a decimal point
            for char in text:
                self.keyboard.type(char)
                # Add a slightly longer delay for the decimal point
                if char == '.':
                    time.sleep(0.2)
                else:
                    time.sleep(0.05)
        else:
            # For other text, type normally
            self.keyboard.type(text)

    def press_key(self, key):
        """Press and release a keyboard key"""
        self.keyboard.press(key)
        self.keyboard.release(key)

    def hold_key(self, key):
        """Press and hold a keyboard key"""
        self.keyboard.press(key)

    def release_key(self, key):
        """Release a keyboard key that was being held"""
        self.keyboard.release(key)

    def press_key_with_modifier(self, modifier, key):
        """Press and release a keyboard key with a modifier"""
        self.keyboard.press(modifier)
        self.keyboard.press(key)
        self.keyboard.release(key)
        self.keyboard.release(modifier)

    def EvePressKeyWithModifier(self, modifier, key):
        """Press and release a keyboard key with a modifier for Eve Online"""
        self.keyboard.press(modifier)
        time.sleep(0.1)
        self.keyboard.press(key)
        time.sleep(0.1)
        self.keyboard.release(key)
        time.sleep(0.1)
        self.keyboard.release(modifier)

    def EvePressKey(self, key):
        """Press and release a keyboard key for Eve Online"""
        self.keyboard.press(key)
        time.sleep(0.1)
        self.keyboard.release(key)

    def EveSafeClick(self, x, y, button=Button.left):
        """Perform a safe click for Eve Online:
        1. Move to coordinates
        2. Wait 1 second
        3. Click
        4. Wait 0.25 seconds

        Args:
            x: x-coordinate
            y: y-coordinate
            button: Mouse button to click (default: left button)
        """
        self.move_to(x, y)
        time.sleep(1.0)  # Wait 1 second
        self.click(button)
        time.sleep(0.25)  # Wait 0.25 seconds

    def EveSafeClickRight(self, x, y, button=Button.right):
        """Perform a safe right click for Eve Online:
        1. Move to coordinates
        2. Wait 1 second
        3. Click
        4. Wait 0.25 seconds

        Args:
            x: x-coordinate
            y: y-coordinate
            button: Mouse button to click (default: right button)
        """
        self.move_to(x, y)
        time.sleep(1.0)  # Wait 1 second
        self.click_right(button)
        time.sleep(0.25)  # Wait 0.25 seconds

    def type_price(self, price_str):
        """
        Specialized method for typing price values in EVE Online.
        Ensures proper decimal point handling.

        Args:
            price_str: The price as a string
        """
        # Ensure the price is properly formatted with a decimal point
        if isinstance(price_str, (int, float)):
            price_str = f"{float(price_str):.2f}"
        else:
            # Try to convert to float and back to ensure proper format
            try:
                price_str = f"{float(price_str):.2f}"
            except ValueError:
                # If conversion fails, use as is
                pass

        # Type each character with a delay
        for char in price_str:
            self.keyboard.type(char)
            # Add a slightly longer delay for the decimal point
            if char == '.':
                time.sleep(0.3)
            else:
                time.sleep(0.1)

        # Add a small delay after typing the full price
        time.sleep(0.5)
