import cv2
import numpy as np
from PyQt5.QtWidgets import QGraphicsPixmapItem, QGraphicsView, QGraphicsScene, QFrame
from PyQt5.QtGui import QPixmap, QImage, QPainter, QPen, QBrush, QColor
from PyQt5.QtCore import QTimer, Qt, QRectF, QSizeF, pyqtSignal, QPoint
from BuilderBuddy.region_selector import RegionSelector, ResultRectangle

class WebcamCanvas(QGraphicsView):
    regionChanged = pyqtSignal(list)
    pixelSelected = pyqtSignal(tuple)

    def __init__(self, parent=None, device_id=0):
        super().__init__(parent)

        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)
        self.setRenderHint(QPainter.Antialiasing)
        self.setFrameShape(QFrame.NoFrame)
        self.setAlignment(Qt.AlignCenter)
        self.setBackgroundBrush(Qt.darkGray)
        self.pixmap_item = None
        self.region_selector = None
        self.drawing = False
        self.panning = False
        self.start_pos = None
        self.last_pan_pos = None
        self.zoom_factor = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 100.0
        self.zoom_step = 0.1
        self.is_resetting_zoom = False
        self.result_rectangle = None
        self.current_frame = None

        self.device_id = device_id
        self.capture = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        self.is_webcam_active = False
        self.fps = 60
        self.timer_interval = int(1000 / self.fps)

    def startWebcam(self):
        if self.is_webcam_active:
            return True
        self.scene.clear()
        self.pixmap_item = None
        self.region_selector = None
        self.result_rectangle = None
        self.zoom_factor = 1.0
        self.resetTransform()

        self.capture = cv2.VideoCapture(self.device_id)
        if not self.capture.isOpened():
            return False
        self.capture.set(cv2.CAP_PROP_FRAME_WIDTH, 3840)
        self.capture.set(cv2.CAP_PROP_FRAME_HEIGHT, 2160)
        actual_width = self.capture.get(cv2.CAP_PROP_FRAME_WIDTH)
        actual_height = self.capture.get(cv2.CAP_PROP_FRAME_HEIGHT)
        print(f"Webcam resolution: {actual_width}x{actual_height}")

        ret, frame = self.capture.read()
        if not ret:
            self.capture.release()
            return False

        # Resize frame to 4K resolution if needed and store
        if frame.shape[1] != 3840 or frame.shape[0] != 2160:
            frame = cv2.resize(frame, (3840, 2160), interpolation=cv2.INTER_LINEAR)
        self.current_frame = frame

        height, width, channel = frame.shape
        bytes_per_line = 3 * width
        q_img = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        pixmap = QPixmap.fromImage(q_img)

        self.pixmap_item = self.scene.addPixmap(pixmap)
        self.scene.setSceneRect(self.pixmap_item.boundingRect())
        self.centerOn(self.scene.sceneRect().center())

        self.setupRegionSelector()

        self.timer.start(self.timer_interval)
        self.is_webcam_active = True
        return True

    def stopWebcam(self):
        if self.timer.isActive():
            self.timer.stop()
        if self.capture and self.capture.isOpened():
            self.capture.release()
        self.capture = None
        self.is_webcam_active = False

    def update_frame(self):
        if not self.capture or not self.capture.isOpened():
            return
        ret, frame = self.capture.read()
        if ret:
            # Resize frame to 4K resolution if needed and store
            if frame.shape[1] != 3840 or frame.shape[0] != 2160:
                frame = cv2.resize(frame, (3840, 2160), interpolation=cv2.INTER_LINEAR)
            self.current_frame = frame

            height, width, channel = frame.shape
            bytes_per_line = 3 * width
            q_img = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            pixmap = QPixmap.fromImage(q_img)

            if self.pixmap_item is None:
                self.pixmap_item = self.scene.addPixmap(pixmap)
                self.scene.setSceneRect(self.pixmap_item.boundingRect())
                if self.zoom_factor == 1.0 and not self.panning:
                    self.centerOn(self.scene.sceneRect().center())
                if self.region_selector is None:
                    self.setupRegionSelector()
            else:
                self.pixmap_item.setPixmap(pixmap)
        else:
            print("Failed to grab frame")

    def setupRegionSelector(self):
        if self.region_selector:
            try:
                self.scene.removeItem(self.region_selector)
            except Exception as e:
                print(f"Error removing existing region selector: {e}")

        initial_rect = QRectF(100, 100, 100, 100)
        if self.pixmap_item:
            scene_rect = self.scene.sceneRect()
            initial_rect = initial_rect.intersected(scene_rect)
            if initial_rect.width() <= 0 or initial_rect.height() <= 0:
                initial_rect = QRectF(scene_rect.topLeft(), QSizeF(min(100, scene_rect.width()), min(100, scene_rect.height())))

        self.region_selector = RegionSelector(initial_rect)
        self.scene.addItem(self.region_selector)
        self.region_selector.signals.regionChanged.connect(self.onRegionSelectorChanged)
        self.region_selector.updatePenWidth(self.zoom_factor)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        if self.pixmap_item:
            self.centerOn(self.scene.sceneRect().center())

    def wheelEvent(self, event):
        # Disable mousewheel zoom
        event.ignore()

    def mousePressEvent(self, event):
        if not self.pixmap_item:
            super().mousePressEvent(event)
            return

        if event.button() == Qt.RightButton:
            self.drawing = True
            scene_pos = self.mapToScene(event.pos())
            clamped_x = max(0, min(scene_pos.x(), self.pixmap_item.pixmap().width()))
            clamped_y = max(0, min(scene_pos.y(), self.pixmap_item.pixmap().height()))
            self.start_pos = QPoint(int(clamped_x), int(clamped_y))

            self.setupRegionSelector()
            self.region_selector.setRect(QRectF(self.start_pos, QSizeF(1, 1)))
            return

        if self.region_selector and self.region_selector.isUnderMouse():
            super().mousePressEvent(event)
            return

        if event.button() == Qt.LeftButton:
            self.panning = True
            self.last_pan_pos = event.pos()
            self.setCursor(Qt.ClosedHandCursor)
            return

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if self.drawing and self.start_pos:
            scene_pos = self.mapToScene(event.pos())
            clamped_x = max(0, min(scene_pos.x(), self.pixmap_item.pixmap().width()))
            clamped_y = max(0, min(scene_pos.y(), self.pixmap_item.pixmap().height()))
            current_pos = QPoint(int(clamped_x), int(clamped_y))

            rect = QRectF(self.start_pos, current_pos).normalized()
            # Set position and size separately to ensure coordinates are preserved
            self.region_selector.setPos(rect.x(), rect.y())
            self.region_selector.setRect(QRectF(0, 0, rect.width(), rect.height()))
            self.regionChanged.emit([rect.x(), rect.y(), rect.width(), rect.height()])
            img_rect = self.pixmap_item.boundingRect()
            rect = rect.intersected(img_rect)

            # Set position and relative rect separately
            self.region_selector.setPos(rect.x(), rect.y())
            self.region_selector.setRect(QRectF(0, 0, rect.width(), rect.height()))
            return

        if self.panning and self.last_pan_pos:
            delta = event.pos() - self.last_pan_pos
            self.last_pan_pos = event.pos()
            self.horizontalScrollBar().setValue(self.horizontalScrollBar().value() - delta.x())
            self.verticalScrollBar().setValue(self.verticalScrollBar().value() - delta.y())
            return

        if self.region_selector:
             super().mouseMoveEvent(event)
             return

        super().mouseMoveEvent(event)

    def onRegionSelectorChanged(self, region):
        self.regionChanged.emit(region)
        if region[2] == 1 and region[3] == 1:
            color = self.getPixelColor(region[0], region[1])
            if color:
                self.pixelSelected.emit(color)

    def mouseReleaseEvent(self, event):
        if self.drawing:
            self.drawing = False
            region = self.region_selector.getRegion()
            if region[2] == 1 and region[3] == 1:
                color = self.getPixelColor(region[0], region[1])
                if color:
                    self.pixelSelected.emit(color)
            self.regionChanged.emit(region)
            return

        if self.panning:
            self.panning = False
            self.last_pan_pos = None
            self.setCursor(Qt.ArrowCursor)
            return

        if self.region_selector and (event.modifiers() & Qt.LeftButton):
             super().mouseReleaseEvent(event)
             region = self.region_selector.getRegion()
             self.regionChanged.emit(region)
             if region[2] == 1 and region[3] == 1:
                 color = self.getPixelColor(region[0], region[1])
                 if color:
                     self.pixelSelected.emit(color)
             return

        super().mouseReleaseEvent(event)

    def getRegion(self):
        if self.region_selector:
            return self.region_selector.getRegion()
        return [0, 0, 0, 0]

    def setRegion(self, region):
        if not self.region_selector:
            self.setupRegionSelector()
            if not self.region_selector:
                 print("Region selector could not be initialized.")
                 return

        if not self.pixmap_item:
            print("Cannot set region: No pixmap loaded.")
            return

        try:
            vx, vy, vw, vh = map(float, region) # Virtual coordinates from sensor
        except (ValueError, TypeError):
            print(f"Invalid region format received: {region}")
            return

        # Get actual dimensions of the displayed pixmap
        pixmap = self.pixmap_item.pixmap()
        actual_w = pixmap.width()
        actual_h = pixmap.height()

        # Since we're working with actual 4K resolution, no scaling is necessary
        # Just use the values as-is
        ax = int(vx)
        ay = int(vy)
        aw = max(1, int(vw)) # Ensure width is at least 1
        ah = max(1, int(vh)) # Ensure height is at least 1

        # Define the rectangle size based on actual width/height, relative origin (0,0)
        relative_rect = QRectF(0, 0, aw, ah)

        if not relative_rect.isValid():
            print(f"Scaled region [ax={ax}, ay={ay}, aw={aw}, ah={ah}] results in invalid QRectF.")
            return

        # Set the item's position in the scene using *actual* scaled coordinates
        self.region_selector.setPos(ax, ay)
        # Set the item's internal rectangle size (relative to its position) using *actual* scaled dimensions
        self.region_selector.setRect(relative_rect)

        # Emit the original *virtual* region coordinates, as that's what the sensor uses
        self.regionChanged.emit([int(vx), int(vy), int(vw), int(vh)])

    def keyPressEvent(self, event):
        key = event.key()
        if key == Qt.Key_Plus or key == Qt.Key_Equal:
            self.zoomIn()
        elif key == Qt.Key_Minus:
            self.zoomOut()
        elif key == Qt.Key_0:
            self.resetZoom()
        elif key == Qt.Key_Left:
            self.pan(-50, 0)
        elif key == Qt.Key_Right:
            self.pan(50, 0)
        elif key == Qt.Key_Up:
            self.pan(0, -50)
        elif key == Qt.Key_Down:
            self.pan(0, 50)
        else:
            super().keyPressEvent(event)

    def zoomIn(self):
        self.zoomWithStep(1.2)

    def zoomOut(self):
        self.zoomWithStep(1 / 1.2)

    def zoomWithStep(self, factor):
        if not self.pixmap_item:
            return
        old_zoom = self.zoom_factor
        new_zoom_factor = self.zoom_factor * factor
        self.zoom_factor = max(self.min_zoom, min(new_zoom_factor, self.max_zoom))

        if old_zoom != self.zoom_factor:
            center_point = self.mapToScene(self.viewport().rect().center())
            self.resetTransform()
            self.scale(self.zoom_factor, self.zoom_factor)
            self.centerOn(center_point)
            if self.region_selector:
                self.region_selector.updatePenWidth(self.zoom_factor)
            if self.result_rectangle:
                self.result_rectangle.updatePenWidth(self.zoom_factor)

    def resetZoom(self):
        if not self.pixmap_item:
             return
        self.is_resetting_zoom = True
        self.zoom_factor = 1.0
        self.resetTransform()
        self.scale(self.zoom_factor, self.zoom_factor)
        self.centerOn(self.scene.sceneRect().center())
        if self.region_selector:
            self.region_selector.updatePenWidth(self.zoom_factor)
        if self.result_rectangle:
            self.result_rectangle.updatePenWidth(self.zoom_factor)
        self.is_resetting_zoom = False

    def pan(self, dx, dy):
        self.horizontalScrollBar().setValue(self.horizontalScrollBar().value() + dx)
        self.verticalScrollBar().setValue(self.verticalScrollBar().value() + dy)

    def getPixelColor(self, x, y):
        if not self.pixmap_item:
            return None
        pixmap = self.pixmap_item.pixmap()
        if x < 0 or y < 0 or x >= pixmap.width() or y >= pixmap.height():
            return None
        image = pixmap.toImage()
        color = image.pixelColor(int(x), int(y))
        return (color.red(), color.green(), color.blue())

    def showResultRectangle(self, x, y, width, height, color=Qt.green):
        if not self.result_rectangle:
            self.result_rectangle = ResultRectangle()
            self.scene.addItem(self.result_rectangle)
        self.result_rectangle.setRect(QRectF(x, y, width, height))
        # Set the pen (outline) color
        pen = QPen(color, self.result_rectangle.base_pen_width, Qt.SolidLine)
        pen.setCosmetic(True)  # Keep pen width constant regardless of zoom
        self.result_rectangle.setPen(pen)
        # Set the brush (fill) color (semi-transparent)
        fill_color = QColor(color)
        fill_color.setAlpha(50) # Adjust alpha for transparency (0-255)
        self.result_rectangle.setBrush(QBrush(fill_color))
        self.result_rectangle.updatePenWidth(self.zoom_factor)
        self.result_rectangle.show()

    def hideResultRectangle(self):
        if self.result_rectangle:
            self.result_rectangle.hide()

    def showParentAnchor(self, x, y):
        """Show a marker for the parent sensor's anchor point"""
        # Remove any existing parent anchor marker
        if hasattr(self, 'parent_anchor_marker') and self.parent_anchor_marker:
            self.scene.removeItem(self.parent_anchor_marker)
            self.parent_anchor_marker = None

        # Create a blue marker at the anchor point
        marker_size = 10
        anchor_rect = QRectF(x - marker_size/2, y - marker_size/2, marker_size, marker_size)
        self.parent_anchor_marker = self.scene.addRect(anchor_rect, QPen(Qt.blue, 2), QBrush(Qt.blue, Qt.SolidPattern))

        # Add a label
        if hasattr(self, 'parent_anchor_text') and self.parent_anchor_text:
            self.scene.removeItem(self.parent_anchor_text)
        self.parent_anchor_text = self.scene.addSimpleText(f"Parent Anchor (0,0)")
        self.parent_anchor_text.setPos(x + marker_size, y - marker_size)
        self.parent_anchor_text.setBrush(QBrush(Qt.blue))
