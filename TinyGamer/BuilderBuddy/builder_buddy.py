import os
import sys
import base64
import json
import cv2
import numpy as np
import logging
from typing import Optional, Dict, Any, List, Tuple
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QCheckBox, QFileDialog, QTabWidget, QListWidget, QListWidgetItem,
    QScrollArea, QFrame, QGridLayout, QMessageBox, QGroupBox,
    QFormLayout, QSizePolicy, QTextEdit, QSplitter
)
from PyQt5.QtGui import QPixmap, QImage, QPainter, QPen, QBrush, QColor
from PyQt5.QtCore import Qt, QRect, QPoint, QSize, pyqtSignal, QObject
from pynput import keyboard

# Import webcam widget
from BuilderBuddy.webcam_widget import WebcamWidget

# Add parent directory to path to import our modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from sensor import Sensor, OpenCVSensor, LocationSensor, YoloSensor, OCRSensor, SamenessSensor, Rect # Added SamenessSensor
from sensor_runner import SensorRunner
from global_state import GlobalState

class SensorCreationWidget(QWidget):
    """Widget for creating and editing sensors"""

    # Signal to indicate that a region has been updated
    region_updated = pyqtSignal(list)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_sensor = None
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QVBoxLayout(self)

        # Title for this section
        title = QLabel("Sensor Properties")
        title.setStyleSheet("font-weight: bold; font-size: 12pt;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)

        # Sensor type selection
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("Sensor Type:"))
        self.type_combo = QComboBox()
        self.type_combo.addItems(["OpenCV", "Location", "Yolo", "OCR", "Sameness"]) # Added Sameness
        self.type_combo.currentTextChanged.connect(self.sensor_type_changed)
        type_layout.addWidget(self.type_combo)
        main_layout.addLayout(type_layout)

        # Common properties form
        common_group = QGroupBox("Common Properties")
        common_layout = QFormLayout(common_group)

        self.name_edit = QLineEdit()
        common_layout.addRow("Sensor Name:", self.name_edit)

        # Parent sensor field and anchor button
        parent_layout = QHBoxLayout()
        self.parent_edit = QLineEdit()
        parent_layout.addWidget(self.parent_edit)

        self.set_parent_anchor_button = QPushButton("Set Parent Anchor")
        self.set_parent_anchor_button.setCheckable(True)
        self.set_parent_anchor_button.clicked.connect(self.toggle_parent_anchor_mode)
        parent_layout.addWidget(self.set_parent_anchor_button)

        # Add Refresh Parent button - only visible when a parent is specified
        self.refresh_parent_button = QPushButton("Refresh Parent")
        self.refresh_parent_button.clicked.connect(self.refresh_parent)
        self.refresh_parent_button.setVisible(False)  # Hidden by default
        parent_layout.addWidget(self.refresh_parent_button)

        common_layout.addRow("Parent Sensor:", parent_layout)

        # Variables for parent anchor functionality
        self.parent_anchor_mode = False
        self.parent_anchor_point = None
        self.parent_anchor_marker = None

        # Region selector
        region_layout = QHBoxLayout()
        self.region_x = QSpinBox()
        self.region_x.setRange(-3840, 3840)  # Allow negative values for relative coordinates
        self.region_y = QSpinBox()
        self.region_y.setRange(-2160, 2160)  # Allow negative values for relative coordinates
        self.region_width = QSpinBox()
        self.region_width.setRange(1, 3840)
        self.region_width.setValue(100)
        self.region_height = QSpinBox()
        self.region_height.setRange(1, 2160)
        self.region_height.setValue(100)

        region_layout.addWidget(QLabel("X:"))
        region_layout.addWidget(self.region_x)
        region_layout.addWidget(QLabel("Y:"))
        region_layout.addWidget(self.region_y)
        region_layout.addWidget(QLabel("W:"))
        region_layout.addWidget(self.region_width)
        region_layout.addWidget(QLabel("H:"))
        region_layout.addWidget(self.region_height)

        # Connect spinbox value changes to update the webcam canvas rectangle
        self.region_x.valueChanged.connect(self.update_canvas_from_form)
        self.region_y.valueChanged.connect(self.update_canvas_from_form)
        self.region_width.valueChanged.connect(self.update_canvas_from_form)
        self.region_height.valueChanged.connect(self.update_canvas_from_form)

        common_layout.addRow("Region:", region_layout)

        # Timeout setting
        self.timeout_spin = QDoubleSpinBox()
        self.timeout_spin.setRange(0.1, 10.0)  # 0.1 to 10 seconds
        self.timeout_spin.setSingleStep(0.1)
        self.timeout_spin.setValue(1.0)  # Default value
        self.timeout_spin.setDecimals(1)
        common_layout.addRow("Timeout (sec):", self.timeout_spin)

        main_layout.addWidget(common_group)

        # OpenCV sensor specific properties
        self.opencv_group = QGroupBox("OpenCV Sensor Properties")
        opencv_layout = QFormLayout(self.opencv_group)

        # Template image selection
        template_layout = QVBoxLayout()

        # Template preview
        self.template_preview = QLabel()
        self.template_preview.setFixedSize(100, 100)
        self.template_preview.setAlignment(Qt.AlignCenter)
        template_layout.addWidget(self.template_preview)

        # Buttons layout (horizontal)
        buttons_layout = QHBoxLayout()

        self.select_image_button = QPushButton("Select Image")
        self.select_image_button.clicked.connect(self.select_template_image)
        buttons_layout.addWidget(self.select_image_button)

        self.capture_image_button = QPushButton("Capture Image")
        self.capture_image_button.clicked.connect(self.capture_template_image)
        buttons_layout.addWidget(self.capture_image_button)

        # Add buttons layout to template layout
        template_layout.addLayout(buttons_layout)

        opencv_layout.addRow("", template_layout)

        # Threshold
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.1, 1.0)
        self.threshold_spin.setSingleStep(0.05)
        self.threshold_spin.setValue(0.8)
        opencv_layout.addRow("Match Threshold:", self.threshold_spin)

        # Vertical priority
        self.vertical_priority = QCheckBox()
        opencv_layout.addRow("Vertical Priority:", self.vertical_priority)

        main_layout.addWidget(self.opencv_group)

        # Location sensor specific properties
        self.location_group = QGroupBox("Location Sensor Properties")
        location_layout = QFormLayout(self.location_group)

        location_info = QLabel("Location sensors use the common region properties.")
        location_layout.addRow("", location_info)

        main_layout.addWidget(self.location_group)

        # Yolo sensor specific properties
        self.yolo_group = QGroupBox("YOLO Sensor Properties")
        yolo_layout = QFormLayout(self.yolo_group)

        # Model path selection
        model_layout = QHBoxLayout()
        self.model_path_edit = QLineEdit()
        model_layout.addWidget(self.model_path_edit)

        self.browse_model_button = QPushButton("Browse")
        self.browse_model_button.clicked.connect(self.select_yolo_model)
        model_layout.addWidget(self.browse_model_button)

        yolo_layout.addRow("Model Path:", model_layout)

        # Confidence threshold
        self.confidence_spin = QDoubleSpinBox()
        self.confidence_spin.setRange(0.01, 1.0)
        self.confidence_spin.setSingleStep(0.05)
        self.confidence_spin.setValue(0.25)
        self.confidence_spin.setDecimals(2)
        yolo_layout.addRow("Confidence Threshold:", self.confidence_spin)

        # Image size
        self.image_size_spin = QSpinBox()
        self.image_size_spin.setRange(128, 1280)
        self.image_size_spin.setSingleStep(32)
        self.image_size_spin.setValue(640)
        yolo_layout.addRow("Image Size:", self.image_size_spin)

        # Class filter
        self.class_filter_edit = QLineEdit()
        self.class_filter_edit.setPlaceholderText("Comma-separated list of class IDs (e.g., 0,1,2)")
        yolo_layout.addRow("Class Filter (optional):", self.class_filter_edit)

        # Device selection
        self.device_combo = QComboBox()
        self.device_combo.addItems(["0", "cpu"])
        yolo_layout.addRow("Device:", self.device_combo)

        main_layout.addWidget(self.yolo_group)

        # OCR sensor specific properties
        self.ocr_group = QGroupBox("OCR Sensor Properties")
        ocr_layout = QFormLayout(self.ocr_group)

        # Search text input
        self.search_text_edit = QLineEdit()
        self.search_text_edit.setPlaceholderText("Text to search for in the region")
        ocr_layout.addRow("Search Text:", self.search_text_edit)

        # Confidence threshold
        self.ocr_confidence_spin = QDoubleSpinBox()
        self.ocr_confidence_spin.setRange(0.1, 1.0)
        self.ocr_confidence_spin.setSingleStep(0.05)
        self.ocr_confidence_spin.setValue(0.7)
        self.ocr_confidence_spin.setDecimals(2)
        ocr_layout.addRow("Confidence Threshold:", self.ocr_confidence_spin)

        # Language selection
        self.ocr_language_combo = QComboBox()
        self.ocr_language_combo.addItems(["eng", "fra", "deu", "spa", "ita", "jpn", "kor", "chi_sim"])
        ocr_layout.addRow("Language:", self.ocr_language_combo)

        # Scale factor for image preprocessing
        self.scale_factor_spin = QDoubleSpinBox()
        self.scale_factor_spin.setRange(0.5, 3.0)
        self.scale_factor_spin.setSingleStep(0.1)
        self.scale_factor_spin.setValue(1.0)
        self.scale_factor_spin.setDecimals(1)
        ocr_layout.addRow("Scale Factor:", self.scale_factor_spin)

        # Whitelist characters
        self.whitelist_edit = QLineEdit()
        self.whitelist_edit.setPlaceholderText("Optional character whitelist (e.g., '0123456789')")
        ocr_layout.addRow("Character Whitelist:", self.whitelist_edit)

        main_layout.addWidget(self.ocr_group)

        # Sameness sensor specific properties
        self.sameness_group = QGroupBox("Sameness Sensor Properties")
        sameness_layout = QFormLayout(self.sameness_group)

        # Confidence threshold for sameness
        self.sameness_confidence_spin = QDoubleSpinBox()
        self.sameness_confidence_spin.setRange(0.1, 1.0) # Usually needs high confidence
        self.sameness_confidence_spin.setSingleStep(0.01)
        self.sameness_confidence_spin.setValue(0.95) # Default high value
        self.sameness_confidence_spin.setDecimals(2)
        sameness_layout.addRow("Confidence Threshold:", self.sameness_confidence_spin)

        sameness_info = QLabel("Compares the region to its state during the previous run.")
        sameness_layout.addRow("", sameness_info)

        main_layout.addWidget(self.sameness_group)

        # Action buttons
        button_layout = QHBoxLayout()

        self.test_button = QPushButton("Test Sensor")
        self.test_button.clicked.connect(self.test_sensor)
        button_layout.addWidget(self.test_button)



        self.save_button = QPushButton("Save Sensor")
        self.save_button.clicked.connect(self.save_sensor)
        button_layout.addWidget(self.save_button)

        # Clear form button removed as requested

        main_layout.addLayout(button_layout)

        # Test results area
        self.results_area = QTextEdit()
        self.results_area.setReadOnly(True)
        self.results_area.setPlaceholderText("Test results will appear here")
        main_layout.addWidget(self.results_area)

        # Set default sensor type
        self.sensor_type_changed(self.type_combo.currentText())

        # Template image data
        self.template_image_data = None

    def sensor_type_changed(self, sensor_type: str):
        """Show/hide specific properties based on sensor type"""
        # First hide all sensor-specific groups
        self.opencv_group.setVisible(False)
        self.location_group.setVisible(False)
        self.yolo_group.setVisible(False)
        self.ocr_group.setVisible(False)
        self.sameness_group.setVisible(False)

        # Then show only the one that matches the selected type
        if sensor_type == "OpenCV":
            self.opencv_group.setVisible(True)
        elif sensor_type == "Location":
            self.location_group.setVisible(True)
        elif sensor_type == "Yolo":
            self.yolo_group.setVisible(True)
        elif sensor_type == "OCR":
            self.ocr_group.setVisible(True)
        elif sensor_type == "Sameness":
            self.sameness_group.setVisible(True)

    def select_template_image(self):
        """Open file dialog to select template image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Template Image", "", "Image Files (*.png *.jpg *.jpeg *.bmp)"
        )

        if file_path:
            try:
                # Read image and convert to bytes
                img = cv2.imread(file_path)
                if img is None:
                    raise ValueError("Failed to read image")

                # Convert to bytes
                _, img_data = cv2.imencode('.png', img)
                self.template_image_data = img_data.tobytes()

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load image: {str(e)}")

    def select_yolo_model(self):
        """Open file dialog to select YOLO model file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select YOLO Model", "", "YOLO Models (*.pt *.pth *.onnx)"
        )

        if file_path:
            self.model_path_edit.setText(file_path)

    def capture_template_image(self):
        """Capture a screen region for the template image"""
        if hasattr(self.window(), 'webcam_widget'):
            webcam = self.window().webcam_widget
            if webcam.webcam_canvas.is_webcam_active:
                webcam.capture_template()
            else:
                QMessageBox.warning(self, "Error", "Please start the webcam first")
        else:
            QMessageBox.warning(self, "Error", "Webcam not available")

    def clear_form(self):
        """Reset all form fields"""
        self.name_edit.clear()
        self.type_combo.setCurrentIndex(0)
        self.region_x.setValue(0)
        self.region_y.setValue(0)
        self.region_width.setValue(100)
        self.region_height.setValue(100)
        self.threshold_spin.setValue(0.8)
        self.vertical_priority.setChecked(False)
        self.x_position.setValue(0)
        self.y_position.setValue(0)
        self.template_image_data = None
        self.update_template_preview(None)
        self.model_path_edit.clear()
        self.confidence_spin.setValue(0.25)
        self.image_size_spin.setValue(640)
        self.class_filter_edit.clear()
        self.device_combo.setCurrentIndex(0)
        # Sameness specific
        self.sameness_confidence_spin.setValue(0.95)
        # Clear results and current sensor
        self.results_area.clear()
        self.current_sensor = None

    def create_sensor_from_form(self) -> Optional[Sensor]:
        """Create a sensor object from the form data"""
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "Validation Error", "Sensor name is required")
            return None

        # Create region
        region = Rect(
            x=self.region_x.value(),
            y=self.region_y.value(),
            width=self.region_width.value(),
            height=self.region_height.value()
        )

        # Get common values from the UI
        timeout = self.timeout_spin.value()
        parent = self.parent_edit.text().strip()
        parent_anchor_point = self.parent_anchor_point if parent else None

        sensor_type = self.type_combo.currentText()

        if sensor_type == "OpenCV":
            sensor = OpenCVSensor(
                sensor_name=name,
                sensor_type="OpenCV",
                region=region,
                timeout=timeout,
                parent=parent,
                parent_anchor_point=parent_anchor_point,
                threshold=self.threshold_spin.value(),
                vertical_priority=self.vertical_priority.isChecked()
            )

            if self.template_image_data:
                sensor.set_template_image(self.template_image_data)

        elif sensor_type == "Location":
            sensor = LocationSensor(
                sensor_name=name,
                sensor_type="Location",
                region=region,
                timeout=timeout,
                parent=parent,
                parent_anchor_point=parent_anchor_point
            )
        elif sensor_type == "Yolo":
            # Parse class filter if provided
            class_filter = None
            class_filter_text = self.class_filter_edit.text().strip()
            if class_filter_text:
                try:
                    class_filter = [int(cls.strip()) for cls in class_filter_text.split(',') if cls.strip()]
                except ValueError:
                    QMessageBox.warning(self, "Validation Error", "Class filter must be comma-separated integers")
                    return None

            # Get model path and validate
            model_path = self.model_path_edit.text().strip()
            if not model_path:
                QMessageBox.warning(self, "Validation Error", "Model path is required for YOLO sensors")
                return None

            if not os.path.exists(model_path):
                QMessageBox.warning(self, "Validation Error", f"Model file not found: {model_path}")
                return None

            sensor = YoloSensor(
                sensor_name=name,
                sensor_type="Yolo",
                region=region,
                timeout=timeout,
                parent=parent,
                parent_anchor_point=parent_anchor_point,
                model_path=model_path,
                confidence_threshold=self.confidence_spin.value(),
                image_size=self.image_size_spin.value(),
                class_filter=class_filter,
                device=self.device_combo.currentText()
            )
        elif sensor_type == "OCR":
            # Validate search text
            search_text = self.search_text_edit.text().strip()
            if not search_text:
                QMessageBox.warning(self, "Validation Error", "Search text is required for OCR sensors")
                return None

            # Get whitelist characters if specified
            whitelist_chars = self.whitelist_edit.text().strip()

            # Create OCR sensor
            sensor = OCRSensor(
                sensor_name=name,
                sensor_type="OCR",
                region=region,
                timeout=timeout,
                parent=parent,
                parent_anchor_point=parent_anchor_point,
                search_text=search_text,
                confidence_threshold=self.ocr_confidence_spin.value(),
                language=self.ocr_language_combo.currentText(),
                scale_factor=self.scale_factor_spin.value(),
                whitelist_chars=whitelist_chars
            )
        elif sensor_type == "Sameness":
            sensor = SamenessSensor(
                sensor_name=name,
                sensor_type="Sameness",
                region=region,
                timeout=timeout,
                parent=parent,
                parent_anchor_point=parent_anchor_point,
                confidence_threshold=self.sameness_confidence_spin.value()
                # _previous_image_data_b64 is handled internally by the runner
            )
        else:
            QMessageBox.warning(self, "Error", f"Unknown sensor type: {sensor_type}")
            return None

        return sensor

    def load_sensor_to_form(self, sensor: Sensor):
        """Load a sensor's data into the form"""
        self.current_sensor = sensor

        # Common properties
        self.name_edit.setText(sensor.sensor_name)
        self.region_x.setValue(sensor.region.x)
        self.region_y.setValue(sensor.region.y)
        self.region_width.setValue(sensor.region.width)
        self.region_height.setValue(sensor.region.height)
        self.timeout_spin.setValue(sensor.timeout)  # Set timeout value
        self.parent_edit.setText(sensor.parent)  # Set parent value
        self.parent_anchor_point = sensor.parent_anchor_point  # Set parent anchor point

        # Show or hide the refresh parent button based on whether this sensor has a parent
        self.refresh_parent_button.setVisible(bool(sensor.parent and sensor.parent.strip()))

        # If there's a parent anchor point, show it in the webcam view
        if sensor.parent_anchor_point and hasattr(self.window(), 'webcam_widget') and hasattr(self.window().webcam_widget, 'webcam_canvas'):
            x, y = sensor.parent_anchor_point
            self.window().webcam_widget.webcam_canvas.showParentAnchor(x, y)

        # Emit signal to update the webcam region
        # If there's a parent anchor point, we need to adjust the coordinates for display
        if sensor.parent_anchor_point and sensor.parent.strip():
            parent_x, parent_y = sensor.parent_anchor_point
            # For display, we add the parent's position to get absolute screen coordinates
            adjusted_x = parent_x + sensor.region.x
            adjusted_y = parent_y + sensor.region.y
            self.region_updated.emit([adjusted_x, adjusted_y, sensor.region.width, sensor.region.height])
        else:
            self.region_updated.emit([sensor.region.x, sensor.region.y, sensor.region.width, sensor.region.height])

        # Set the type combo box
        self.type_combo.setCurrentText(sensor.sensor_type)

        # Type-specific properties
        if sensor.sensor_type == "OpenCV" and isinstance(sensor, OpenCVSensor):
            self.threshold_spin.setValue(sensor.threshold)
            self.vertical_priority.setChecked(sensor.vertical_priority)

            if sensor.base64_encoded_image_data:
                self.template_image_data = sensor.get_template_image()
                self.update_template_preview(self.template_image_data)
            else:
                self.template_image_data = None

        elif sensor.sensor_type == "Location" and isinstance(sensor, LocationSensor):
            # Location sensors now just use common region properties
            pass

        elif sensor.sensor_type == "Yolo" and isinstance(sensor, YoloSensor):
            self.model_path_edit.setText(sensor.model_path)
            self.confidence_spin.setValue(sensor.confidence_threshold)
            self.image_size_spin.setValue(sensor.image_size)

            # Set class filter if present
            if sensor.class_filter:
                self.class_filter_edit.setText(','.join(map(str, sensor.class_filter)))
            else:
                self.class_filter_edit.clear()

            # Set device
            device_index = self.device_combo.findText(sensor.device)
            if device_index >= 0:
                self.device_combo.setCurrentIndex(device_index)
            else:
                self.device_combo.setCurrentIndex(0)

        elif sensor.sensor_type == "OCR" and isinstance(sensor, OCRSensor):
            # Set OCR properties
            self.search_text_edit.setText(sensor.search_text)
            self.ocr_confidence_spin.setValue(sensor.confidence_threshold)

            # Set language
            language_index = self.ocr_language_combo.findText(sensor.language)
            if language_index >= 0:
                self.ocr_language_combo.setCurrentIndex(language_index)
            else:
                self.ocr_language_combo.setCurrentIndex(0)  # Default to English

            # Set scale factor
            self.scale_factor_spin.setValue(sensor.scale_factor)

            # Set whitelist characters
            self.whitelist_edit.setText(sensor.whitelist_chars)

        elif sensor.sensor_type == "Sameness" and isinstance(sensor, SamenessSensor):
            self.sameness_confidence_spin.setValue(sensor.confidence_threshold)
            # No visual preview needed for Sameness sensor itself


    def update_template_preview(self, template_bytes):
        """Update the template preview image"""
        if template_bytes:
            # Convert bytes to QImage
            image = QImage.fromData(template_bytes)
            if not image.isNull():
                # Scale image to fit preview, keeping aspect ratio
                pixmap = QPixmap.fromImage(image)
                scaled = pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.template_preview.setPixmap(scaled)
                return

        # Clear preview if no valid image
        self.template_preview.clear()

    def save_sensor(self):
        """Save the sensor to a file"""
        sensor = self.create_sensor_from_form()
        if not sensor:
            return

        # Ask user for save location
        game_folder = QFileDialog.getExistingDirectory(
            self,
            "Select Game Folder",
            os.path.join(os.path.dirname(os.path.dirname(__file__)), "Games")
        )

        if not game_folder:
            return

        # Ensure Sensors directory exists
        sensors_dir = os.path.join(game_folder, "Sensors")
        os.makedirs(sensors_dir, exist_ok=True)

        # Save the sensor
        file_path = os.path.join(sensors_dir, f"{sensor.sensor_name}.json")

        try:
            sensor.save_to_file(file_path)
            QMessageBox.information(
                self,
                "Success",
                f"Sensor saved to {file_path}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to save sensor: {str(e)}"

            )

    def update_canvas_from_form(self):
        """Update the webcam canvas region selector based on form values"""
        # Get current values from form
        x = self.region_x.value()
        y = self.region_y.value()
        width = self.region_width.value()
        height = self.region_height.value()

        # If there's a parent anchor point, adjust the coordinates for display
        if self.parent_anchor_point and self.parent_edit.text().strip():
            parent_x, parent_y = self.parent_anchor_point
            # For display, we add the parent's position to get absolute screen coordinates
            x = parent_x + x
            y = parent_y + y

        # Emit signal to update webcam canvas
        self.region_updated.emit([x, y, width, height])

    def toggle_parent_anchor_mode(self):
        """Toggle the parent anchor selection mode"""
        # Check if a parent sensor is specified
        parent_name = self.parent_edit.text().strip()
        if not parent_name:
            QMessageBox.warning(self, "No Parent Specified", "Please enter a parent sensor name first.")
            self.set_parent_anchor_button.setChecked(False)
            return

        # Toggle the mode
        self.parent_anchor_mode = self.set_parent_anchor_button.isChecked()

        if self.parent_anchor_mode:
            # Change button appearance to indicate active mode
            self.set_parent_anchor_button.setStyleSheet("background-color: #3498db; color: white;")
            self.results_area.setText("Parent Anchor Mode Active: Click on the webcam view to set the parent anchor point.")
        else:
            # Reset button appearance
            self.set_parent_anchor_button.setStyleSheet("")
            self.results_area.setText("")

        # Show the refresh parent button if there's a parent
        self.refresh_parent_button.setVisible(bool(parent_name))

    def set_parent_anchor(self, x, y):
        """Set the parent anchor point and adjust region coordinates"""
        # Store the anchor point
        self.parent_anchor_point = (x, y)

        # Turn off anchor mode
        self.parent_anchor_mode = False
        self.set_parent_anchor_button.setChecked(False)
        self.set_parent_anchor_button.setStyleSheet("")

        # Show anchor in the webcam view
        if hasattr(self.window(), 'webcam_widget') and hasattr(self.window().webcam_widget, 'webcam_canvas'):
            webcam_canvas = self.window().webcam_widget.webcam_canvas
            webcam_canvas.showParentAnchor(x, y)

        # Adjust the current region coordinates to be relative to the anchor
        current_x = self.region_x.value()
        current_y = self.region_y.value()

        # Calculate the relative coordinates
        rel_x = current_x - x
        rel_y = current_y - y

        # Update the form with relative coordinates
        self.region_x.setValue(rel_x)
        self.region_y.setValue(rel_y)

        # Show the refresh button since we have a parent anchor now
        self.refresh_parent_button.setVisible(True)

        # Show a message explaining the change
        self.results_area.setText(f"Parent anchor set at X={x}, Y={y}.\n\n"
                                f"Region coordinates are now relative to this anchor point.\n\n"
                                f"Original coordinates: X={current_x}, Y={current_y}\n"
                                f"Relative coordinates: X={rel_x}, Y={rel_y}")

    def refresh_parent(self):
        """Refresh the parent sensor position and update the parent anchor"""
        # Check if a parent sensor is specified
        parent_name = self.parent_edit.text().strip()
        if not parent_name:
            QMessageBox.warning(self, "No Parent Specified", "Please enter a parent sensor name first.")
            return

        # Get the SensorRunner instance from the parent window
        sensor_runner = self.window().sensor_runner
        if not sensor_runner:
            QMessageBox.warning(self, "Error", "Sensor runner not available.")
            return

        # Make sure the parent sensor exists
        if parent_name not in sensor_runner.sensors:
            QMessageBox.warning(self, "Error", f"Parent sensor '{parent_name}' not found."
                            "\nMake sure the parent sensor exists in the selected game.")
            return

        # Get the webcam instance to provide the current frame
        if not hasattr(self.window(), 'webcam_widget') or not hasattr(self.window().webcam_widget, 'webcam_canvas'):
            QMessageBox.warning(self, "Error", "Webcam not available.")
            return

        webcam_canvas = self.window().webcam_widget.webcam_canvas
        if not webcam_canvas.is_webcam_active:
            QMessageBox.warning(self, "Webcam Not Active", "Please start the webcam first.")
            return

        # Get the current webcam frame
        current_frame = webcam_canvas.current_frame
        if current_frame is None or not current_frame.any():
            QMessageBox.warning(self, "Error", "Failed to get current webcam frame.")
            return

        # Run the parent sensor on the current frame
        self.results_area.setText("Running parent sensor...")
        self.results_area.repaint()  # Force UI update

        success, result = sensor_runner.run_sensor(parent_name, substituteImage=current_frame)

        if not success or not result.get("found", False):
            self.results_area.setText(f"Failed to detect parent sensor '{parent_name}'.\n"
                                    f"Details: {result.get('error', 'Unknown error')}")
            return

        # Get the parent's position
        if "position" in result:
            parent_x = result["position"]["x"]
            parent_y = result["position"]["y"]
        elif "current_position" in result:
            parent_x = result["current_position"]["x"]
            parent_y = result["current_position"]["y"]
        else:
            self.results_area.setText(f"Parent sensor found but position data is missing.")
            return

        # Store old anchor point for logging
        old_x, old_y = self.parent_anchor_point if self.parent_anchor_point else (0, 0)

        # Update the parent anchor point
        self.parent_anchor_point = (parent_x, parent_y)

        # Update the webcam view with the new anchor point
        webcam_canvas.showParentAnchor(parent_x, parent_y)

        # The region coordinates are already relative to the parent, so we don't need to adjust them
        # We just need to update the displayed selection rectangle position
        rel_x = self.region_x.value()
        rel_y = self.region_y.value()
        width = self.region_width.value()
        height = self.region_height.value()

        # Update the selection rectangle on the webcam canvas
        # The rectangle's position is the absolute position (parent anchor + relative offset)
        abs_x = parent_x + rel_x
        abs_y = parent_y + rel_y
        self.region_updated.emit([abs_x, abs_y, width, height])

        # Show a message explaining the change
        self.results_area.setText(f"Parent sensor '{parent_name}' refreshed.\n\n"
                                f"New parent anchor position: X={parent_x}, Y={parent_y}\n"
                                f"Previous anchor position: X={old_x}, Y={old_y}\n\n"
                                f"The region coordinates remain unchanged: X={rel_x}, Y={rel_y}\n"
                                f"But the absolute position is now: X={abs_x}, Y={abs_y}")

    def test_sensor(self):
        """Test the current sensor configuration, preserving state for SamenessSensor"""
        # Get current form values
        name = self.name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "Validation Error", "Sensor name is required for testing")
            return
        sensor_type = self.type_combo.currentText()
        region = Rect(
            x=self.region_x.value(),
            y=self.region_y.value(),
            width=self.region_width.value(),
            height=self.region_height.value()
        )
        timeout = self.timeout_spin.value()
        parent = self.parent_edit.text().strip()
        parent_anchor_point = self.parent_anchor_point if parent else None

        sensor_to_test = None

        # Check if a sensor is loaded and its type matches the form
        if self.current_sensor and self.current_sensor.sensor_type == sensor_type:
            # Update the existing sensor instance with current form values
            sensor_to_test = self.current_sensor
            sensor_to_test.sensor_name = name # Update name
            sensor_to_test.region = region
            sensor_to_test.timeout = timeout
            sensor_to_test.parent = parent
            sensor_to_test.parent_anchor_point = parent_anchor_point

            # Update type-specific properties
            if sensor_type == "OpenCV":
                sensor_to_test.threshold = self.threshold_spin.value()
                sensor_to_test.vertical_priority = self.vertical_priority.isChecked()
                if self.template_image_data: # Update template if new one selected/captured
                    sensor_to_test.set_template_image(self.template_image_data)
            elif sensor_type == "Yolo":
                sensor_to_test.model_path = self.model_path_edit.text().strip()
                sensor_to_test.confidence_threshold = self.confidence_spin.value()
                sensor_to_test.image_size = self.image_size_spin.value()
                class_filter_text = self.class_filter_edit.text().strip()
                if class_filter_text:
                    try:
                        sensor_to_test.class_filter = [int(cls.strip()) for cls in class_filter_text.split(',') if cls.strip()]
                    except ValueError:
                        QMessageBox.warning(self, "Validation Error", "Class filter must be comma-separated integers")
                        return
                else:
                    sensor_to_test.class_filter = None
                sensor_to_test.device = self.device_combo.currentText()
            elif sensor_type == "OCR":
                sensor_to_test.search_text = self.search_text_edit.text().strip()
                sensor_to_test.confidence_threshold = self.ocr_confidence_spin.value()
                sensor_to_test.language = self.ocr_language_combo.currentText()
                sensor_to_test.scale_factor = self.scale_factor_spin.value()
                sensor_to_test.whitelist_chars = self.whitelist_edit.text().strip()
            elif sensor_type == "Sameness":
                sensor_to_test.confidence_threshold = self.sameness_confidence_spin.value()
                # DO NOT clear _previous_image_data_b64 here, we want to preserve it

        else:
            # Type changed or no sensor loaded, create a new one from the form
            sensor_to_test = self.create_sensor_from_form()
            if not sensor_to_test:
                # create_sensor_from_form shows its own error messages
                return
            # Store this newly created sensor as the current one for subsequent tests
            self.current_sensor = sensor_to_test
            # If it's a SamenessSensor, ensure its history in the runner is clear initially for this new test instance
            if isinstance(sensor_to_test, SamenessSensor):
                 runner = self.window().sensor_runner
                 if sensor_to_test.sensor_name in runner.sameness_history:
                     del runner.sameness_history[sensor_to_test.sensor_name]
                     print(f"Cleared sameness history for new test instance: {sensor_to_test.sensor_name}")


        # --- Validation Checks ---
        if sensor_to_test.sensor_type == "OpenCV" and not sensor_to_test.get_template_image():
            QMessageBox.warning(self, "Validation Error", "Template image is required to test OpenCV sensors")
            return
        if sensor_to_test.sensor_type == "Yolo":
            if not sensor_to_test.model_path or not os.path.exists(sensor_to_test.model_path):
                QMessageBox.warning(self, "Validation Error", "Valid model path is required to test YOLO sensors")
                return
            try:
                import ultralytics
            except ImportError:
                QMessageBox.warning(self, "Validation Error", "The ultralytics package is not installed. Please install it with 'pip install ultralytics'")
                return
        if sensor_to_test.sensor_type == "OCR":
            if not sensor_to_test.search_text.strip():
                QMessageBox.warning(self, "Validation Error", "Search text is required to test OCR sensors")
                return
            try:
                import pytesseract
            except ImportError:
                QMessageBox.warning(self, "Validation Error", "The pytesseract package is not installed. Please install it with 'pip install pytesseract'")
                return

        # --- Run Test ---
        runner = self.window().sensor_runner
        runner.add_sensor(sensor_to_test) # Use the potentially updated or newly created sensor

        try:
            # Get current frame from webcam if available and needed
            substitute_image = None
            # Sameness sensor also requires an image
            if sensor_to_test.sensor_type in ("OpenCV", "Yolo", "OCR", "Sameness"): # Use sensor_to_test
                if hasattr(self.window(), 'webcam_widget'):
                    substitute_image = self.window().webcam_widget.get_current_frame()
                    if substitute_image is None and sensor_to_test.sensor_type != "Location": # Use sensor_to_test
                        self.results_area.setText("Error: Webcam is not active or no frame available")
                        return

            # Run the sensor with webcam frame if available
            success, metadata = runner.run_sensor(sensor_to_test.sensor_name, substitute_image)


            # Display results
            result_text = f"Sensor Test Results:\n\n"
            result_text += f"Sensor: {sensor_to_test.sensor_name}\n"
            result_text += f"Type: {sensor_to_test.sensor_type}\n"
            result_text += f"Using Webcam: {substitute_image is not None}\n"
            result_text += f"Success: {success}\n\n"
            result_text += f"Metadata:\n{json.dumps(metadata, indent=2)}"

            self.results_area.setText(result_text)

            # Update webcam canvas with result rectangle if success
            if hasattr(self.window(), 'webcam_widget') and hasattr(self.window().webcam_widget, 'webcam_canvas'):
                webcam_canvas = self.window().webcam_widget.webcam_canvas
                if success and 'found' in metadata and metadata['found']:
                    # Show result rectangle using the match data (if applicable)
                    # Sameness sensor doesn't return position/dimensions for a match rectangle
                    if sensor_to_test.sensor_type in ("OpenCV", "Yolo", "OCR") and 'dimensions' in metadata and 'position' in metadata:
                        # Get match location from metadata
                        match_width = metadata['dimensions']['width']
                        match_height = metadata['dimensions']['height']
                        match_x = metadata['position']['x']
                        match_y = metadata['position']['y']

                        # Show the result rectangle on the webcam canvas
                        color = Qt.green
                        if sensor_to_test.sensor_type == "OCR":
                            # Use a different color for OCR matches for clarity
                            color = QColor(255, 165, 0)  # Orange

                        webcam_canvas.showResultRectangle(match_x, match_y, match_width, match_height, color)
                    # For Sameness sensor, maybe indicate success/failure differently?
                    # For now, just rely on the text output and don't draw a rectangle.
                else:
                    # Hide the result rectangle if sensor test failed
                    webcam_canvas.hideResultRectangle()

        except Exception as e:
            self.results_area.setText(f"Error testing sensor: {str(e)}")



    def save_sensor(self):
        """Save the sensor to a file"""
        sensor = self.create_sensor_from_form()
        if not sensor:
            return

        # Get the currently selected game from the sensor list widget
        parent_window = self.window()
        if not hasattr(parent_window, 'sensor_list'):
            QMessageBox.critical(self, "Error", "Cannot access game list")
            return

        game_name = parent_window.sensor_list.game_combo.currentText()
        if not game_name:
            QMessageBox.warning(self, "Error", "No game selected. Please select a game first.")
            return

        # Create path to the game's Sensors directory
        game_folder = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "Games",
            game_name
        )

        # Ensure Sensors directory exists
        sensors_dir = os.path.join(game_folder, "Sensors")
        os.makedirs(sensors_dir, exist_ok=True)

        # Save the sensor
        file_path = os.path.join(sensors_dir, f"{sensor.sensor_name}.json")

        try:
            sensor.save_to_file(file_path)
            # No confirmation message - just refresh the sensor list
            parent_window.sensor_list.refresh_sensors()
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to save sensor: {str(e)}"
            )

class SensorListWidget(QWidget):
    """Widget for listing and loading sensors"""

    sensor_selected = pyqtSignal(Sensor)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        # We'll use the shared SensorRunner from the parent window
        self.sensor_runner = None

    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QVBoxLayout(self)

        # Title for this section
        title = QLabel("Available Sensors")
        title.setStyleSheet("font-weight: bold; font-size: 12pt;")
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)

        # Game selection
        game_layout = QHBoxLayout()
        game_layout.addWidget(QLabel("Game:"))
        self.game_combo = QComboBox()
        self.populate_games()
        self.game_combo.currentTextChanged.connect(self.game_changed)
        game_layout.addWidget(self.game_combo)

        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.refresh_sensors)
        game_layout.addWidget(self.refresh_button)

        main_layout.addLayout(game_layout)

        # Sensor list
        self.sensor_list = QListWidget()
        self.sensor_list.itemDoubleClicked.connect(self.sensor_selected_handler)
        main_layout.addWidget(self.sensor_list)

        # Button layout for side-by-side buttons
        button_layout = QHBoxLayout()

        # Load selected button
        self.load_button = QPushButton("Load Selected")
        self.load_button.clicked.connect(self.load_selected_sensor)
        button_layout.addWidget(self.load_button)

        # Delete selected button
        self.delete_button = QPushButton("Delete Selected")
        self.delete_button.clicked.connect(self.delete_selected_sensor)
        button_layout.addWidget(self.delete_button)

        main_layout.addLayout(button_layout)

    def populate_games(self):
        """Populate the game dropdown with available games"""
        self.game_combo.clear()

        games_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "Games")
        if os.path.exists(games_dir):
            for game_folder in os.listdir(games_dir):
                game_path = os.path.join(games_dir, game_folder)
                if os.path.isdir(game_path) and os.path.exists(os.path.join(game_path, "Sensors")):
                    self.game_combo.addItem(game_folder)

    def game_changed(self, game_name):
        """Update the sensor list when game selection changes"""
        self.refresh_sensors()

    def refresh_sensors(self):
        """Refresh the list of available sensors and load them into the shared SensorRunner"""
        self.sensor_list.clear()

        # Make sure we have access to the parent window's shared SensorRunner
        if self.sensor_runner is None:
            self.sensor_runner = self.window().sensor_runner

        # Clear previously loaded sensors
        self.sensor_runner.sensors = {}

        game_name = self.game_combo.currentText()
        if not game_name:
            return

        sensors_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "Games",
            game_name,
            "Sensors"
        )

        if not os.path.exists(sensors_dir):
            return

        # First, load all sensors
        sensors = {}
        parent_child_map = {}

        for filename in os.listdir(sensors_dir):
            if filename.endswith('.json'):
                sensor_name = filename[:-5]  # Remove .json extension
                sensor_path = os.path.join(sensors_dir, filename)

                # Load the sensor and add it to the shared SensorRunner
                try:
                    # First load metadata for the UI
                    with open(sensor_path, 'r') as f:
                        sensor_data = json.load(f)
                    parent = sensor_data.get('parent', '')
                    sensors[sensor_name] = {'parent': parent, 'added': False}

                    # Add to parent-child map for UI hierarchy
                    if parent:
                        if parent not in parent_child_map:
                            parent_child_map[parent] = []
                        parent_child_map[parent].append(sensor_name)

                    # Now load the actual sensor object for testing
                    sensor = Sensor.load_from_file(sensor_path)
                    self.sensor_runner.add_sensor(sensor)

                except Exception as e:
                    print(f"Error loading sensor {filename}: {e}")

        # Add sensors to list with hierarchy
        self._add_sensors_to_list('', sensors, parent_child_map, 0)

    def _add_sensors_to_list(self, parent_name, sensors, parent_child_map, indent_level):
        """
        Recursively add sensors to the list with proper hierarchy.
        Args:
            parent_name: Name of the parent sensor ('' for top-level)
            sensors: Dict of all sensors with {'parent', 'added'} properties
            parent_child_map: Dict mapping parent sensor names to lists of their children
            indent_level: Current indentation level
        """
        # Get sensors with this parent (or top-level sensors if parent_name is empty)
        sensor_names = []
        for name, info in sensors.items():
            # Skip if already added to the list
            if info['added']:
                continue

            # Check if this sensor has the current parent
            if (not parent_name and not info['parent']) or (info['parent'] == parent_name):
                sensor_names.append(name)

        # Sort sensors alphabetically
        sensor_names.sort()

        # Add these sensors and their children
        for name in sensor_names:
            # Add the sensor with proper indentation
            indent = '  ' * indent_level
            item = QListWidgetItem(f"{indent}{name}")
            item.setData(Qt.UserRole, name)  # Store actual sensor name without indentation
            self.sensor_list.addItem(item)

            # Mark as added
            sensors[name]['added'] = True

            # Recursively add children
            if name in parent_child_map:
                self._add_sensors_to_list(name, sensors, parent_child_map, indent_level + 1)

    def load_selected_sensor(self):
        """Load the selected sensor and emit signal"""
        item = self.sensor_list.currentItem()
        if not item:
            QMessageBox.warning(self, "Error", "No sensor selected")
            return

        # Get the actual sensor name (without indentation)
        sensor_name = item.data(Qt.UserRole)
        self.load_sensor(sensor_name)

    def sensor_selected_handler(self, item):
        """Handle double-click on sensor item"""
        if item:
            # Get the actual sensor name (without indentation)
            sensor_name = item.data(Qt.UserRole)
            self.load_sensor(sensor_name)

    def load_sensor(self, sensor_name):
        """Load a sensor by name and emit signal"""
        game_name = self.game_combo.currentText()
        if not game_name:
            return

        file_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "Games",
            game_name,
            "Sensors",
            f"{sensor_name}.json"
        )

        try:
            sensor = Sensor.load_from_file(file_path)
            self.sensor_selected.emit(sensor)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load sensor: {str(e)}")

    def delete_selected_sensor(self):
        """Delete the selected sensor JSON file"""
        item = self.sensor_list.currentItem()
        if not item:
            QMessageBox.warning(self, "Error", "No sensor selected")
            return

        # Get the actual sensor name (without indentation)
        sensor_name = item.data(Qt.UserRole)
        game_name = self.game_combo.currentText()
        if not game_name:
            return

        file_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "Games",
            game_name,
            "Sensors",
            f"{sensor_name}.json"
        )

        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                # Refresh the sensor list
                self.game_changed(game_name)
            else:
                QMessageBox.warning(self, "Error", f"Sensor file not found: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to delete sensor: {str(e)}")


class BuilderBuddy(QMainWindow):
    """Main BuilderBuddy application window with unified interface"""

    def __init__(self):
        super().__init__()
        # Get the shared SensorRunner instance from GlobalState
        self.global_state = GlobalState() # Get global state instance
        self.sensor_runner = self.global_state._sensor_runner # Access the runner set in main.py
        # Ensure sensor_runner is available
        if self.sensor_runner is None:
            # This case happens when BuilderBuddy is run directly instead of through main.py
            logging.warning("SensorRunner not initialized in GlobalState before BuilderBuddy! Initializing now...")
            # Initialize it here as a fallback
            self.sensor_runner = SensorRunner()
            self.global_state.set_sensor_runner(self.sensor_runner)
            # Load default sensors if running as standalone
            if __name__ == "__main__":
                # Try to find and load sensors from a default location
                default_sensors_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "Games", "Default", "Sensors")
                if os.path.exists(default_sensors_dir):
                    self.sensor_runner.load_sensors(default_sensors_dir)
                    logging.info(f"Loaded sensors from {default_sensors_dir}")

        self.init_ui()
        self._setup_keyboard_listener()
        self.temp_sensor_path = os.path.join(os.path.dirname(__file__), 'temp_sensor.json')
        self._init_temp_sensor()

        # Refresh the sensor list on startup
        self.sensor_list.refresh_sensors()

    def init_ui(self):
        """Initialize the user interface with all components on a single screen"""
        self.setWindowTitle("Builder Buddy - Sensor Creator")
        self.setGeometry(100, 100, 1280, 800)  # Larger window to accommodate all components

        # Create central widget and main layout
        central_widget = QWidget()
        main_layout = QHBoxLayout(central_widget)

        # Create splitter for resizable panels
        self.splitter = QSplitter(Qt.Horizontal)

        # Left side - Webcam panel
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # Webcam widget occupies the entire left panel
        self.webcam_widget = WebcamWidget()
        self.webcam_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        left_layout.addWidget(self.webcam_widget)

        # Add left panel to splitter
        self.splitter.addWidget(left_panel)

        # Right side - Sensor Management panel
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Create a vertical splitter for the right panel
        right_splitter = QSplitter(Qt.Vertical)

        # Top section - Sensor List
        self.sensor_list = SensorListWidget()
        right_splitter.addWidget(self.sensor_list)

        # Bottom section - Sensor Creation
        self.sensor_creation = SensorCreationWidget()
        right_splitter.addWidget(self.sensor_creation)

        # Add right splitter to right layout
        right_layout.addWidget(right_splitter)

        # Add right panel to main splitter
        self.splitter.addWidget(right_panel)

        # Set splitter sizes (60% webcam, 40% controls)
        self.splitter.setSizes([600, 400])

        # Add splitter to main layout
        main_layout.addWidget(self.splitter)

        # Set central widget
        self.setCentralWidget(central_widget)

        # Connect signals
        self.sensor_list.sensor_selected.connect(self.sensor_creation.load_sensor_to_form)
        self.webcam_widget.regionSelected.connect(self.on_region_selected)
        self.webcam_widget.templateCaptured.connect(self.on_webcam_template_captured)
        self.sensor_creation.region_updated.connect(self.webcam_widget.webcam_canvas.setRegion)

        # Status bar
        self.statusBar().showMessage("Ready")

    def _setup_keyboard_listener(self):
        """Set up keyboard listener for hotkeys."""
        def on_press(key):
            try:
                # Check for the '5' key
                if hasattr(key, 'char') and key.char == '5':
                    print("Hotkey '5' pressed - closing BuilderBuddy")
                    self.close()
            except Exception as e:
                print(f"Error in keyboard listener: {str(e)}")

        # Start keyboard listener in a non-blocking thread
        self.keyboard_listener = keyboard.Listener(on_press=on_press)
        self.keyboard_listener.daemon = True
        self.keyboard_listener.start()
        print("Keyboard listener started - Press '5' to exit")

    def closeEvent(self, event):
        """Handle window close event"""
        # Stop keyboard listener
        if hasattr(self, 'keyboard_listener') and self.keyboard_listener.is_alive():
            self.keyboard_listener.stop()

        # Clean up temporary sensor file
        if os.path.exists(self.temp_sensor_path):
            try:
                os.remove(self.temp_sensor_path)
            except:
                pass

        # Accept the close event
        event.accept()

    def on_region_selected(self, rect):
        """Handle region selection from webcam"""
        # Get region values
        x = int(rect.x)
        y = int(rect.y)
        width = int(rect.width)
        height = int(rect.height)

        # If parent anchor mode is active, handle click as an anchor selection
        if self.sensor_creation.parent_anchor_mode:
            self.sensor_creation.set_parent_anchor(x, y)
            return

        # Update UI with coordinates (relative to parent anchor if set)
        if self.sensor_creation.parent_anchor_point and self.sensor_creation.parent_edit.text().strip():
            # Convert to coordinates relative to parent anchor
            rel_x = x - self.sensor_creation.parent_anchor_point[0]
            rel_y = y - self.sensor_creation.parent_anchor_point[1]
            self.sensor_creation.region_x.setValue(rel_x)
            self.sensor_creation.region_y.setValue(rel_y)
        else:
            # Use absolute coordinates
            self.sensor_creation.region_x.setValue(x)
            self.sensor_creation.region_y.setValue(y)

        self.sensor_creation.region_width.setValue(width)
        self.sensor_creation.region_height.setValue(height)

        # Show a status message with appropriate coordinate reference
        if self.sensor_creation.parent_anchor_point and self.sensor_creation.parent_edit.text().strip():
            rel_x = x - self.sensor_creation.parent_anchor_point[0]
            rel_y = y - self.sensor_creation.parent_anchor_point[1]
            self.statusBar().showMessage(f"Region selected: Absolute(X={x}, Y={y}), Relative to Parent(X={rel_x}, Y={rel_y}), W={width}, H={height}", 3000)
        else:
            self.statusBar().showMessage(f"Region selected: X={x}, Y={y}, W={width}, H={height}", 3000)

        # Update temp sensor with new region
        self._update_temp_sensor()

    def _init_temp_sensor(self):
        """Initialize or load temporary sensor"""
        if os.path.exists(self.temp_sensor_path):
            try:
                sensor = Sensor.load_from_file(self.temp_sensor_path)
                self.sensor_creation.load_sensor_to_form(sensor)
            except:
                self._create_default_temp_sensor()
        else:
            self._create_default_temp_sensor()

    def _create_default_temp_sensor(self):
        """Create a new default temporary sensor"""
        self.sensor_creation.name_edit.setText("TempSensor")
        self.sensor_creation.type_combo.setCurrentText("OpenCV")
        sensor = self.sensor_creation.create_sensor_from_form()
        if sensor:
            sensor.save_to_file(self.temp_sensor_path)

    def _update_temp_sensor(self):
        """Update temporary sensor with current form data"""
        sensor = self.sensor_creation.create_sensor_from_form()
        if sensor:
            # Only set template image on OpenCV sensors
            if sensor.sensor_type == "OpenCV" and self.sensor_creation.template_image_data:
                sensor.set_template_image(self.sensor_creation.template_image_data)
            sensor.save_to_file(self.temp_sensor_path)

    def update_template_preview(self, template_bytes):
        """Update the template preview and label"""
        if template_bytes:
            # Convert bytes to QImage
            image = QImage.fromData(template_bytes)
            if not image.isNull():
                # Scale image to fit preview, keeping aspect ratio
                pixmap = QPixmap.fromImage(image)
                scaled = pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.template_preview.setPixmap(scaled)
                return

        # Clear preview if no valid image
        self.template_preview.clear()

    def on_webcam_template_captured(self, template_bytes):
        """Handle template captured from webcam"""
        # Update template preview in sensor creation widget
        pixmap = QPixmap()
        pixmap.loadFromData(template_bytes)
        self.sensor_creation.template_preview.setPixmap(pixmap.scaled(200, 200, Qt.KeepAspectRatio))
        self.sensor_creation.template_image_data = template_bytes

        # Update template preview
        if template_bytes:
            # Convert bytes to QImage
            image = QImage.fromData(template_bytes)
            if not image.isNull():
                # Scale image to fit preview, keeping aspect ratio
                pixmap = QPixmap.fromImage(image)
                scaled = pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.sensor_creation.template_preview.setPixmap(scaled)
                # Template label text removed as requested

        # Set sensor type to OpenCV if not already set
        self.sensor_creation.type_combo.setCurrentText("OpenCV")

        # Update and save temporary sensor
        self._update_temp_sensor()

        # Show a status message
        self.statusBar().showMessage("Template image captured from webcam", 3000)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = BuilderBuddy()
    window.show()
    sys.exit(app.exec_())
