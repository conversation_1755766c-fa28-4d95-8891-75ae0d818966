import cv2
import numpy as np
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QMessageBox, QGroupBox, QFormLayout, QSizePolicy
)
from PyQt5.QtCore import pyqtSignal
import pyautogui
from BuilderBuddy.webcam_canvas import WebcamCanvas
from sensor import Rect

class WebcamWidget(QWidget):
    """Widget that provides webcam functionality for BuilderBuddy"""

    # Signals
    regionSelected = pyqtSignal(Rect)
    templateCaptured = pyqtSignal(bytes)

    def __init__(self, parent=None):
        super().__init__(parent)

        # Load camera device ID from config
        try:
            from config_manager import ConfigManager
            config_manager = ConfigManager()
            self.current_device_id = config_manager.get_camera_device_number()
        except Exception as e:
            print(f"Could not load camera ID from config, using default: {e}")
            self.current_device_id = 0

        self.current_frame = None  # Store the current frame
        # Initialize UI
        self.init_ui()
        # Start the webcam with the configured device ID
        self.start_webcam()

    def get_current_frame(self) -> np.ndarray:
        """Get the current frame from the webcam canvas"""
        if hasattr(self, 'webcam_canvas') and self.webcam_canvas.is_webcam_active:
            return self.webcam_canvas.current_frame
        return None

    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QVBoxLayout(self)

        # Webcam canvas (make it take up most of the space)
        self.webcam_canvas = WebcamCanvas(self)
        self.webcam_canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.webcam_canvas.setMinimumHeight(400)  # Ensure it takes enough vertical space

        # Connect signals
        self.webcam_canvas.regionChanged.connect(self.on_region_changed)

        main_layout.addWidget(self.webcam_canvas, 1)  # Add stretch factor to make it larger

        # Bottom controls
        bottom_layout = QHBoxLayout()

        # Camera controls
        controls_group = QGroupBox("Camera Controls")
        controls_layout = QFormLayout(controls_group)

        self.zoom_in_button = QPushButton("Zoom In")
        self.zoom_in_button.clicked.connect(self.webcam_canvas.zoomIn)
        controls_layout.addRow(self.zoom_in_button)

        self.zoom_out_button = QPushButton("Zoom Out")
        self.zoom_out_button.clicked.connect(self.webcam_canvas.zoomOut)
        controls_layout.addRow(self.zoom_out_button)

        self.reset_zoom_button = QPushButton("Reset Zoom")
        self.reset_zoom_button.clicked.connect(self.webcam_canvas.resetZoom)
        controls_layout.addRow(self.reset_zoom_button)

        # Add controls group to bottom layout
        bottom_layout.addWidget(controls_group)

        # Add bottom layout to main layout
        main_layout.addLayout(bottom_layout)

        # Instructions label removed as requested

    def start_webcam(self):
        """Start the webcam capture"""
        # Stop any existing webcam
        if self.webcam_canvas.is_webcam_active:
            self.webcam_canvas.stopWebcam()

        # Set the device ID and start
        self.webcam_canvas.device_id = self.current_device_id
        if not self.webcam_canvas.startWebcam():
            QMessageBox.warning(self, "Error", f"Failed to start camera {self.current_device_id}")

    def on_region_changed(self, region):
        """Handle region selection from webcam canvas"""
        # Keep track of valid region for template capture
        # Create a Rect object from the region
        rect = Rect(
            x=region[0],
            y=region[1],
            width=region[2],
            height=region[3]
        )

        if rect.width > 0 and rect.height > 0:
            # Emit signal with the region
            self.regionSelected.emit(rect)

    def capture_template(self):
        """Capture the selected region as a template image"""
        if not self.webcam_canvas.is_webcam_active:
            QMessageBox.warning(self, "Error", "Webcam is not active")
            return

        # Get the current frame from webcam
        if not self.webcam_canvas.capture or not self.webcam_canvas.capture.isOpened():
            QMessageBox.warning(self, "Error", "Webcam capture not available")
            return

        ret, frame = self.webcam_canvas.capture.read()
        if not ret:
            QMessageBox.warning(self, "Error", "Failed to grab frame from webcam")
            return

        # Get the current region from the region selector
        if not self.webcam_canvas.region_selector:
            QMessageBox.warning(self, "Error", "No region selected")
            return

        region = self.webcam_canvas.region_selector.getRegion()
        x, y, w, h = region

        screen_size = pyautogui.size()
        screen_size_actual = pyautogui.screenshot()
        scaleX = screen_size.width / screen_size_actual.width
        scaleY = screen_size.height / screen_size_actual.height
        x = int(x * scaleX)
        y = int(y * scaleY)
        w = int(w * scaleX)
        h = int(h * scaleY)

        # Ensure region is within frame bounds
        height, width, _ = frame.shape
        x = max(0, min(x, width-1))
        y = max(0, min(y, height-1))
        w = max(1, min(w, width-x))
        h = max(1, min(h, height-y))

        cropped = frame[y:y+h, x:x+w]
        cropped = cv2.resize(cropped, (int(w / scaleX), int(h / scaleY)), interpolation=cv2.INTER_LINEAR)

        # Convert to PNG bytes
        _, img_data = cv2.imencode('.png', cropped)
        template_bytes = img_data.tobytes()

        # Emit signal with the template data
        self.templateCaptured.emit(template_bytes)

    def stop_webcam(self):
        """Stop the webcam capture"""
        if hasattr(self, 'webcam_canvas') and self.webcam_canvas.is_webcam_active:
            self.webcam_canvas.stopWebcam()

    def closeEvent(self, event):
        """Clean up when the widget is closed"""
        self.stop_webcam()
        super().closeEvent(event)
