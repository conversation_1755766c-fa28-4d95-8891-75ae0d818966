from PyQt5.QtWidgets import QGraphicsRectItem
from PyQt5.QtGui import QPen, QBrush, QColor
from PyQt5.QtCore import Qt, QRectF, pyqtSignal, QObject

class RegionSelectorSignals(QObject):
    """Signals for the RegionSelector class"""
    regionChanged = pyqtSignal(list)  # [x, y, width, height]

class RegionSelector(QGraphicsRectItem):
    """
    Interactive region selector for WebcamCanvas.
    Allows user to select a rectangular region on the canvas.
    """
    def __init__(self, rect=QRectF(0, 0, 100, 100)):
        super().__init__(rect)
        
        # Set up appearance
        self.base_pen_width = 2.0  # Base pen width, will be adjusted based on zoom
        self.pen = QPen(Qt.red, self.base_pen_width, Qt.SolidLine)
        self.pen.setCosmetic(True)  # Keep pen width constant regardless of zoom
        self.setPen(self.pen)
        
        # Semi-transparent fill
        brush_color = QColor(Qt.red)
        brush_color.setAlpha(40)  # Adjust alpha for transparency (0-255)
        self.setBrush(QBrush(brush_color))
        
        # Make the item selectable and movable
        self.setFlags(
            QGraphicsRectItem.ItemIsSelectable |
            QGraphicsRectItem.ItemIsMovable |
            QGraphicsRectItem.ItemSendsGeometryChanges
        )
        
        # Create signals object
        self.signals = RegionSelectorSignals()
    
    def mouseMoveEvent(self, event):
        """Handle mouse move events for resizing"""
        super().mouseMoveEvent(event)
        # Emit region changed when resizing
        self.signals.regionChanged.emit(self.getRegion())
    
    def getRegion(self):
        """Get the current region as [x, y, width, height]"""
        pos = self.pos()
        rect = self.rect()
        return [int(pos.x()), int(pos.y()), int(rect.width()), int(rect.height())]
    
    def updatePenWidth(self, zoom_factor):
        """Update the pen width based on zoom factor"""
        adjusted_width = max(1.0, self.base_pen_width / max(0.1, zoom_factor))
        pen = self.pen
        pen.setWidthF(adjusted_width)
        self.setPen(pen)


class ResultRectangle(QGraphicsRectItem):
    """
    Rectangle to display results, such as template matching results.
    Not interactive, but visually shows where something was found.
    """
    def __init__(self, rect=QRectF(0, 0, 100, 100)):
        super().__init__(rect)
        
        # Set up appearance
        self.base_pen_width = 3.0  # Thicker than the region selector
        self.pen = QPen(Qt.green, self.base_pen_width, Qt.SolidLine)
        self.pen.setCosmetic(True)  # Keep pen width constant regardless of zoom
        self.setPen(self.pen)
        
        # Semi-transparent fill
        brush_color = QColor(Qt.green)
        brush_color.setAlpha(40)  # Adjust alpha for transparency (0-255)
        self.setBrush(QBrush(brush_color))
        
        # Make the item not selectable or movable
        self.setFlags(QGraphicsRectItem.ItemIsSelectable)
    
    def updatePenWidth(self, zoom_factor):
        """Update the pen width based on zoom factor"""
        adjusted_width = max(1.0, self.base_pen_width / max(0.1, zoom_factor))
        pen = self.pen
        pen.setWidthF(adjusted_width)
        self.setPen(pen)
