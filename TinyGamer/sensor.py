import json
import base64
import os
from dataclasses import dataclass, field, asdict
from typing import Optional, Dict, Any, Tuple, List, Union

@dataclass
class Rect:
    """Rectangle region for sensor operations."""
    x: int
    y: int
    width: int
    height: int
    
    @property
    def as_tuple(self) -> Tuple[int, int, int, int]:
        """Return the rectangle as a tuple (x, y, width, height)."""
        return (self.x, self.y, self.width, self.height)
    
    @property
    def as_opencv_tuple(self) -> Tuple[int, int, int, int]:
        """Return the rectangle as a tuple for OpenCV (x, y, x+width, y+height)."""
        return (self.x, self.y, self.x + self.width, self.y + self.height)

@dataclass
class Sensor:
    """
    Base sensor class that can be serialized/deserialized from JSON.
    Contains common properties for all sensor types.
    """
    sensor_name: str
    sensor_type: str
    region: Rect
    timeout: float = 5.0  # Default timeout value in seconds
    parent: str = ""  # Parent sensor name if this sensor depends on another
    parent_anchor_point: Optional[Tuple[int, int]] = None  # Anchor point for parent sensor
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert sensor to dictionary."""
        return asdict(self)
    
    def to_json(self) -> str:
        """Convert sensor to JSON string."""
        return json.dumps(self.to_dict())
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Sensor':
        """Create sensor from dictionary."""
        # Convert region dict to Rect object
        if 'region' in data and isinstance(data['region'], dict):
            data['region'] = Rect(**data['region'])
        
        # Use the sensor_type to determine which class to instantiate
        sensor_type = data.get('sensor_type', '')
        if sensor_type == 'OpenCV':
            return OpenCVSensor(**data)
        elif sensor_type == 'Location':
            return LocationSensor(**data)
        elif sensor_type == 'Yolo':
            return YoloSensor(**data)
        elif sensor_type == 'OCR':
            return OCRSensor(**data)
        elif sensor_type == 'Sameness':
            return SamenessSensor(**data)
        else:
            # Fallback for unknown types or base Sensor
            # Check if it's intended to be a base Sensor instance
            if sensor_type == 'Sensor' or not sensor_type:
                 return cls(**data)
            else:
                 # Or raise an error for unknown specific types
                 raise ValueError(f"Unknown sensor type: {sensor_type}")
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Sensor':
        """Create sensor from JSON string."""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    @classmethod
    def load_from_file(cls, file_path: str) -> 'Sensor':
        """Load sensor from file."""
        with open(file_path, 'r') as f:
            return cls.from_json(f.read())
    
    def save_to_file(self, file_path: str) -> None:
        """Save sensor to file."""
        with open(file_path, 'w') as f:
            f.write(self.to_json())

@dataclass
class OpenCVSensor(Sensor):
    """
    OpenCV-specific sensor for image processing tasks.
    """
    base64_encoded_image_data: Optional[str] = None
    threshold: float = 0.8
    vertical_priority: bool = False
    
    def __post_init__(self):
        """Ensure sensor_type is set correctly."""
        self.sensor_type = 'OpenCV'
    
    def set_template_image(self, image_data: bytes) -> None:
        """Set the template image from raw bytes."""
        self.base64_encoded_image_data = base64.b64encode(image_data).decode('utf-8')
    
    def get_template_image(self) -> Optional[bytes]:
        """Get the template image as raw bytes."""
        if self.base64_encoded_image_data:
            return base64.b64decode(self.base64_encoded_image_data)
        return None

@dataclass
class LocationSensor(Sensor):
    """
    Sensor for tracking location-based data.
    Uses the standard region field for location tracking.
    """
    
    def __post_init__(self):
        """Ensure sensor_type is set correctly."""
        self.sensor_type = 'Location'

@dataclass
class YoloSensor(Sensor):
    """
    YOLO-based sensor for object detection tasks using YOLOv8 models.
    """
    model_path: str = ""  # Path to the YOLO model file
    confidence_threshold: float = 0.25  # Minimum confidence threshold for detections
    image_size: int = 640  # Image size for YOLO model input
    class_filter: Optional[List[int]] = None  # Optional list of class IDs to filter detections
    device: str = "0"  # Device to run inference on ("0" for GPU, "cpu" for CPU)
    
    def __post_init__(self):
        """Ensure sensor_type is set correctly."""
        self.sensor_type = 'Yolo'

@dataclass
class OCRSensor(Sensor):
    """
    OCR-based sensor for text recognition using Tesseract OCR.
    """
    search_text: str = ""  # Text to search for in the image
    confidence_threshold: float = 0.7  # Minimum confidence threshold for text detection
    language: str = "eng"  # Language for OCR (e.g., 'eng', 'fra', etc.)
    scale_factor: float = 1.0  # Scale factor for image preprocessing (can improve OCR accuracy)
    whitelist_chars: str = ""  # Optional whitelist of characters to recognize
    
    def __post_init__(self):
        """Ensure sensor_type is set correctly."""
        self.sensor_type = 'OCR'


@dataclass
class SamenessSensor(Sensor):
    """
    Sensor that compares the current image region to the previous one.
    Returns True if the images are the same (within threshold) or if it's the first run.
    """
    confidence_threshold: float = 0.90  # Lowered default threshold for sameness
    # Runtime state (previous image) is now managed by SensorRunner

    def __post_init__(self):
        """Ensure sensor_type is set correctly."""
        self.sensor_type = 'Sameness'
