import os
import time
import uuid
import logging
from typing import Dict, Any, List, Optional, Tuple, Callable, Set
import traceback

from global_state import GlobalState
from game_interactor import GameInteractor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class CancelException(Exception):
    """Exception raised when a task is canceled"""
    pass

def check_canceled(global_state):
    """Check if a task is canceled and raise CancelException if it is"""
    if global_state.get_flag("canceled", False):
        raise CancelException("Task was canceled")

def check_paused(global_state):
    """Wait while a task is paused"""
    while global_state.get_flag("paused", False):
        time.sleep(0.1)

class GlobalStateWrapper:
    """Wrapper for GlobalState that checks for cancellation on every operation"""
    def __init__(self, global_state):
        self._state = global_state

    def get(self, key, default=None):
        """Get data from global state, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        return self._state.get(key, default)

    def get_flag(self, key, default=None):
        """Get flag from global state, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        return self._state.get_flag(key, default)

    def set(self, key, value):
        """Set data in global state, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._state.set(key, value)
        
    def wait_for_data(self, key, timeout=None):
        """Wait for data, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        return self._state.wait_for_data(key, timeout)
        
    def register_task_waiting(self, task_id, sensor_keys):
        """Register task waiting for data, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._state.register_task_waiting(task_id, sensor_keys)

class InteractorWrapper:
    """Wrapper for GameInteractor that checks for cancellation on every operation"""
    def __init__(self, interactor, global_state):
        self._interactor = interactor
        self._state = global_state

    def EveSafeClick(self, x, y, button=None):
        """Perform a safe click for Eve Online, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        kwargs = {"x": x, "y": y}
        if button is not None:
            kwargs["button"] = button
        self._interactor.EveSafeClick(**kwargs)

    def EveSafeClickRight(self, x, y, button=None):
        """Perform a safe right click for Eve Online, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        kwargs = {"x": x, "y": y}
        if button is not None:
            kwargs["button"] = button
        self._interactor.EveSafeClickRight(**kwargs)

    def EvePressKeyWithModifier(self, modifier, key):
        """Press and release a keyboard key with a modifier for Eve Online, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._interactor.EvePressKeyWithModifier(modifier, key)

    def EvePressKey(self, key):
        """Press and release a keyboard key for Eve Online, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._interactor.EvePressKey(key)
        
    def move_to(self, x, y):
        """Move the mouse, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._interactor.move_to(x, y)

    def click_up(self, button=None):
        """Click the specified mouse button up, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        kwargs = {}
        if button is not None:
            kwargs["button"] = button
        self._interactor.click_up(**kwargs)
        
    def click_down(self, button=None):
        """Click the specified mouse button down, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        kwargs = {}
        if button is not None:
            kwargs["button"] = button
        self._interactor.click_down(**kwargs)
        
    def click(self, button=None):
        """Click the mouse, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        kwargs = {}
        if button is not None:
            kwargs["button"] = button
        self._interactor.click(**kwargs)

    def click_right(self, button=None):
        """Click the right mouse button, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._interactor.click_right()
        
    def type_string(self, text):
        """Type a string, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._interactor.type_string(text)
        
    def press_key(self, key):
        """Press a key, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._interactor.press_key(key)
        
    def hold_key(self, key):
        """Hold down a key, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._interactor.hold_key(key)
        
    def release_key(self, key):
        """Release a held key, checking for cancellation first"""
        check_paused(self._state)
        check_canceled(self._state)
        self._interactor.release_key(key)

class Task:
    """
    A modular unit of logic that can be canceled via exceptions.
    Tasks can access global state and run other tasks.
    """
    def __init__(self, name: str, game_interactor: Optional[GameInteractor] = None):
        self.name = name
        self.id = f"{name}_{uuid.uuid4().hex[:8]}"
        self.logger = logging.getLogger(f"Task[{self.name}]")
        self._raw_global_state = GlobalState()
        self.global_state = GlobalStateWrapper(self._raw_global_state)
        self._raw_interactor = game_interactor if game_interactor else GameInteractor()
        self.interactor = InteractorWrapper(self._raw_interactor, self._raw_global_state)
        self.is_running = False
        self.is_canceled = False
        self.parent_task: Optional[str] = None
    
    def execute(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Main execution method to be overridden by subclasses.
        Returns (success, metadata) tuple.
        """
        self.logger.info(f"Task {self.name} executed")
        # Default implementation does nothing
        return True, {"message": "Task executed successfully"}
    
    def start(self, parent_task: Optional[str] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Start the task and execute it directly.
        If parent_task is provided, this is considered a subtask.
        Returns the result of the task execution.
        """
        if self.is_running:
            self.logger.warning(f"Task {self.name} is already running")
            return False, {"error": "Task is already running"}
            
        self.parent_task = parent_task
        self.is_running = True
        self.is_canceled = False
        
        try:
            self.logger.info(f"Task {self.name} started")
            # Run the task directly
            result = self.run()
            self.is_running = False
            return result
        except CancelException as e:
            self.logger.info(f"Task {self.name} was canceled: {str(e)}")
            self.is_running = False
            self.is_canceled = True
            return False, {"error": str(e)}
        except Exception as e:
            logging.exception(f"Exception in Task.start for {self.name}:")
            self.logger.error(f"Task {self.name} failed with exception: {str(e)}")
            self.logger.error(traceback.format_exc())
            self.is_running = False
            return False, {"error": str(e), "traceback": traceback.format_exc()}
    
    def wait(self, timeout: float = 5.0) -> bool:
        """
        Wait for duration, checking for cancellation.
        
        """
        check_canceled(self._raw_global_state)
        time.sleep(timeout)
        return True
    
    def cancel(self) -> None:
        """Mark the task as canceled. Execution will stop at the next cancellation check."""
        if not self.is_running:
            return
            
        self.logger.info(f"Canceling task {self.name}")
        # Use set method for canceled flag as we're writing data, not reading
        self._raw_global_state.set("canceled", True)
        self.is_canceled = True
    
    def run(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Run the task directly.
        Returns (success, metadata) tuple.
        """
        # Mark as not canceled before executing
        # Use set method for canceled flag as we're writing data, not reading
        self._raw_global_state.set("canceled", False)
        return self.execute()
    
    def run_subtask(self, task: 'Task') -> Tuple[bool, Dict[str, Any]]:
        """
        Run another task as a subtask of this task.
        Returns the result of the subtask.
        """
        self.logger.info(f"Running subtask {task.name}")
        
        # Make sure subtask is using the same game interactor
        task._raw_interactor = self._raw_interactor
        task.interactor = InteractorWrapper(task._raw_interactor, task._raw_global_state)
        
        # Register this as the parent task and run the task directly
        return task.start(parent_task=self.id)
    
    def get_sensor_data(self, sensor_name: str, default=None) -> Any:
        """Get sensor data from global state."""
        return self.global_state.get(sensor_name, default)
    
    def is_sensor_found(self, sensor_name: str) -> bool:
        """
        Check if a sensor is found in the global state.
        Returns True if the sensor data exists and has found=True, False otherwise.
        """
        sensor_data = self.global_state.get(sensor_name)
        return sensor_data is not None and sensor_data.get("found", False)
    
    def create_subtask(self, task_class: type, **kwargs) -> 'Task':
        """
        Create a subtask instance with the current game interactor.
        Returns a new instance of the specified task class.
        """
        return task_class(game_interactor=self._raw_interactor, **kwargs)
    
    def run_subtask_with_error_handling(self, task: 'Task', error_message: str) -> bool:
        """
        Run a subtask and handle errors.
        Returns True if the subtask was successful, False otherwise.
        """
        success, _ = self.run_subtask(task)
        if not success:
            self.logger.error(error_message)
        return success


class TaskRegistry:
    """
    Registry for storing and retrieving task classes by name.
    """
    _tasks: Dict[str, type] = {}
    
    @classmethod
    def register(cls, task_class: type) -> type:
        """Register a task class."""
        cls._tasks[task_class.__name__] = task_class
        return task_class
    
    @classmethod
    def get_task_class(cls, task_name: str) -> Optional[type]:
        """Get a task class by name."""
        return cls._tasks.get(task_name)
    
    @classmethod
    def get_all_task_classes(cls) -> Dict[str, type]:
        """Get all registered task classes."""
        return cls._tasks.copy()
    
    @classmethod
    def create_task(cls, task_name: str, *args, **kwargs) -> Optional[Task]:
        """Create a new task instance by name."""
        task_class = cls.get_task_class(task_name)
        if task_class:
            return task_class(*args, **kwargs)
        return None


# Example of how to register and create a task
# @TaskRegistry.register
# class ExampleTask(Task):
#     def __init__(self, name="ExampleTask"):
#         super().__init__(name)
#     
#     def execute(self):
#         self.logger.info("Executing example task")
#         return True, {"message": "Example task executed"}
