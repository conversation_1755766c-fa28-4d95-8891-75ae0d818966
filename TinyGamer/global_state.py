import json
import threading
import time
from typing import Dict, Any

class GlobalState:
    """
    A singleton class that holds all data returned from sensors.
    Thread-safe implementation to allow access from multiple processes.
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(GlobalState, cls).__new__(cls)
                cls._instance._initialize()
            return cls._instance
    
    def _initialize(self):
        """Initialize the GlobalState with empty data."""
        self._data_lock = threading.Lock()
        self._data = {}
        self._waiting_tasks = {}
        self._data_events = {}
        self._sensor_runner = None  # Will be set by first caller
        self._timestamps = {}  # Store sensor update timestamps
        self._webcam_grabber = None  # Headless webcam grabber for live operation
    
    def set_sensor_runner(self, sensor_runner) -> None:
        """Set the SensorRunner instance for auto-running sensors."""
        with self._data_lock:
            self._sensor_runner = sensor_runner

    def set_webcam_grabber(self, webcam_grabber) -> None:
        """Set the headless webcam grabber for live sensor runs."""
        with self._data_lock:
            self._webcam_grabber = webcam_grabber
    
    def get(self, key: str, default=None) -> Any:
        """
        Get data by key with optional default value.
        If the data exists but has timed out, automatically run the sensor.
        """
        with self._data_lock:
            # First check if we have valid data
            if key in self._data:
                data = self._data.get(key)
                
                # If data is a dict and has a 'timeout' field, check against timestamp
                if isinstance(data, dict) and 'timeout' in data:
                    current_time = time.time()
                    last_update = self._timestamps.get(key, 0)
                    
                    # If data is still fresh, return it
                    if not ((current_time - last_update > 0.1) and self._sensor_runner is not None):
                        return data
            
            # If we get here, we either have no data or it's expired and needs to be refreshed
            # Only attempt to run the sensor if we have a sensor runner
            if self._sensor_runner is not None:
                # Release lock during sensor run to avoid deadlock
                self._data_lock.release()
                try:
                    # Always pass the latest webcam frame if available
                    substitute_image = None
                    if self._webcam_grabber is not None:
                        substitute_image = self._webcam_grabber.get_latest_frame()
                    _, new_data = self._sensor_runner.run_sensor(key, substituteImage=substitute_image)
                    return new_data
                finally:
                    # Reacquire lock before returning
                    self._data_lock.acquire()
            
            # If we have no sensor runner or other issues, return the default
            return default
    
    def get_flag(self, key: str, default=None) -> Any:
        """
        Get data by key without triggering sensor runs.
        This is intended for internal flags like 'canceled' that shouldn't run sensors.
        """
        with self._data_lock:
            return self._data.get(key, default)
            
    def set(self, key: str, value: Any) -> None:
        """Set data by key, update timestamp, and notify any waiting tasks."""
        with self._data_lock:
            self._data[key] = value
            self._timestamps[key] = time.time()
            
            # Notify any tasks waiting for this key
            if key in self._data_events:
                self._data_events[key].set()
    
    def to_json(self) -> str:
        """Convert state to JSON string for logging/debugging."""
        with self._data_lock:
            return json.dumps(self._data, default=str)
