#!/usr/bin/env python3
import os
import sys
import argparse
import logging
import multiprocessing

from director import Director
from eve_director import EveDirector
from task import Task, TaskRegistry
from global_state import GlobalState
from sensor_runner import Sensor<PERSON>unner
from webcam_frame_grabber import WebcamFrameGrabber
from config_manager import Confi<PERSON><PERSON><PERSON><PERSON>

def run_director(use_eve=False, no_preview=False, no_status=False):
    """Run the main TinyGamer Director application."""
    multiprocessing.freeze_support()

    # Set up file logging if no_status is enabled
    if no_status:
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, 'tinygamer.log')
        file_handler = logging.FileHandler(log_file, mode='w')
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logging.getLogger().addHandler(file_handler)
        logging.info(f"Running in headless mode, logging to {log_file}")

    director = EveDirector(no_preview=no_preview, no_status=no_status) if use_eve else Director(no_status=no_status)
    try:
        exit_code = director.run()
    except Exception as e:
        logging.error(f"Unhandled exception: {str(e)}")
        exit_code = 1
    finally:
        director.shutdown()

    return exit_code

def run_builder_buddy():
    """Run the BuilderBuddy sensor creation application."""
    # Ensure GlobalState and SensorRunner are initialized
    gs = GlobalState()
    sensor_runner = SensorRunner()
    gs.set_sensor_runner(sensor_runner)

    # Load camera device ID from config
    config_manager = ConfigManager()
    camera_device_id = config_manager.get_camera_device_number()
    logging.info(f"Using camera device ID from settings.config: {camera_device_id}")

    # Initialize and set WebcamFrameGrabber for GlobalState internal sensor runs
    webcam_grabber = WebcamFrameGrabber(device_id=camera_device_id)
    gs.set_webcam_grabber(webcam_grabber)
    webcam_grabber.start() # Start the grabber thread
    logging.info(f"WebcamFrameGrabber initialized for GlobalState in Builder mode with camera ID: {camera_device_id}")

    # Load sensors from all game directories
    games_dir = os.path.join(os.path.dirname(__file__), 'Games')
    if os.path.exists(games_dir):
        for game_folder in os.listdir(games_dir):
            game_path = os.path.join(games_dir, game_folder)
            if os.path.isdir(game_path):
                sensors_path = os.path.join(game_path, 'Sensors')
                if os.path.exists(sensors_path):
                    sensor_runner.load_sensors(sensors_path)
                    logging.info(f"Loaded sensors from: {sensors_path}")

    # Import here to avoid circular imports
    from BuilderBuddy.builder_buddy import BuilderBuddy
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    window = BuilderBuddy()
    window.show()
    exit_code = app.exec_()

    # Stop the webcam grabber when the app closes
    if webcam_grabber:
        webcam_grabber.stop()
        logging.info("WebcamFrameGrabber stopped.")

    return exit_code

def main():
    """Main entry point for the application."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='TinyGamer - Game Automation Tool')
    parser.add_argument('--builder', action='store_true', help='Run BuilderBuddy instead of Director')
    parser.add_argument('--eve', action='store_true', help='Run with EveDirector for Eve Online specific features')
    parser.add_argument('--no-preview', action='store_true', help='Skip camera preview dialog and go straight to main loop')
    parser.add_argument('--no-status', action='store_true', help='Run without status window and log to file instead')
    args = parser.parse_args()

    if args.builder:
        return run_builder_buddy()
    else:
        return run_director(use_eve=args.eve, no_preview=args.no_preview, no_status=args.no_status)

if __name__ == "__main__":
    sys.exit(main())
