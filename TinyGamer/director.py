import os
import sys
import time
import logging
import threading
import multiprocessing
from typing import Dict, List, Any, Optional, Tuple, Set
from queue import Queue, Empty
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QListWidget, QListWidgetItem,
    QCheckBox
)
from PyQt5.QtCore import QTimer, Qt, pyqtSignal, QObject, QThread
from pynput import keyboard

from global_state import GlobalState
from task import Task, TaskRegistry
from sensor_runner import SensorRunner
from webcam_frame_grabber import WebcamFrameGrabber
from game_interactor import GameInteractor
from config_manager import ConfigManager

# Configure logging to include timestamp
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class LogHandler(logging.Handler):
    """Custom log handler that emits signals with log records."""
    def __init__(self, signal_emitter):
        super().__init__()
        self.signal_emitter = signal_emitter

    def emit(self, record):
        try:
            msg = self.format(record)
            self.signal_emitter.log_received.emit(msg)
        except Exception:
            self.handleError(record)

class SignalEmitter(QObject):
    """Class to emit signals from any thread to the Qt interface."""
    log_received = pyqtSignal(str)
    status_changed = pyqtSignal(str, str)  # task_name, status

class Director:
    """
    Root application-level class that manages threads and windows.
    Handles task execution, global state, and UI interaction.
    """
    def __init__(self, camera_device_id=None, init_webcam=True, no_status=False):
        self.app = QApplication(sys.argv)
        self.global_state = GlobalState()
        self.game_interactor = GameInteractor()
        self.no_status = no_status

        # Load camera device ID from config if not provided
        if camera_device_id is None:
            config_manager = ConfigManager()
            camera_device_id = config_manager.get_camera_device_number()
            self.logger = logging.getLogger("Director")
            self.logger.info(f"Loaded camera device ID from config: {camera_device_id}")

        # Check if GlobalState already has a SensorRunner
        if self.global_state._sensor_runner is None:
            # Create a new SensorRunner and register it with GlobalState
            self.sensor_runner = SensorRunner()
            self.global_state.set_sensor_runner(self.sensor_runner)
            logging.info("Created new SensorRunner and registered with GlobalState")
        else:
            # Use the existing SensorRunner from GlobalState
            self.sensor_runner = self.global_state._sensor_runner
            logging.info("Using existing SensorRunner from GlobalState")

        self.active_task: Optional[Task] = None
        self.task_queue: Queue = Queue()
        self.is_paused = False
        self.shutting_down = False
        self.webcam_grabber = None

        # Set up signal emitter for cross-thread signals
        self.signal_emitter = SignalEmitter()

        # Set up logging
        if not hasattr(self, 'logger'):
            self.logger = logging.getLogger("Director")

        # Only add the log handler if we're showing the status window
        if not self.no_status:
            self.log_handler = LogHandler(self.signal_emitter)
            self.log_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            logging.getLogger().addHandler(self.log_handler)

        # Initialize webcam frame grabber if requested
        if init_webcam:
            self.init_webcam_grabber(device_id=camera_device_id)

        # Load tasks and sensors
        self._load_tasks()
        self._load_sensors()

        # Create the UI if not in headless mode
        if not self.no_status:
            self._create_ui()
        else:
            # Create a minimal QMainWindow to keep the QApplication running
            self.main_window = QMainWindow()
            self.main_window.resize(1, 1)  # Minimal size
            self.main_window.setWindowTitle("TinyGamer (Headless)")
            self.logger.info("Running in headless mode (no status window)")

        # Start the task execution thread
        self.task_thread = threading.Thread(target=self._task_executor, daemon=True)
        self.task_thread.start()

        # Set up keyboard listener for hotkeys
        self._setup_keyboard_listener()

        self.logger.info("Director initialized")

    def _create_ui(self):
        """Create the main UI window."""
        self.main_window = QMainWindow()
        self.main_window.setWindowTitle("TinyGamer")
        self.main_window.resize(600, 400)

        # Create central widget and layout
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)

        # Status section
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("Status:"))
        self.status_label = QLabel("Idle")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        self.pause_button = QPushButton("Pause")
        self.pause_button.clicked.connect(self.toggle_pause)
        status_layout.addWidget(self.pause_button)

        # Always on top checkbox
        self.always_on_top_checkbox = QCheckBox("Always on Top")
        self.always_on_top_checkbox.stateChanged.connect(self.toggle_always_on_top)
        status_layout.addWidget(self.always_on_top_checkbox)

        main_layout.addLayout(status_layout)

        # Log view
        main_layout.addWidget(QLabel("Log Output:"))
        self.log_view = QTextEdit()
        self.log_view.setReadOnly(True)
        main_layout.addWidget(self.log_view)

        # Set central widget
        self.main_window.setCentralWidget(central_widget)

        # Connect signals
        self.signal_emitter.log_received.connect(self.update_log)
        self.signal_emitter.status_changed.connect(self.update_status)

        # Timer for UI updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(100)  # Update every 100ms

    def update_log(self, message: str):
        """Update the log view with a new message."""
        if not self.no_status and hasattr(self, 'log_view'):
            self.log_view.append(message)
            # Auto-scroll to bottom
            scrollbar = self.log_view.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def update_status(self, task_name: str, status: str):
        """Update the status display."""
        if not self.no_status and hasattr(self, 'status_label'):
            if task_name:
                self.status_label.setText(f"{task_name}: {status}")
            else:
                self.status_label.setText(status)

    def update_ui(self):
        """Periodic UI update."""
        # Skip UI updates in headless mode
        if self.no_status:
            return

        # Update UI elements based on current state
        if self.is_paused:
            self.pause_button.setText("Resume")
            if self.active_task:
                self.status_label.setText(f"{self.active_task.name}: PAUSED")
        else:
            self.pause_button.setText("Pause")

    def toggle_pause(self):
        """Toggle the paused state of task execution."""
        self.is_paused = not self.is_paused
        self.global_state.set('paused', self.is_paused)
        if self.is_paused:
            self.logger.info("Task execution paused")
        else:
            self.logger.info("Task execution resumed")

    def toggle_always_on_top(self, state):
        """Toggle the 'always on top' window flag."""
        is_checked = (state == Qt.Checked)
        self.main_window.setWindowFlag(Qt.WindowStaysOnTopHint, is_checked)
        # Re-show the window to apply the flag change
        self.main_window.show()

    def run_selected_task(self):
        """Run the task selected in the dropdown."""
        # This method is kept for compatibility but no longer has UI components
        self.logger.warning("run_selected_task called but task selection UI has been removed")
        return False, {"error": "Task selection UI has been removed"}

    def run_task(self, task_name: str):
        """
        Run a specific task by name.
        Returns (success, metadata) tuple.
        """
        task = TaskRegistry.create_task(task_name, game_interactor=self.game_interactor)
        if task:
            self.logger.info(f"Queuing task: {task_name}")
            self.task_queue.put(task)
            return True, {"message": f"Task {task_name} queued successfully"}
        else:
            self.logger.error(f"Failed to create task: {task_name}")
            return False, {"error": f"Failed to create task: {task_name}"}

    def _load_tasks(self):
        """Load all task modules."""
        self.logger.info("Loading tasks...")

        # Load task directories - can be overridden in subclasses
        task_dirs = self._get_task_directories()

        # Load tasks from each directory
        for tasks_path in task_dirs:
            if os.path.exists(tasks_path):
                self._load_tasks_from_directory(tasks_path)

    def _get_task_directories(self):
        """Get a list of directories containing task modules.
        Can be overridden by subclasses to load tasks from different locations.
        """
        task_dirs = []

        # Load custom tasks from Games directory
        games_dir = os.path.join(os.path.dirname(__file__), 'Games')
        if os.path.exists(games_dir):
            for game_folder in os.listdir(games_dir):
                game_path = os.path.join(games_dir, game_folder)
                if os.path.isdir(game_path):
                    tasks_path = os.path.join(game_path, 'Tasks')
                    if os.path.exists(tasks_path):
                        task_dirs.append(tasks_path)

        return task_dirs

    def _load_tasks_from_directory(self, directory: str):
        """Load task modules from a specific directory."""
        self.logger.info(f"Loading tasks from {directory}")
        sys.path.append(directory)

        for filename in os.listdir(directory):
            if filename.endswith('.py'):
                module_name = filename[:-3]  # Remove .py extension
                try:
                    __import__(module_name)
                    self.logger.info(f"Loaded task module: {module_name}")
                except Exception as e:
                    self.logger.error(f"Error loading task module {module_name}: {str(e)}")

    def _load_sensors(self):
        """Load all sensor configurations."""
        self.logger.info("Loading sensors...")

        # Load sensors from Games directory
        games_dir = os.path.join(os.path.dirname(__file__), 'Games')
        if os.path.exists(games_dir):
            for game_folder in os.listdir(games_dir):
                game_path = os.path.join(games_dir, game_folder)
                if os.path.isdir(game_path):
                    sensors_path = os.path.join(game_path, 'Sensors')
                    if os.path.exists(sensors_path):
                        self.sensor_runner.load_sensors(sensors_path)

    def _task_executor(self):
        """Thread that executes tasks from the queue."""
        self.logger.info("Task executor thread started")

        while not self.shutting_down:
            # If paused, wait and check again
            if self.is_paused:
                time.sleep(0.1)
                continue

            # Check if there's a task to execute
            try:
                if not self.active_task:
                    # Get a new task from the queue
                    task = self.task_queue.get(block=False)
                    self.active_task = task
                    self.logger.info(f"Starting task: {task.name}")
                    self.signal_emitter.status_changed.emit(task.name, "RUNNING")

                    # Start the task (now returns result directly)
                    success, metadata = task.start()

                    if success:
                        self.logger.info(f"Task {task.name} completed successfully: {metadata}")
                        self.signal_emitter.status_changed.emit(task.name, "SUCCESS")
                    else:
                        self.logger.warning(f"Task {task.name} failed: {metadata}")
                        self.signal_emitter.status_changed.emit(task.name, "FAILED")

                    # Clear the active task
                    self.active_task = None

            except Empty:
                # No tasks in the queue
                if not self.active_task:
                    self.signal_emitter.status_changed.emit("", "Idle")

            except Exception as e:
                self.logger.error(f"Error in task executor: {str(e)}")
                self.signal_emitter.status_changed.emit("", f"ERROR: {str(e)}")
                self.active_task = None

            # Brief sleep to prevent CPU thrashing
            time.sleep(0.01)

    def run(self):
        """Start the application main loop."""
        if not self.no_status:
            self.main_window.show()
        else:
            # In headless mode, we still need a minimal window to keep the QApplication running
            # but we can hide it
            self.main_window.hide()
        return self.app.exec_()

    def _setup_keyboard_listener(self):
        """Set up keyboard listener for hotkeys."""
        # Start keyboard listener in a non-blocking thread
        self.keyboard_listener = keyboard.Listener(on_press=self._on_key_press)
        self.keyboard_listener.daemon = True
        self.keyboard_listener.start()
        self.logger.info("Keyboard listener started - Press 'End' to exit and 'Home' to pause/resume")

    def _on_key_press(self, key):
        """Handle key press events for hotkeys.
        Can be overridden by subclasses to add more hotkeys.
        """
        try:
            # Check for the 'End' key (previously '5')
            if key == keyboard.Key.end:
                self.logger.info("Hotkey 'End' pressed - initiating clean shutdown")
                # Trigger clean shutdown from the main thread
                self.app.quit()
            # Check for the 'Home' key (previously '6')
            if key == keyboard.Key.home:
                self.logger.info("Hotkey 'Home' pressed - toggling pause state")
                self.toggle_pause()
                self.global_state.set('paused', self.is_paused)
        except Exception as e:
            self.logger.error(f"Error in keyboard listener: {str(e)}")

    def init_webcam_grabber(self, device_id=0):
        """Initialize the webcam frame grabber with the specified device ID."""
        # Stop existing webcam grabber if running
        if self.webcam_grabber:
            self.webcam_grabber.stop()

        # Create and start new webcam grabber
        # Always use 4K resolution for webcam capture
        self.webcam_grabber = WebcamFrameGrabber(device_id=device_id, width=3840, height=2160)
        self.webcam_grabber.start()
        # Register webcam grabber with global state
        self.global_state.set_webcam_grabber(self.webcam_grabber)
        self.logger.info(f"Webcam frame grabber initialized with device ID: {device_id}")

    def shutdown(self):
        """Clean shutdown of all threads and resources."""
        self.logger.info("Shutting down Director")
        self.shutting_down = True

        # Cancel any active task
        if self.active_task and self.active_task.is_running:
            self.active_task.cancel()

        # Wait for task thread to finish
        self.task_thread.join(timeout=2.0)

        # Stop keyboard listener
        if hasattr(self, 'keyboard_listener') and self.keyboard_listener.is_alive():
            self.keyboard_listener.stop()

        # Stop webcam grabber if running
        if hasattr(self, "webcam_grabber") and self.webcam_grabber is not None:
            self.webcam_grabber.stop()

        # Final cleanup - only remove log handler if it was added
        if not self.no_status and hasattr(self, 'log_handler'):
            logging.getLogger().removeHandler(self.log_handler)


if __name__ == "__main__":
    multiprocessing.freeze_support()
    director = Director()
    try:
        exit_code = director.run()
    except Exception as e:
        logging.error(f"Unhandled exception: {str(e)}")
        exit_code = 1
    finally:
        director.shutdown()

    sys.exit(exit_code)
