import os
import cv2
import base64
import numpy as np
import threading
import time
import re
import logging
from typing import Dict, Any, Optional, List, Tuple

# --- Basic Logging Config for Diagnostics ---
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
# --- End Basic Logging Config ---

from sensor import Sensor, OpenCVSensor, LocationSensor, YoloSensor, OCRSensor, SamenessSensor, Rect # Added SamenessSensor
from global_state import GlobalState

# Removed calculate_mse function

class SensorRunner:
    """
    Contains all execution code for each type of sensor.
    Responsible for running sensors and storing results in global state.
    """
    def __init__(self):
        self.global_state = GlobalState()
        self.global_state.set_sensor_runner(self)  # Register this instance with GlobalState
        self.lock = threading.Lock()
        self.running = False
        self.sensors: Dict[str, Sensor] = {}
        self.yolo_models = {}  # Cache for loaded YOLO models
        self.sameness_history: Dict[str, bytes] = {} # History for SamenessSensors

    def load_sensors(self, directory: str) -> None:
        """Load all sensor files from a directory."""
        if not os.path.exists(directory):
            return

        with self.lock:
            # Clear existing sensors before loading new ones
            # DO NOT clear sameness_history here - it should persist
            self.sensors.clear()
            print("Cleared existing sensors. Sameness history persists.")

            for filename in os.listdir(directory):
                if filename.endswith('.json'):
                    file_path = os.path.join(directory, filename)
                    try:
                        sensor = Sensor.load_from_file(file_path)
                        self.sensors[sensor.sensor_name] = sensor
                        print(f"Loaded sensor: {sensor.sensor_name}")
                    except Exception as e:
                        print(f"Error loading sensor {filename}: {e}")

    def run_sensor(self, sensor_name: str, substituteImage: np.ndarray = None, _is_internal_call: bool = False) -> Tuple[bool, Dict[str, Any]]:
        """
        Run a specific sensor and store results in global state.
        If the sensor has a parent, ensure the parent's state is updated first if using a substitute image.
        Returns a tuple of (success, metadata).
        """
        logging.debug(f"run_sensor called for: '{sensor_name}'") # Log entry point
        if sensor_name not in self.sensors:
            logging.error(f"Sensor '{sensor_name}' not found in loaded sensors: {list(self.sensors.keys())}")
            # Avoid infinite loops if parent doesn't exist
            if _is_internal_call:
                 logging.warning(f"Internal call failed: Parent sensor '{sensor_name}' not found during child run.")
                 return False, {"error": f"Parent sensor '{sensor_name}' not found during child run."}
            return False, {"error": f"Sensor '{sensor_name}' not found"}

        sensor = self.sensors[sensor_name]

        try:
            parent_offset = None
            if sensor.parent and sensor.parent.strip():
                # If we have a substitute image (e.g., from BuilderBuddy),
                # ensure the parent sensor runs against this image first.
                # Use _is_internal_call to prevent infinite recursion if parent lookup fails.
                if substituteImage is not None and not _is_internal_call:
                    logging.debug(f"Running parent sensor '{sensor.parent}' with substitute image for child '{sensor_name}'")
                    parent_success, _ = self.run_sensor(sensor.parent, substituteImage=substituteImage, _is_internal_call=True)
                    if not parent_success:
                        logging.warning(f"Failed to run parent sensor '{sensor.parent}' before running child '{sensor_name}'")
                        # Decide if we should proceed or fail the child? Let's fail for now.
                        return False, {"error": f"Failed to pre-run parent sensor '{sensor.parent}'"}

                # Now get parent data using get_flag to avoid auto-rerun in GlobalState
                parent_data = self.global_state.get_flag(sensor.parent)
                # --- MODIFICATION END ---

                if parent_data and parent_data.get("found", False):
                    # Use existing position logic
                    if "position" in parent_data:
                        parent_offset = (parent_data["position"]["x"], parent_data["position"]["y"])
                    elif "current_position" in parent_data: # Handle potential alternative key
                        parent_offset = (parent_data["current_position"]["x"], parent_data["current_position"]["y"])
                    else:
                        # Parent found but no position data? Problem.
                        return False, {"error": f"Parent sensor '{sensor.parent}' found but lacks position data"}
                else:
                    # If parent_data is still not found/valid after potential pre-run
                    return False, {"error": f"Parent sensor '{sensor.parent}' data not found or invalid"}

            if sensor.sensor_type == "OpenCV":
                if substituteImage is None:
                    return False, {"error": "No image provided. Screenshotting is no longer supported."}
                return self._run_opencv_sensor(sensor, substituteImage=substituteImage, parent_offset=parent_offset)
            elif sensor.sensor_type == "Location":
                return self._run_location_sensor(sensor, parent_offset=parent_offset)
            elif sensor.sensor_type == "Yolo":
                if substituteImage is None:
                    return False, {"error": "No image provided. Screenshotting is no longer supported."}
                return self._run_yolo_sensor(sensor, substituteImage=substituteImage, parent_offset=parent_offset)
            elif sensor.sensor_type == "OCR":
                if substituteImage is None:
                    logging.warning(f"OCR sensor '{sensor_name}' called without substituteImage.")
                    return False, {"error": "No image provided. Screenshotting is no longer supported."}
                return self._run_ocr_sensor(sensor, substituteImage=substituteImage, parent_offset=parent_offset)
            elif sensor.sensor_type == "Sameness": # Correctly placed elif
                logging.debug(f"Sensor '{sensor_name}' identified as Sameness type.") # Log type identification
                if substituteImage is None:
                    logging.warning(f"Sameness sensor '{sensor_name}' called without substituteImage.")
                    return False, {"error": "No image provided. Screenshotting is no longer supported."}
                return self._run_sameness_sensor(sensor, substituteImage=substituteImage, parent_offset=parent_offset)
            else: # Final else for unsupported types
                return False, {"error": f"Unsupported sensor type: {sensor.sensor_type}"}
        except Exception as e:
            logging.exception(f"Exception in run_sensor for {sensor_name}:")
            return False, {"error": f"Error running sensor {sensor_name}: {str(e)}"}

    def _run_opencv_sensor(self, sensor: OpenCVSensor, substituteImage: np.ndarray = None, parent_offset: Optional[Tuple[int, int]] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Run OpenCV sensor to perform template matching.
        Returns (success, metadata) tuple.

        Args:
            sensor: OpenCVSensor object containing template and region info
            substituteImage: Optional numpy array to use instead of taking a screenshot
            parent_offset: Optional (x, y) tuple of offsets from parent sensor
        """
        # Get the original region before any parent adjustments
        original_region = sensor.region
        # If parent_offset is provided, use it directly
        if parent_offset:
            parent_x, parent_y = parent_offset
            # For sensors with parents, coordinates in sensor.region are considered RELATIVE to parent
            # So we add the parent's position to get absolute screen coordinates
            adjusted_region = Rect(
                x=parent_x + sensor.region.x,
                y=parent_y + sensor.region.y,
                width=sensor.region.width,
                height=sensor.region.height
            )
            # Temporarily set the sensor region to the adjusted one
            sensor.region = adjusted_region

        # Get the template image
        template_bytes = sensor.get_template_image()
        if not template_bytes:
            return False, {"error": "Template image is required to run OpenCV sensor"}

        # Convert template image bytes to OpenCV format
        template_np = np.frombuffer(template_bytes, np.uint8)
        template = cv2.imdecode(template_np, cv2.IMREAD_COLOR)

        if template is None:
            return False, {"error": "Failed to decode template image"}

        # Get the region to search in
        region = sensor.region

        try:
            # Resize and crop, with bounds checks
            substituteImage_4k = cv2.resize(substituteImage, (3840, 2160), interpolation=cv2.INTER_LINEAR)
            height, width = substituteImage_4k.shape[:2]
            x1 = max(0, region.x)
            y1 = max(0, region.y)
            x2 = min(width, region.x + region.width)
            y2 = min(height, region.y + region.height)
            screen = substituteImage_4k[y1:y2, x1:x2]

            # Check if cropped image is empty or all zeros
            if screen is None or screen.size == 0:
                print("[DEBUG] Cropped screen is empty!")
            elif not np.any(screen):
                print("[DEBUG] Cropped screen is all zeros!")

            # Save both images for debugging
            # Save template/needle image
            # template_filename = f"{timestamp}_{sensor.sensor_name}_needle.png"
            # cv2.imwrite(os.path.join(debug_dir, template_filename), template)
            # # Save screenshot/haystack image
            # haystack_filename = f"{timestamp}_{sensor.sensor_name}_haystack.png"
            # cv2.imwrite(os.path.join(debug_dir, haystack_filename), screen)

            # Perform template matching
            result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)

            if sensor.vertical_priority:
                # If vertical priority is enabled, find all matches above threshold
                # and select the one with the lowest y-coordinate
                h, w = template.shape[:2]
                locations = np.where(result >= sensor.threshold)

                if locations[0].size > 0:
                    # Convert to a list of (y, x) points and sort by y
                    matches = [(y, x) for y, x in zip(*locations)]
                    matches_count = len(matches)

                    if matches:
                        # Sort by y-coordinate (lowest first)
                        matches.sort(key=lambda m: m[0])
                        y, x = matches[0]

                        # Calculate confidence at this location
                        confidence = result[y, x]

                        # Calculate center point of match
                        anchor_x = x + region.x
                        anchor_y = y + region.y
                        center_x = anchor_x + w / 2
                        center_y = anchor_y + h / 2

                        # Create result data with timestamp and timeout
                        result_data = {
                            "found": True,
                            "confidence": float(confidence),
                            "position": {
                                "x": int(anchor_x),
                                "y": int(anchor_y)
                            },
                            "dimensions": {
                                "width": w,
                                "height": h
                            },
                            "centerpoint": {"x": int(center_x), "y": int(center_y)},
                            "matches_count": matches_count,
                            "timestamp": time.time(),
                            "timeout": sensor.timeout
                        }

                        self.global_state.set(sensor.sensor_name, result_data)
                        return True, result_data

                # No matches found above threshold
                result_data = {
                    "found": False,
                    "confidence": 0.0,
                    "matches_count": 0,
                    "timestamp": time.time(),
                    "timeout": sensor.timeout
                }
                self.global_state.set(sensor.sensor_name, result_data)
                return False, result_data
            else:
                # Standard behavior - find the best match
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                # Find all matches above threshold for filtering
                locations = np.where(result >= sensor.threshold)
                matches = [(result[y, x], (x, y)) for y, x in zip(*locations)]

                # Sort matches by confidence (highest first)
                matches.sort(key=lambda m: m[0], reverse=True)

                # Filter out overlapping matches
                filtered_matches = []
                h, w = template.shape[:2]

                for confidence, (x, y) in matches:
                    # Check if this match overlaps with any higher confidence match
                    is_valid = True
                    for _, (fx, fy) in filtered_matches:
                        # Check if center of current match is within bounding box of filtered match
                        if (fx <= x < fx + w) and (fy <= y < fy + h):
                            is_valid = False
                            break

                    if is_valid:
                        filtered_matches.append((confidence, (x, y)))

                # Get filtered match count
                matches_count = len(filtered_matches)

                # Check if match exceeds threshold
                if max_val >= sensor.threshold:
                    # Calculate center point of match
                    h, w = template.shape[:2]
                    anchor_x = max_loc[0] + region.x
                    anchor_y = max_loc[1] + region.y
                    center_x = anchor_x + w / 2
                    center_y = anchor_y + h / 2

                    # Create result data with timestamp and timeout
                    result_data = {
                        "found": True,
                        "confidence": float(max_val),
                        "position": {
                            "x": int(anchor_x),
                            "y": int(anchor_y)
                        },
                        "dimensions": {
                            "width": w,
                            "height": h
                        },
                        "centerpoint": {"x": int(center_x), "y": int(center_y)},
                        "matches_count": int(matches_count),
                        "timestamp": time.time(),
                        "timeout": sensor.timeout
                    }

                    # Update global state with the results
                    self.global_state.set(sensor.sensor_name, result_data)
                    return True, result_data
                else:
                    # No match found
                    result_data = {
                        "found": False,
                        "confidence": float(max_val),
                        "matches_count": 0,
                        "timestamp": time.time(),
                        "timeout": sensor.timeout
                    }
                    self.global_state.set(sensor.sensor_name, result_data)
                    return False, result_data

        except Exception as e:
            logging.exception(f"Exception in _run_opencv_sensor for {sensor.sensor_name}:")
            error_data = {
                "found": False,
                "error": str(e),
                "timestamp": time.time(),
                "timeout": sensor.timeout
            }
            self.global_state.set(sensor.sensor_name, error_data)
            return False, error_data

        finally:
            # Restore the original region if it was adjusted
            if sensor.region != original_region:
                sensor.region = original_region

    def _run_sameness_sensor(self, sensor: SamenessSensor, substituteImage: np.ndarray, parent_offset: Optional[Tuple[int, int]] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Run Sameness sensor to compare the current region image with the previous one.
        Returns (success, metadata) tuple. Success is True if images match or first run.

        Args:
            sensor: SamenessSensor object containing region and threshold info.
            substituteImage: Numpy array of the current screen/window content.
            parent_offset: Optional (x, y) tuple of offsets from parent sensor.
        """
        # Enhanced debugging for sameness sensor
        logging.info(f"[SAMENESS DEBUG] Running sameness sensor: {sensor.sensor_name}")
        logging.info(f"[SAMENESS DEBUG] Confidence threshold: {sensor.confidence_threshold}")
        logging.info(f"[SAMENESS DEBUG] Current history keys: {list(self.sameness_history.keys())}")

        original_region = sensor.region
        adjusted_region = sensor.region # Start with original

        if parent_offset:
            parent_x, parent_y = parent_offset
            adjusted_region = Rect(
                x=parent_x + sensor.region.x,
                y=parent_y + sensor.region.y,
                width=sensor.region.width,
                height=sensor.region.height
            )
            # Temporarily update sensor region for cropping logic
            sensor.region = adjusted_region

        region = sensor.region # Use the potentially adjusted region

        try:
            # Crop the substitute image to the sensor's region
            # Resize and crop, with bounds checks (similar to OpenCV sensor)
            substituteImage_4k = cv2.resize(substituteImage, (3840, 2160), interpolation=cv2.INTER_LINEAR)
            height, width = substituteImage_4k.shape[:2]
            x1 = max(0, region.x)
            y1 = max(0, region.y)
            x2 = min(width, region.x + region.width)
            y2 = min(height, region.y + region.height)
            current_image_cropped = substituteImage_4k[y1:y2, x1:x2]

            if current_image_cropped is None or current_image_cropped.size == 0:
                return False, {"error": "Cropped image region is empty."}

            # Encode current cropped image to bytes for storage and comparison
            _, current_image_bytes_encoded = cv2.imencode('.png', current_image_cropped)
            current_image_bytes = current_image_bytes_encoded.tobytes()

            # Get the previous image bytes from the runner's history
            previous_image_bytes = self.sameness_history.get(sensor.sensor_name)

            # Debug the previous image state
            if previous_image_bytes is None:
                logging.info(f"[SAMENESS DEBUG] {sensor.sensor_name}: No previous image found in history")
            else:
                logging.info(f"[SAMENESS DEBUG] {sensor.sensor_name}: Found previous image in history ({len(previous_image_bytes)} bytes)")

            # Always update the stored image in the runner's history
            self.sameness_history[sensor.sensor_name] = current_image_bytes
            logging.info(f"[SAMENESS DEBUG] {sensor.sensor_name}: Updated history with new image ({len(current_image_bytes)} bytes)")

            # First run check (no previous image in history)
            if previous_image_bytes is None:
                logging.info(f"[SAMENESS DEBUG] {sensor.sensor_name}: First run, returning TRUE")
                result_data = {
                    "found": True, # Treat first run as success
                    "confidence": 1.0, # Confidence is max on first run
                    "reason": "First run",
                    "timestamp": time.time(),
                    "timeout": sensor.timeout
                }
                self.global_state.set(sensor.sensor_name, result_data)
                return True, result_data

            logging.debug(f"[{sensor.sensor_name}] Found previous image in history.")
            # Decode previous image for comparison
            previous_image_np = np.frombuffer(previous_image_bytes, np.uint8)
            previous_image_decoded = cv2.imdecode(previous_image_np, cv2.IMREAD_COLOR)

            if previous_image_decoded is None:
                 # This shouldn't happen if set_previous_image worked, but handle defensively
                 # This shouldn't happen if set_previous_image worked, but handle defensively
                 logging.error(f"Failed to decode previous image data for sensor {sensor.sensor_name}")
                 # Treat as different if previous image is corrupt
                 result_data = {
                     "found": False,
                     "confidence": 0.0,
                     "reason": "Failed to decode previous image",
                     "timestamp": time.time(),
                     "timeout": sensor.timeout
                 }
                 self.global_state.set(sensor.sensor_name, result_data)
                 return False, result_data


            logging.debug(f"[{sensor.sensor_name}] Current shape: {current_image_cropped.shape}, dtype: {current_image_cropped.dtype}")
            logging.debug(f"[{sensor.sensor_name}] Previous shape: {previous_image_decoded.shape}, dtype: {previous_image_decoded.dtype}")

            # Ensure images have the same dimensions for comparison
            if previous_image_decoded.shape != current_image_cropped.shape:
                logging.warning(f"[{sensor.sensor_name}] Image dimensions mismatch! Current: {current_image_cropped.shape}, Previous: {previous_image_decoded.shape}")
                # Images differ in size, likely due to region changes or errors
                # Treat as different
                result_data = {
                    "found": False,
                    "confidence": 0.0, # Confidence is 0 if shapes mismatch
                    "reason": f"Image dimensions mismatch (Current: {current_image_cropped.shape}, Previous: {previous_image_decoded.shape})",
                    "timestamp": time.time(),
                    "timeout": sensor.timeout
                }
                self.global_state.set(sensor.sensor_name, result_data)
                return False, result_data

            # Compare the images using template matching (correlation)
            try:
                # Ensure both images are suitable for matchTemplate (e.g., not empty, correct type)
                if current_image_cropped.size == 0 or previous_image_decoded.size == 0:
                     return False, {"error": "One or both images are empty for comparison."}
                if current_image_cropped.dtype != np.uint8 or previous_image_decoded.dtype != np.uint8:
                     # Ensure images are uint8 as expected by matchTemplate
                     current_image_cropped = current_image_cropped.astype(np.uint8)
                     previous_image_decoded = previous_image_decoded.astype(np.uint8)

                # Perform template matching (current vs previous)
                # Using TM_CCOEFF_NORMED: 1.0 means perfect match
                result = cv2.matchTemplate(current_image_cropped, previous_image_decoded, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, _ = cv2.minMaxLoc(result) # Get the maximum correlation value
                logging.debug(f"[{sensor.sensor_name}] Comparison max_val (correlation): {max_val:.6f}")
            except cv2.error as cv_err:
                 # Handle potential OpenCV errors during matching
                 logging.error(f"OpenCV error during image comparison for {sensor.sensor_name}: {cv_err}")
                 return False, {"error": f"OpenCV error during image comparison: {cv_err}"}
            except Exception as comp_err:
                 # Catch other potential errors during comparison
                 logging.error(f"Error during image comparison for {sensor.sensor_name}: {comp_err}")
                 return False, {"error": f"Error during image comparison: {comp_err}"}


            # Check if the similarity meets the sensor's confidence threshold
            logging.info(f"[SAMENESS DEBUG] {sensor.sensor_name}: Correlation value: {max_val:.6f}, Threshold: {sensor.confidence_threshold}")

            # Save debug images to help diagnose issues
            try:
                debug_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "debug_images")
                os.makedirs(debug_dir, exist_ok=True)
                timestamp = int(time.time())

                # Save current image
                current_img_path = os.path.join(debug_dir, f"{sensor.sensor_name}_current_{timestamp}.png")
                cv2.imwrite(current_img_path, current_image_cropped)

                # Save previous image
                prev_img_path = os.path.join(debug_dir, f"{sensor.sensor_name}_previous_{timestamp}.png")
                cv2.imwrite(prev_img_path, previous_image_decoded)

                logging.info(f"[SAMENESS DEBUG] Saved debug images to {debug_dir}")
            except Exception as e:
                logging.error(f"[SAMENESS DEBUG] Failed to save debug images: {e}")

            if max_val >= sensor.confidence_threshold:
                # Images are considered the same
                logging.info(f"[SAMENESS DEBUG] {sensor.sensor_name}: MATCH FOUND! Correlation: {max_val:.4f} >= {sensor.confidence_threshold}")
                result_data = {
                    "found": True,
                    "confidence": float(max_val),
                    "reason": f"Images match (Correlation: {max_val:.4f} >= {sensor.confidence_threshold})",
                    "timestamp": time.time(),
                    "timeout": sensor.timeout
                }
                self.global_state.set(sensor.sensor_name, result_data)
                return True, result_data
            else:
                # Images are considered different
                logging.info(f"[SAMENESS DEBUG] {sensor.sensor_name}: NO MATCH! Correlation: {max_val:.4f} < {sensor.confidence_threshold}")
                result_data = {
                    "found": False,
                    "confidence": float(max_val),
                    "reason": f"Images differ (Correlation: {max_val:.4f} < {sensor.confidence_threshold})",
                    "timestamp": time.time(),
                    "timeout": sensor.timeout
                }
                self.global_state.set(sensor.sensor_name, result_data)
                return False, result_data

        except Exception as e:
            logging.exception(f"Exception in _run_sameness_sensor for {sensor.sensor_name}:")
            error_data = {
                "found": False,
                "error": str(e),
                "timestamp": time.time(),
                "timeout": sensor.timeout
            }
            self.global_state.set(sensor.sensor_name, error_data)
            return False, error_data

        finally:
            # Restore the original region if it was adjusted
            if sensor.region != original_region:
                sensor.region = original_region

    def _run_location_sensor(self, sensor: LocationSensor, parent_offset: Optional[Tuple[int, int]] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Run location sensor to check position data.
        Returns a tuple of (success, metadata).

        Args:
            sensor: LocationSensor object containing location info
            parent_offset: Optional (x, y) tuple of offsets from parent sensor
        """
        # Get the original region before any parent adjustments
        original_region = sensor.region

        # If parent_offset is provided, use it directly
        if parent_offset:
            parent_x, parent_y = parent_offset
            # For sensors with parents, coordinates in sensor.region are considered RELATIVE to parent
            # So we add the parent's position to get absolute screen coordinates
            adjusted_region = Rect(
                x=parent_x + sensor.region.x,
                y=parent_y + sensor.region.y,
                width=sensor.region.width,
                height=sensor.region.height
            )
            # Temporarily set the sensor region to the adjusted one
            sensor.region = adjusted_region

        # Calculate the center of the region for position reporting
        center_x = sensor.region.x + (sensor.region.width // 2)
        center_y = sensor.region.y + (sensor.region.height // 2)

        result_data = {
            "found": True,
            "position": {
                "x": center_x,
                "y": center_y
            },
            "region": {
                "x": sensor.region.x,
                "y": sensor.region.y,
                "width": sensor.region.width,
                "height": sensor.region.height
            },
            "timestamp": time.time(),
            "timeout": sensor.timeout,
            "centerpoint": {"x": int(center_x), "y": int(center_y)},

        }

        # Store in global state
        self.global_state.set(sensor.sensor_name, result_data)

        # Restore the original region if it was adjusted
        if sensor.region != original_region:
            sensor.region = original_region

        # LocationSensor always reports success
        success = True

        return success, result_data

    def run_requested_sensors(self, sensor_names: List[str]) -> Dict[str, Tuple[bool, Dict[str, Any]]]:
        """
        Run multiple sensors requested by a task.
        Returns a dictionary mapping sensor names to their results.
        """
        results = {}

        for name in sensor_names:
            success, metadata = self.run_sensor(name)
            results[name] = (success, metadata)

        return results

    def get_sensor(self, sensor_name: str) -> Optional[Sensor]:
        """Get a sensor by name."""
        return self.sensors.get(sensor_name)

    def add_sensor(self, sensor: Sensor) -> None:
        """Add or update a sensor."""
        with self.lock:
            self.sensors[sensor.sensor_name] = sensor

    def remove_sensor(self, sensor_name: str) -> bool:
        """Remove a sensor by name. Returns True if removed, False if not found."""
        with self.lock:
            if sensor_name in self.sensors:
                # Also remove from sameness history if it exists
                if sensor_name in self.sameness_history:
                    del self.sameness_history[sensor_name]
                del self.sensors[sensor_name]
                return True
            return False

    def clear_sameness_history(self, sensor_name: str = None) -> None:
        """
        Clear the sameness history for a specific sensor or all sensors.

        Args:
            sensor_name: Optional name of sensor to clear history for.
                         If None, clears history for all sensors.
        """
        with self.lock:
            if sensor_name is None:
                # Clear all history
                self.sameness_history.clear()
                logging.info("[SAMENESS DEBUG] Cleared all sameness history")
            elif sensor_name in self.sameness_history:
                # Clear history for specific sensor
                del self.sameness_history[sensor_name]
                logging.info(f"[SAMENESS DEBUG] Cleared sameness history for {sensor_name}")
            else:
                logging.info(f"[SAMENESS DEBUG] No history found for {sensor_name} to clear")

    def _run_yolo_sensor(self, sensor: YoloSensor, substituteImage: np.ndarray = None, parent_offset: Optional[Tuple[int, int]] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Run YOLO sensor to perform object detection.
        Returns (success, metadata) tuple.

        Args:
            sensor: YoloSensor object containing model path and region info
            substituteImage: Optional numpy array to use instead of taking a screenshot
            parent_offset: Optional (x, y) tuple of offsets from parent sensor
        """
        try:
            # Import YOLO here to avoid dependency issues if not installed
            from ultralytics import YOLO
        except ImportError:
            return False, {"error": "ultralytics package not installed. Please install with 'pip install ultralytics'."}

        # Get the original region before any parent adjustments
        original_region = sensor.region

        # If parent_offset is provided, use it directly
        if parent_offset:
            parent_x, parent_y = parent_offset
            # For sensors with parents, coordinates in sensor.region are considered RELATIVE to parent
            # So we add the parent's position to get absolute screen coordinates
            adjusted_region = Rect(
                x=parent_x + sensor.region.x,
                y=parent_y + sensor.region.y,
                width=sensor.region.width,
                height=sensor.region.height
            )
            # Temporarily set the sensor region to the adjusted one
            sensor.region = adjusted_region

        # Check if model path is valid
        if not sensor.model_path or not os.path.exists(sensor.model_path):
            return False, {"error": f"Model path '{sensor.model_path}' not found. Please provide a valid path to a YOLO model."}

        # Load or get cached YOLO model
        if sensor.model_path not in self.yolo_models:
            try:
                print(f"Loading YOLO model from {sensor.model_path}...")
                self.yolo_models[sensor.model_path] = YOLO(sensor.model_path)
                print(f"Model loaded successfully")
            except Exception as e:
                return False, {"error": f"Failed to load YOLO model: {str(e)}"}

        model = self.yolo_models[sensor.model_path]

        # Get the region to search in
        region = sensor.region

        try:
            if substituteImage is not None:
                # Otherwise, crop according to region
                screen = substituteImage[region.y:region.y + region.height,
                                        region.x:region.x + region.width]
            else:
                from window_capture import grab_foreground_window
                screen = grab_foreground_window()
                screen = cv2.resize(screen, (3840, 2160), interpolation=cv2.INTER_LINEAR)
                screen = screen[region.y:region.y + region.height, region.x:region.x + region.width]

            # Run YOLO inference
            results = model.predict(
                source=screen,
                conf=sensor.confidence_threshold,
                imgsz=sensor.image_size,
                device=sensor.device,
                verbose=False
            )
            # Get detection boxes
            detections = results[0].boxes  # YOLO returns one result per image

            # Check if there are any detections
            if detections is None or len(detections) == 0:
                # No detections found
                result_data = {
                    "found": False,
                    "confidence": 0.0,
                    "detections": [],
                    "timestamp": time.time(),
                    "timeout": sensor.timeout
                }
                self.global_state.set(sensor.sensor_name, result_data)
                return False, result_data

            # Process detections
            detection_list = []
            best_detection = None
            best_confidence = 0.0

            for i, box in enumerate(detections):
                cls = int(box.cls[0]) if box.cls is not None else 0
                conf = float(box.conf[0]) if box.conf is not None else 0.0

                # Skip if class filter is set and this class is not in the filter
                if sensor.class_filter and cls not in sensor.class_filter:
                    continue

                # Get coordinates relative to the full screen
                x1, y1, x2, y2 = map(int, box.xyxy[0])

                # Adjust coordinates to be relative to the full screen
                abs_x1 = x1 + region.x
                abs_y1 = y1 + region.y
                abs_x2 = x2 + region.x
                abs_y2 = y2 + region.y

                width = abs_x2 - abs_x1
                height = abs_y2 - abs_y1

                # Calculate center point
                center_x = abs_x1 + (width // 2)
                center_y = abs_y1 + (height // 2)

                detection_data = {
                    "id": i,
                    "class": cls,
                    "confidence": conf,
                    "position": {
                        "x": center_x,
                        "y": center_y
                    },
                    "bbox": {
                        "x1": abs_x1,
                        "y1": abs_y1,
                        "x2": abs_x2,
                        "y2": abs_y2,
                        "width": width,
                        "height": height
                    }
                }

                detection_list.append(detection_data)

                # Keep track of best detection (highest confidence)
                if conf > best_confidence:
                    best_confidence = conf
                    best_detection = detection_data

            # If all detections were filtered out by class filter
            if not detection_list:
                result_data = {
                    "found": False,
                    "confidence": 0.0,
                    "detections": [],
                    "timestamp": time.time(),
                    "timeout": sensor.timeout
                }
                self.global_state.set(sensor.sensor_name, result_data)
                return False, result_data

            # Sort detections by confidence (highest first)
            detection_list.sort(key=lambda d: d["confidence"], reverse=True)

            # Create result with all detections and best match highlighted
            result_data = {
                "found": True,
                "confidence": best_confidence,
                "position": best_detection["position"],  # Use position of best match for backward compatibility
                "dimensions": {
                    "width": best_detection["bbox"]["width"],
                    "height": best_detection["bbox"]["height"]
                },
                "best_detection": best_detection,
                "detections": detection_list,
                "num_detections": len(detection_list),
                "timestamp": time.time(),
                "timeout": sensor.timeout
            }

            # Update global state with the results
            self.global_state.set(sensor.sensor_name, result_data)
            return True, result_data

        except Exception as e:
            error_data = {
                "found": False,
                "error": str(e),
                "timestamp": time.time(),
                "timeout": sensor.timeout
            }
            self.global_state.set(sensor.sensor_name, error_data)
            return False, error_data

        finally:
            # Restore the original region if it was adjusted
            if sensor.region != original_region:
                sensor.region = original_region

    def _run_ocr_sensor(self, sensor: OCRSensor, substituteImage: np.ndarray = None, parent_offset: Optional[Tuple[int, int]] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Run OCR sensor to perform text recognition using Tesseract OCR.
        Returns (success, metadata) tuple.

        Args:
            sensor: OCRSensor object containing search text and region info
            substituteImage: Optional numpy array to use instead of taking a screenshot
            parent_offset: Optional (x, y) tuple of offsets from parent sensor
        """
        try:
            # Import pytesseract here to avoid dependency issues if not installed
            import pytesseract
        except ImportError:
            return False, {"error": "pytesseract package not installed. Please install with 'pip install pytesseract'."}

        # Get the original region before any parent adjustments
        original_region = sensor.region

        # If parent_offset is provided, use it directly
        if parent_offset:
            parent_x, parent_y = parent_offset
            # For sensors with parents, coordinates in sensor.region are considered RELATIVE to parent
            # So we add the parent's position to get absolute screen coordinates
            adjusted_region = Rect(
                x=parent_x + sensor.region.x,
                y=parent_y + sensor.region.y,
                width=sensor.region.width,
                height=sensor.region.height
            )
            # Temporarily set the sensor region to the adjusted one
            sensor.region = adjusted_region

        # Get the region to search in
        region = sensor.region

        try:
            if substituteImage is not None:
                # If substituteImage already matches the region size, use as-is
                if (
                    substituteImage.shape[0] == region.height and
                    substituteImage.shape[1] == region.width
                ):
                    screen = substituteImage
                else:
                    # Otherwise, crop according to region
                    screen = substituteImage[region.y:region.y + region.height,
                                            region.x:region.x + region.width]
            else:
                from window_capture import grab_foreground_window
                screen = grab_foreground_window()
                screen = cv2.resize(screen, (3840, 2160), interpolation=cv2.INTER_LINEAR)
                screen = screen[region.y:region.y + region.height, region.x:region.x + region.width]

            # Preprocess the image to improve OCR accuracy
            if sensor.scale_factor != 1.0:
                new_width = int(screen.shape[1] * sensor.scale_factor)
                new_height = int(screen.shape[0] * sensor.scale_factor)
                screen = cv2.resize(screen, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

            # Convert to grayscale
            gray = cv2.cvtColor(screen, cv2.COLOR_BGR2GRAY)

            # Apply additional preprocessing (thresholding)
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)

            # Configure tesseract parameters
            config = f"--psm 6"  # Assume a single uniform block of text

            # Add language configuration
            config += f" -l {sensor.language}"

            # Add character whitelist if specified
            if sensor.whitelist_chars:
                config += f" -c tessedit_char_whitelist='{sensor.whitelist_chars}'"

            # Perform OCR to get all text in the region
            ocr_data = pytesseract.image_to_data(binary, config=config, output_type=pytesseract.Output.DICT)

            # Look for the text we're searching for
            search_text = sensor.search_text.strip()
            found_text = False
            best_confidence = 0.0
            best_match_position = None
            best_match_dimensions = None
            all_matches = []

            # Process OCR results
            text_blocks = []
            current_block = ""
            current_block_data = None

            for i in range(len(ocr_data['text'])):
                text = ocr_data['text'][i]
                conf = float(ocr_data['conf'][i]) / 100.0  # Convert to 0-1 scale

                # Skip low confidence or empty text
                if conf < 0.05 or not text.strip():
                    continue

                # Accumulate text blocks for exact and substring matching
                if current_block_data is None:
                    current_block_data = {
                        'x': ocr_data['left'][i],
                        'y': ocr_data['top'][i],
                        'width': ocr_data['width'][i],
                        'height': ocr_data['height'][i],
                        'conf': conf
                    }
                else:
                    # Update width to span the entire block
                    right_edge = max(
                        current_block_data['x'] + current_block_data['width'],
                        ocr_data['left'][i] + ocr_data['width'][i]
                    )
                    current_block_data['width'] = right_edge - current_block_data['x']
                    # Average the confidence
                    current_block_data['conf'] = (current_block_data['conf'] + conf) / 2

                current_block += " " + text if current_block else text

                # If we see a line break or end of paragraph, store the block
                if i + 1 >= len(ocr_data['text']) or ocr_data['line_num'][i] != ocr_data['line_num'][i + 1]:
                    if current_block.strip():
                        text_blocks.append({
                            'text': current_block.strip(),
                            **current_block_data
                        })
                    current_block = ""
                    current_block_data = None

            # Look for matches in the text blocks
            for block in text_blocks:
                # Exact match
                if search_text.lower() == block['text'].lower():
                    match_confidence = block['conf'] * 1.0  # Full confidence for exact match
                    match_data = {
                        'text': block['text'],
                        'confidence': float(match_confidence),
                        'position': {
                            'x': int(block['x'] + region.x),
                            'y': int(block['y'] + region.y)
                        },
                        'dimensions': {
                            'width': int(block['width']),
                            'height': int(block['height'])
                        },
                        'match_type': 'exact'
                    }
                    all_matches.append(match_data)

                    if match_confidence > best_confidence:
                        found_text = True
                        best_confidence = match_confidence
                        best_match_position = match_data['position']
                        best_match_dimensions = match_data['dimensions']

                # Try regex pattern matching for partial matches
                elif re.search(search_text.lower(), block['text'].lower()):
                    # Lower confidence for partial matches
                    match_confidence = block['conf'] * 0.8

                    match_data = {
                        'text': block['text'],
                        'confidence': float(match_confidence),
                        'position': {
                            'x': int(block['x'] + region.x),
                            'y': int(block['y'] + region.y)
                        },
                        'dimensions': {
                            'width': int(block['width']),
                            'height': int(block['height'])
                        },
                        'match_type': 'partial'
                    }
                    all_matches.append(match_data)

                    if match_confidence > best_confidence:
                        found_text = True
                        best_confidence = match_confidence
                        best_match_position = match_data['position']
                        best_match_dimensions = match_data['dimensions']

            # Check if match exceeds threshold
            if found_text and best_confidence >= sensor.confidence_threshold:
                result_data = {
                    "found": True,
                    "confidence": float(best_confidence),
                    "position": best_match_position,
                    "dimensions": best_match_dimensions,
                    "matches_count": len(all_matches),
                    "matches": all_matches,
                    "timestamp": time.time(),
                    "timeout": sensor.timeout
                }
                self.global_state.set(sensor.sensor_name, result_data)
                return True, result_data
            else:
                # No matches found or confidence too low
                result_data = {
                    "found": False,
                    "confidence": float(best_confidence) if found_text else 0.0,
                    "matches_count": len(all_matches),
                    "matches": all_matches,
                    "search_text": search_text,
                    "timestamp": time.time(),
                    "timeout": sensor.timeout
                }
                self.global_state.set(sensor.sensor_name, result_data)
                return False, result_data

        except Exception as e:
            error_data = {
                "found": False,
                "error": str(e),
                "timestamp": time.time(),
                "timeout": sensor.timeout
            }
            self.global_state.set(sensor.sensor_name, error_data)
            return False, error_data

        finally:
            # Restore the original region if it was adjusted
            if sensor.region != original_region:
                sensor.region = original_region
