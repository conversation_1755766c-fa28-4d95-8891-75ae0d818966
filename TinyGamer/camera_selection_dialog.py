import cv2
import numpy as np
from PyQt5.QtWidgets import (
    Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QWidget, QApplication, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import Q<PERSON>mage, QPixmap, QFont
from config_manager import ConfigManager

class CameraSelectionDialog(QDialog):
    """Dialog for displaying the camera preview using the device ID from settings.config."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Camera Preview")
        self.resize(640, 520)  # Reasonable size for preview

        # Load camera device ID from config
        self.config_manager = ConfigManager()
        self.selected_device_id = self.config_manager.get_camera_device_number()
        self.current_device_id = self.selected_device_id
        self.cap = None

        # Create UI
        self._create_ui()

        # Start camera preview
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_preview)
        self.timer.start(33)  # ~30 FPS



    def _create_ui(self):
        """Create the dialog UI elements."""
        layout = QVBoxLayout(self)

        # Camera info section
        info_layout = QHBoxLayout()
        info_layout.addWidget(QLabel(f"Using Camera Device: {self.selected_device_id}"))
        info_layout.addStretch()
        layout.addLayout(info_layout)

        # Preview label
        self.preview_label = QLabel("Connecting to camera...")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout.addWidget(self.preview_label)

        # Buttons
        button_layout = QHBoxLayout()
        self.select_button = QPushButton("Continue")
        self.select_button.clicked.connect(self.accept)
        button_layout.addWidget(self.select_button)

        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.refresh_camera)
        button_layout.addWidget(self.refresh_button)

        layout.addLayout(button_layout)

        # Initialize camera
        self.init_camera()

    def init_camera(self):
        """Initialize the camera with the device ID from config."""
        device_id = self.selected_device_id

        # Close existing capture if open
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()

        # Open new capture
        self.cap = cv2.VideoCapture(device_id)
        if not self.cap.isOpened():
            self.preview_label.setText(f"Failed to open camera {device_id}\n\nPlease check your settings.config file.")
            return

        self.current_device_id = device_id

    def refresh_camera(self):
        """Refresh the camera preview."""
        # Release current camera
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()
            self.cap = None

        # Reload config in case it was changed
        self.selected_device_id = self.config_manager.get_camera_device_number()

        # Restart camera preview
        self.init_camera()

    def update_preview(self):
        """Update camera preview frame."""
        if self.cap is None or not self.cap.isOpened():
            return

        ret, frame = self.cap.read()
        if not ret:
            self.preview_label.setText("Failed to grab frame from camera")
            return

        # Convert to RGB for Qt
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        h, w, ch = frame_rgb.shape

        # Scale the image to fit the preview area while maintaining aspect ratio
        # Get the size of the preview label
        label_width = self.preview_label.width()
        label_height = self.preview_label.height()

        # Calculate scale factor to fit within the label
        scale_w = label_width / w
        scale_h = label_height / h
        scale = min(scale_w, scale_h)

        new_w = int(w * scale)
        new_h = int(h * scale)

        # Resize the frame
        if scale < 1:  # Only resize if it's too big
            frame_rgb = cv2.resize(frame_rgb, (new_w, new_h))
            h, w, ch = frame_rgb.shape

        # Convert to QImage and display
        bytes_per_line = ch * w
        qt_image = QImage(frame_rgb.data, w, h, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(qt_image)
        self.preview_label.setPixmap(pixmap)

    def get_selected_camera_id(self):
        """Return the selected camera device ID."""
        return self.selected_device_id

    def closeEvent(self, event):
        """Clean up resources when dialog is closed."""
        self.timer.stop()
        if self.cap is not None and self.cap.isOpened():
            self.cap.release()
        super().closeEvent(event)

# For testing as standalone
if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    dialog = CameraSelectionDialog()
    if dialog.exec_():
        print(f"Selected camera ID: {dialog.get_selected_camera_id()}")
    else:
        print("Camera selection canceled")
