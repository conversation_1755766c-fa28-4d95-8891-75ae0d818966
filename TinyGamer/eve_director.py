import os
import logging
import pynput
from PyQt5.QtCore import Qt
from director import Director
from task import TaskRegistry
from camera_selection_dialog import CameraSelectionDialog
from config_manager import ConfigManager

class EveDirector(Director):
    """
    Special director class for Eve Online automation.
    Extends the base Director with Eve-specific hotkeys and task loading.
    """
    def __init__(self, no_preview=False, no_status=False):
        # Load camera device ID from config
        config_manager = ConfigManager()
        camera_device_id = config_manager.get_camera_device_number()

        # Initialize with device_id from config but don't init webcam yet
        super().__init__(camera_device_id=camera_device_id, init_webcam=False, no_status=no_status)

        self.logger = logging.getLogger("EveDirector")

        if no_preview or no_status:
            # Skip camera preview dialog and use the camera ID from config
            self.logger.info(f"Skipping camera preview dialog (--no-preview or --no-status flag used)")
        else:
            # Now that QApplication is initialized through the parent constructor,
            # we can safely create and show the camera preview dialog
            camera_dialog = CameraSelectionDialog()
            if camera_dialog.exec_():
                # Get the possibly updated camera ID from the dialog
                camera_device_id = camera_dialog.get_selected_camera_id()

        # Now initialize the webcam with the camera ID
        self.init_webcam_grabber(device_id=camera_device_id)
        self.logger.info(f"Eve Director initialized with camera ID: {camera_device_id}")

    def _get_task_directories(self):
        """
        Override the base task directories method to focus on Eve Online tasks.
        """
        eve_tasks_path = os.path.join(os.path.dirname(__file__), 'Games', 'EveOnline', 'Tasks')

        # Ensure Eve tasks path exists
        if not os.path.exists(eve_tasks_path):
            self.logger.warning(f"Eve Online tasks directory not found at: {eve_tasks_path}")
            # Fall back to standard task directories if Eve directory doesn't exist
            return super()._get_task_directories()

        self.logger.info(f"Loading Eve Online tasks from: {eve_tasks_path}")
        return [eve_tasks_path]

    def _on_key_press(self, key):
        """
        Handle Eve-specific hotkeys in addition to base Director hotkeys.
        """
        try:
            # # Check for the '1' key to launch asteroid targeting task
            # if hasattr(key, 'char') and key.char == '1':
            #     self.logger.info("Hotkey '1' pressed - starting TaskMining")
            #     success, metadata = self.run_task("TaskMiningLowSec")
            #     if not success:
            #         self.logger.error(f"Failed to run TaskMining: {metadata}")
            # # Check for the '2' key to launch auto travel task
            # elif hasattr(key, 'char') and key.char == '2':
            #     self.logger.info("Hotkey '2' pressed - starting TaskAutoTravel")
            #     success, metadata = self.run_task("TaskAutoTravel")
            #     if not success:
            #         self.logger.error(f"Failed to run TaskAutoTravel: {metadata}")
            # # Check for the 'page up' key to launch buy order task
            if key == pynput.keyboard.Key.page_up:
                self.logger.info("Hotkey 'page up' pressed - starting TaskMarketWindowPlaceBuyOrder")
                success, metadata = self.run_task("TaskMarketWindowPlaceBuyOrder")
                if not success:
                    self.logger.error(f"Failed to run TaskMarketWindowPlaceBuyOrder: {metadata}")
            elif key == pynput.keyboard.Key.page_down:
                self.logger.info("Hotkey 'page down' pressed - starting TaskStationInventorySellAllItems")
                success, metadata = self.run_task("TaskStationInventorySellAllItems")
                if not success:
                    self.logger.error(f"Failed to run TaskStationInventorySellAllItems: {metadata}")
            else:
                # Call the parent class's key handler for other keys
                super()._on_key_press(key)

        except Exception as e:
            self.logger.error(f"Error in Eve keyboard listener: {str(e)}")

# Replace main block in director.py to use EveDirector when run directly
if __name__ == "__main__":
    import multiprocessing
    import sys

    multiprocessing.freeze_support()
    director = EveDirector()
    try:
        exit_code = director.run()
    except Exception as e:
        logging.error(f"Unhandled exception: {str(e)}")
        exit_code = 1
    finally:
        director.shutdown()

    sys.exit(exit_code)
