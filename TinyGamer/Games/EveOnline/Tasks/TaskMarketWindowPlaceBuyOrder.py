from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskMultibuyWindowOpen import TaskMultibuyWindowOpen
from Games.EveOnline.Tasks.TaskMultibuyWindowImportClick import TaskMultibuyWindowImportClick
from Games.EveOnline.Tasks.TaskMultibuyWindowImportConfirm import TaskMultibuyWindowImportConfirm
from Games.EveOnline.Tasks.TaskMultiBuyWindowFirstItemClick import TaskMultiBuyWindowFirstItemClick
from Games.EveOnline.Tasks.TaskMarketBuyOrderClick import TaskMarketBuyOrder<PERSON>lick
from Games.EveOnline.Tasks.TaskBuyOrderPriceSet import TaskBuyOrderPriceSet
from Games.EveOnline.Tasks.TaskBuyOrderQuantitySet import TaskBuyOrderQuantitySet
from Games.EveOnline.Tasks.TaskBuyOrderPlaceOrder import TaskBuyOrderPlaceOrder
from Games.EveOnline.Tasks.TaskInformationWindowMarketDetailsClick import TaskInformationWindowMarketDetailsClick
import logging
import pyperclip

@TaskRegistry.register
class TaskMarketWindowPlaceBuyOrder(Task):
    """
    Task for placing a buy order for an item in the market window.
    This is a composite task that runs other tasks in sequence.
    """
    def __init__(self, price="1", quantity="1", game_interactor=None):
        super().__init__("MarketWindowPlaceBuyOrder", game_interactor=game_interactor)
        self.price = price
        self.quantity = quantity

    def execute(self):
        """
        Execute the task sequence for placing a buy order.
        Returns (success, metadata) tuple.
        """
        try:
            # Try to get item and quantity from clipboard
            try:
                clipboard_text = pyperclip.paste()
                self.logger.info(f"Clipboard content: {clipboard_text}")

                if clipboard_text and " " in clipboard_text:
                    # Parse clipboard text in format "ItemName Quantity"
                    parts = clipboard_text.rsplit(" ", 1)
                    if len(parts) == 2 and parts[1].isdigit():
                        self.quantity = parts[1]
                        self.logger.info(f"Using quantity from clipboard: {self.quantity}")
            except Exception as e:
                self.logger.warning(f"Error reading from clipboard: {str(e)}")

            # Try to get price from temporary file
            try:
                import tempfile
                import os

                # Get the temp file path
                temp_dir = tempfile.gettempdir()
                price_file_path = os.path.join(temp_dir, "tinygamer_price.txt")

                # Check if the file exists
                if os.path.exists(price_file_path):
                    # Read the price from the file
                    with open(price_file_path, "r") as f:
                        price_str = f.read().strip()

                    # Validate and use the price
                    if price_str:
                        try:
                            # Convert to float to validate it's a number
                            float(price_str)
                            self.price = price_str
                            self.logger.info(f"Using price from temp file: {self.price}")
                        except ValueError:
                            self.logger.warning(f"Invalid price in temp file: {price_str}")
            except Exception as e:
                self.logger.warning(f"Error reading price from temp file: {str(e)}")
            # Open the multibuy window
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMultibuyWindowOpen),
                "Multibuy window not found after opening it"
            ):
                return False, {"error": "Multibuy window not found after opening it"}

            # Click on the import button
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMultibuyWindowImportClick),
                "Failed to click on import button"
            ):
                return False, {"error": "Failed to click on import button"}

            # Confirm the import
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMultibuyWindowImportConfirm),
                "Failed to confirm import"
            ):
                return False, {"error": "Failed to confirm import"}

            # Click on the first item
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMultiBuyWindowFirstItemClick),
                "Failed to click on first item"
            ):
                return False, {"error": "Failed to click on first item"}

            # Click market details
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskInformationWindowMarketDetailsClick),
                "Failed to click on market details"
            ):
                return False, {"error": "Failed to click on market details"}

            # Click on the buy order button
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMarketBuyOrderClick),
                "Failed to click on buy order button"
            ):
                return False, {"error": "Failed to click on buy order button"}

            # Set the price in the buy order window
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskBuyOrderPriceSet, price=self.price),
                "Failed to set price in buy order window"
            ):
                return False, {"error": "Failed to set price in buy order window"}

            # Set the quantity in the buy order window
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskBuyOrderQuantitySet, quantity=self.quantity),
                "Failed to set quantity in buy order window"
            ):
                return False, {"error": "Failed to set quantity in buy order window"}

            # Place the buy order
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskBuyOrderPlaceOrder),
                "Failed to place buy order"
            ):
                self._write_completion_status(False, "Failed to place buy order")
                return False, {"error": "Failed to place buy order"}

            # Write completion status to temp file
            self._write_completion_status(True, "Buy order placed successfully")

            return True, {}
        except CancelException as ex:
            self._write_completion_status(False, str(ex))
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskMarketWindowPlaceBuyOrder.execute:")
            self._write_completion_status(False, str(e))
            return False, {"error": str(e)}

    def _write_completion_status(self, success: bool, message: str):
        """
        Write task completion status to a temporary file for TinyTrader to read.

        Args:
            success: Whether the task completed successfully
            message: Status message
        """
        try:
            import tempfile
            import os
            import json
            import time

            # Get the temp file path
            temp_dir = tempfile.gettempdir()
            status_file_path = os.path.join(temp_dir, "tinygamer_status.json")

            # Create status data
            status_data = {
                "task": self.name,
                "success": success,
                "message": message,
                "timestamp": time.time()
            }

            # Write status to file
            with open(status_file_path, "w") as f:
                json.dump(status_data, f)

            self.logger.info(f"Wrote completion status to {status_file_path}: {success}, {message}")
        except Exception as e:
            self.logger.error(f"Error writing completion status: {str(e)}")