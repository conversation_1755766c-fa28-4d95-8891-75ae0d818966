from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskStationInventoryOpen import TaskStationInventoryOpen
import logging

@TaskRegistry.register
class TaskStationInventoryOpenSubmenu(Task):
    """
    Task for opening the submenu for the first item in the station inventory.
    """
    def __init__(self, game_interactor=None):
        super().__init__("StationInventoryOpenSubmenu", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to open the submenu for the first item in the station inventory.
        Returns (success, metadata) tuple.
        """
        try:
            # First make sure the station inventory window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskStationInventoryOpen),
                "Station inventory window not found after opening it"
            ):
                return False, {"error": "Station inventory window not found after opening it"}
            
            # Check if the first item is visible
            if not self.is_sensor_found("StationInventoryWindowFirstItem"):
                self.logger.error("First item in station inventory window not found")
                return False, {"error": "First item in station inventory window not found"}
            
            # Get the first item location
            first_item_location = self.global_state.get("StationInventoryWindowFirstItem")
            
            # Right-click on the first item
            self.interactor.EveSafeClickRight(x=first_item_location["centerpoint"]["x"], y=first_item_location["centerpoint"]["y"])
            self.wait(1.0)
            
            # Check if the sell menu option is visible
            if not self.is_sensor_found("StationInventoryWindowSellMenu"):
                self.logger.error("Sell menu option not found")
                return False, {"error": "Sell menu option not found"}
            
            # Get the sell menu option location
            sell_menu_location = self.global_state.get("StationInventoryWindowSellMenu")
            
            # Left-click on the sell menu option
            self.interactor.EveSafeClick(x=sell_menu_location["centerpoint"]["x"], y=sell_menu_location["centerpoint"]["y"])
            self.wait(1.0)
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskStationInventoryOpenSubmenu.execute:")
            return False, {"error": str(e)}
