from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskOverviewMiningTabSelect import TaskOverviewMiningTabSelect
from Games.EveOnline.Tasks.TaskOpenSpaceClick import TaskOpenSpaceClick
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskOverviewWindowTargetLockAsteroid(Task):
    """
    Task for targeting and locking an asteroid in the overview window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("OverviewWindowTargetLockAsteroid", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to target and lock an asteroid in the overview window.
        Returns (success, metadata) tuple.
        """
        try:
            # Make sure the mining tab is selected
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskOverviewMiningTabSelect),
                "Mining tab not selected after trying to select it"
            ):
                return False, {"error": "Mining tab not selected after trying to select it"}
            
            # Check if there's already a locked target
            if self.is_sensor_found("OverviewIconLockedTarget"):
                return True, {}

            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskOpenSpaceClick),
                "Failed to click on open space"
            ):
                return False, {"error": "Failed to click on open space"}
            
            self.interactor.EvePressKey("x")
            self.wait(1.0)
            
            # Check for asteroids of all sizes
            asteroid_sensors = [
                "OverviewIconAsteroidHuge",
                "OverviewIconAsteroidLarge",
                "OverviewIconAsteroidMedium",
                "OverviewIconAsteroidSmall"
            ]
            
            highest_asteroid = None
            highest_y = float('inf')  # Lower y value means higher on screen
            
            # Find the highest (lowest y value) asteroid
            for sensor_name in asteroid_sensors:
                asteroid_location = self.global_state.get(sensor_name)
                if asteroid_location is not None and asteroid_location["found"]:
                    asteroid_y = asteroid_location["position"]["y"]
                    if asteroid_y < highest_y:
                        highest_y = asteroid_y
                        highest_asteroid = asteroid_location
            
            # If no asteroid was found, return false
            if highest_asteroid is None:
                self.logger.error("No asteroids found in the overview window")
                return False, {"error": "No asteroids found"}
            
            # Click on the highest asteroid and then press control to lock it
            self.interactor.EveSafeClick(x=highest_asteroid["centerpoint"]["x"], y=highest_asteroid["centerpoint"]["y"])
            self.wait(1.0)
            self.interactor.EvePressKey("w")
            self.wait(1.0)
            self.interactor.EvePressKey(Key.ctrl)
            self.wait(10.0)
            
            # Verify that the target was locked
            if not self.is_sensor_found("OverviewIconLockedTarget"):
                self.logger.error("Failed to lock target after pressing control")
                return False, {"error": "Target lock failed"}
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskOverviewWindowTargetLockAsteroid.execute:")
            return False, {"error": str(e)}
