from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskDroneWindowDronesReturned import TaskDroneWindowDronesReturned
from Games.EveOnline.Tasks.TaskWarpToAsteroidField import TaskWarpToAsteroidField
from Games.EveOnline.Tasks.TaskDroneWindowDronesDeployed import TaskDroneWindowDronesDeployed
from Games.EveOnline.Tasks.TaskOverviewWindowTargetLockAsteroid import TaskOverviewWindowTargetLockAsteroid
from Games.EveOnline.Tasks.TaskWeaponsOnline import TaskWeaponsOnline
import logging

@TaskRegistry.register
class TaskMinePalasAsteroids(Task):
    """
    Task for mining asteroids at the Palas asteroid field.
    """
    def __init__(self, game_interactor=None):
        super().__init__("MinePalasAsteroids", game_interactor=game_interactor)
        self.weapon_sensor_name = "WeaponIconStripMiner1"
    
    def execute(self):
        """
        Execute the task sequence for mining at the Palas asteroid field.
        Returns (success, metadata) tuple.
        """
        try:
            # Warp to the asteroid field
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskWarpToAsteroidField),
                "Failed to warp to asteroid field"
            ):
                return False, {"error": "Failed to warp to asteroid field"}
            
            # Deploy drones
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskDroneWindowDronesDeployed),
                "Failed to deploy drones"
            ):
                return False, {"error": "Failed to deploy drones"}
            
            # Target and lock an asteroid
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskOverviewWindowTargetLockAsteroid),
                "Failed to target and lock asteroid"
            ):
                return False, {"error": "Failed to target and lock asteroid"}
            
            # Activate mining lasers
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskWeaponsOnline, weapon_sensor_name=self.weapon_sensor_name),
                "Failed to activate mining lasers"
            ):
                self.wait(10.0)
                return False, {"error": "Failed to activate mining lasers"}
            
            self.wait(10.0)
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskMinePalasAsteroids.execute:")
            return False, {"error": str(e)}
