from task import Task, TaskRegistry, CancelException
import logging
from Games.EveOnline.Tasks.TaskDroneWindowOpen import TaskDroneWindowOpen
from pynput.keyboard import Key
from Games.EveOnline.Tasks.TaskOpenSpaceClick import TaskOpenSpaceClick

@TaskRegistry.register
class TaskDroneWindowDronesDeployed(Task):
    """
    Task for opening a submenu in the location window.
    """
    def __init__(self, game_interactor=None, submenu_sensor_name: str = ""):
        super().__init__("DroneWindowDronesDeployed", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to open a submenu in the location window.
        Returns (success, metadata) tuple.
        """
        try:
            if not self.is_sensor_found("DroneWindowOpen"):
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskDroneWindowOpen),
                    "Drone window not found after opening it"
                ):
                    return False, {"error": "Drone window not found after opening it"}
            
            if self.is_sensor_found("DroneWindowDronesDeployed"):
                return True, {}

            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskOpenSpaceClick),
                "Open space not found after clicking on it"
            ):
                return False, {"error": "Open space not found after clicking on it"}
            
            self.interactor.EvePressKeyWithModifier(Key.shift, "f")
            self.wait(5.0)
            
            if not self.is_sensor_found("DroneWindowDronesDeployed"):
                self.logger.error("Drones not deployed after pressing Shift+F")
                return False, {"error": "Drones not deployed after pressing Shift+F"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskDroneWindowDronesDeployed.execute:")
            return False, {"error": str(e)}
