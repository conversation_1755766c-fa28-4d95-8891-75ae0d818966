from Games.EveOnline.Tasks.TaskLocationWindowSubmenuWarp import TaskLocationWindowSubmenuWarp
from Games.EveOnline.Tasks.TaskDepositMineralsAtPugspero import TaskDepositMineralsAtPugspero
from Games.EveOnline.Tasks.TaskInventoryWindowOpen import TaskInventory<PERSON>indowOpen
from Games.EveOnline.Tasks.TaskMineLowsecAsteroids import TaskMineLowsecAsteroids
from Games.EveOnline.Tasks.TaskInformationPopupClose import TaskInformationPopupClose
from task import Task, TaskRegistry, CancelException
import logging

@TaskRegistry.register
class TaskMiningLowSec(Task):
    """
    Task for mining in Eve Online. This is a composite task that runs other tasks.
    It first targets an asteroid and then performs mining operations.
    """
    def __init__(self, game_interactor=None):
        super().__init__("MiningLowSec", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the mining task sequence.
        Returns (success, metadata) tuple.
        """
        try:
            while True:
                # Create and run the asteroid targeting task
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskInformationPopupClose),
                    "Information popup not closed after clicking"
                ):
                    continue
                
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskInventoryWindowOpen),
                    "Inventory window not found after opening it"
                ):
                    continue
                
                # Check for full inventory
                if self.is_sensor_found("InventoryWindowEmptyInventory"):
                    self.logger.info("Inventory is full, depositing minerals at Pugspero")
                    
                    if not self.run_subtask_with_error_handling(
                        self.create_subtask(TaskLocationWindowSubmenuWarp, submenu_sensor_name="LocationWindowPugspero"),
                        "Failed to warp to Pugspero"
                    ):
                        continue
                    
                    if not self.run_subtask_with_error_handling(
                        self.create_subtask(TaskDepositMineralsAtPugspero),
                        "Failed to deposit minerals at Pugspero"
                    ):
                        continue
                
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskMineLowsecAsteroids),
                    "Failed to mine asteroids at Lowsec"
                ):
                    continue
                self.wait(1.0)

        except CancelException as ex:
            self.logger.info(f"MiningLowSec task was canceled: {str(ex)}")
            return False, {"error": str(ex)}
        except Exception as ex:
            logging.exception(f"Exception in TaskMiningLowSec.execute:")
            return False, {"error": f"Unexpected error: {str(ex)}"}
