from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskInventoryWindowOpen import TaskInventoryWindowOpen
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskInventoryWindowCompress(Task):
    """
    Task for compressing cargo in the inventory window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("InventoryWindowCompress", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to compress cargo in the inventory window.
        Returns (success, metadata) tuple.
        """
        try:
            # Find the inventory in the inventory window
            if not self.is_sensor_found("InventoryWindowFirstItem"):
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskInventoryWindowOpen),
                    "Inventory window not found after opening it"
                ):
                    return False, {"error": "Inventory window not found after opening it"}
                
                # Check again after opening inventory window
                if not self.is_sensor_found("InventoryWindowFirstItem"):
                    return False, {"error": "Inventory first item not found after opening inventory window"}
            
            inventoryLocation = self.global_state.get("InventoryWindowFirstItem")
            
            # Click on the inventory
            self.interactor.EveSafeClick(x=inventoryLocation["position"]["x"], y=inventoryLocation["position"]["y"])
            self.wait(0.5)
            
            # Select all items (Ctrl+A)
            self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
            self.interactor.EvePressKeyWithModifier(Key.cmd, "a")

            self.wait(0.5)

            #Right Click on first item
            if not self.is_sensor_found("InventoryWindowFirstItem"):
                return False, {"error": "Inventory first item not found after opening inventory window"}
            inventoryLocation = self.global_state.get("InventoryWindowFirstItem")
            self.interactor.EveSafeClickRight(x=inventoryLocation["position"]["x"], y=inventoryLocation["position"]["y"])
            self.wait(0.5)
            
            # Find the compress location
            if not self.is_sensor_found("InventoryWindowCompress"):
                self.logger.error("InventoryWindowCompress not found")
                return False, {"error": "Inventory compress button not found"}
            compressLocation = self.global_state.get("InventoryWindowCompress")
            
            # Click on the compress button
            self.interactor.EveSafeClick(x=compressLocation["position"]["x"], y=compressLocation["position"]["y"])
            self.wait(0.5)

            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskInventoryWindowCompress.execute:")
            return False, {"error": str(e)}
