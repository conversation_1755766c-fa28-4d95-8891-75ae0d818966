from task import Task, TaskRegistry, CancelException
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskInventoryWindowOpen(Task):
    """
    Task for opening the inventory window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("InventoryWindowOpen", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to open the inventory window.
        Returns (success, metadata) tuple.
        """
        try:
            if self.is_sensor_found("InventoryWindowOpen"):
                return True, {}
            
            self.interactor.EvePressKeyWithModifier(Key.alt, "c")
            self.wait(1.0)
            
            if not self.is_sensor_found("InventoryWindowOpen"):
                self.logger.error("Inventory window didn't appear after pressing Alt+C")
                return False, {"error": "Inventory window not found"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskInventoryWindowOpen.execute:")
            return False, {"error": str(e)}
