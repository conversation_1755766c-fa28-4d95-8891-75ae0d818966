from task import Task, TaskRegistry, CancelException
import logging

@TaskRegistry.register
class TaskCargoDepositWindowClose(Task):
    """
    Task for closing the cargo deposit window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("CargoDepositWindowClose", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to close the cargo deposit window.
        Returns (success, metadata) tuple.
        """
        try:
            # Find the cargo deposit window close button
            if not self.is_sensor_found("CargoDepositWindowCloseButton"):
                self.logger.error("CargoDepositWindowCloseButton not found")
                return True, {}
            
            closeButtonLocation = self.global_state.get("CargoDepositWindowCloseButton")
            
            # Click on the close button
            self.interactor.EveSafeClick(x=closeButtonLocation["centerpoint"]["x"], y=closeButtonLocation["centerpoint"]["y"])
            self.wait(1.0)
            
            # Verify that the window is closed
            if self.is_sensor_found("CargoDepositWindow"):
                self.logger.error("Cargo deposit window still open after clicking close button")
                return False, {"error": "Failed to close cargo deposit window"}
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskCargoDepositWindowClose.execute:")
            return False, {"error": str(e)}
