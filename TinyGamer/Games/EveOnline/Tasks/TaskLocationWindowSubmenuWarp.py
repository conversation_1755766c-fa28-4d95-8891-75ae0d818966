from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskLocationWindowSubmenuOpen import TaskLocationWindowSubmenuOpen
from Games.EveOnline.Tasks.TaskDroneWindowDronesReturned import TaskDroneWindowDronesReturned
import logging

@TaskRegistry.register
class TaskLocationWindowSubmenuWarp(Task):
    """
    Task for opening a submenu in the location window.
    """
    def __init__(self, game_interactor=None, submenu_sensor_name: str = ""):
        super().__init__("LocationWindowSubmenuWarp", game_interactor=game_interactor)
        self.submenu_sensor_name = submenu_sensor_name
    
    def execute(self):
        """
        Execute the task to warp to a location in the location window.
        Returns (success, metadata) tuple.
        """
        try:
            # Make sure drones are returned
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskDroneWindowDronesReturned),
                "Failed to return drones"
            ):
                return False, {"error": "Failed to return drones"}
            
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskLocationWindowSubmenuOpen, submenu_sensor_name=self.submenu_sensor_name),
                f"{self.submenu_sensor_name} not found after opening it"
            ):
                return False, {"error": f"{self.submenu_sensor_name} not found after opening it"}
            
            if not self.is_sensor_found("LocationWindowSubmenuWarp"):
                return True, {}
            
            submenuLocation = self.global_state.get("LocationWindowSubmenuWarp")
            
            self.interactor.EveSafeClick(x=submenuLocation["centerpoint"]["x"], y=submenuLocation["centerpoint"]["y"])
            self.wait(1.0)
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskLocationWindowSubmenuWarp.execute:")
            return False, {"error": str(e)}
