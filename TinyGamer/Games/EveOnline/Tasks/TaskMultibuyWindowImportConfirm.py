from task import Task, TaskRegistry, CancelException
import logging
from Games.EveOnline.Tasks.TaskMultibuyWindowOpen import TaskMultibuyWindowOpen

@TaskRegistry.register
class TaskMultibuyWindowImportConfirm(Task):
    """
    Task for clicking on the import confirm button in the multibuy window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("MultibuyWindowImportConfirm", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to click on the import confirm button in the multibuy window.
        Returns (success, metadata) tuple.
        """
        try:
            # First make sure the multibuy window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMultibuyWindowOpen),
                "Multibuy window not found after opening it"
            ):
                return False, {"error": "Multibuy window not found after opening it"}
            
            # Check if the import confirm button is visible
            if not self.is_sensor_found("MultibuyWindowImportConfirm"):
                self.logger.error("Import confirm button in multibuy window not found")
                return False, {"error": "Import confirm button in multibuy window not found"}
            
            # Get the import confirm button location
            import_confirm_button_location = self.global_state.get("MultibuyWindowImportConfirm")
            
            # Click on the import confirm button
            self.interactor.EveSafeClick(x=import_confirm_button_location["centerpoint"]["x"], y=import_confirm_button_location["centerpoint"]["y"])
            self.wait(1.0)
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskMultibuyWindowImportConfirm.execute:")
            return False, {"error": str(e)}
