from task import Task, TaskRegistry, CancelException
import logging
from Games.EveOnline.Tasks.TaskMultibuyWindowOpen import TaskMultibuyWindowOpen

@TaskRegistry.register
class TaskMultibuyWindowImportClick(Task):
    """
    Task for clicking on the import button in the multibuy window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("MultibuyWindowImportClick", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to click on the import button in the multibuy window.
        Returns (success, metadata) tuple.
        """
        try:
            # First make sure the multibuy window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMultibuyWindowOpen),
                "Multibuy window not found after opening it"
            ):
                return False, {"error": "Multibuy window not found after opening it"}
            
            # Check if the import button is visible
            if not self.is_sensor_found("MultibuyWindowImport"):
                self.logger.error("Import button in multibuy window not found")
                return False, {"error": "Import button in multibuy window not found"}
            
            # Get the import button location
            import_button_location = self.global_state.get("MultibuyWindowImport")
            
            # Click on the import button
            self.interactor.EveSafeClick(x=import_button_location["centerpoint"]["x"], y=import_button_location["centerpoint"]["y"])
            self.wait(1.0)
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskMultibuyWindowImportClick.execute:")
            return False, {"error": str(e)}
