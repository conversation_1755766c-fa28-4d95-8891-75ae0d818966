from task import Task, TaskRegistry, CancelException
import logging
from pynput.keyboard import Key

@TaskRegistry.register
class TaskReloadSensors(Task):
    """
    Task for reloading sensors.
    """
    def __init__(self, game_interactor=None):
        super().__init__("ReloadSensors", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to reload sensors.
        Returns (success, metadata) tuple.
        """
        try:
            if self.is_sensor_found("CapacitorWindowProbesLoaded"):
                return True, {}
                
            self.interactor.EvePressKeyWithModifier(Key.ctrl, "r")
            self.wait(10.0)
            
            if not self.is_sensor_found("CapacitorWindowProbesLoaded"):
                self.logger.error("Probes didn't reload after pressing Ctrl+R")
                return False, {"error": "Probes didn't reload"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskLocationWindowOpen.execute:")
            return False, {"error": str(e)}
