from task import Task, TaskRegistry, CancelException
import logging
from Games.EveOnline.Tasks.TaskLocationWindowSubmenuCargoDeposit import TaskLocationWindowSubmenuCargoDeposit
from Games.EveOnline.Tasks.TaskCargoDeposit import TaskCargoDeposit

@TaskRegistry.register
class TaskDepositMineralsAtPugspero(Task):
    """
    Task for depositing minerals at the Pugspero location.
    """
    def __init__(self, game_interactor=None):
        super().__init__("DepositMineralsAtPugspero", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to deposit minerals at Pugspero.
        Returns (success, metadata) tuple.
        """
        try:
            # Open the cargo deposit window
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskLocationWindowSubmenuCargoDeposit, submenu_sensor_name="LocationWindowPugspero"),
                "Failed to open cargo deposit window"
            ):
                return False, {"error": "Failed to open cargo deposit window"}
            
            # Deposit the cargo
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskCargoDeposit),
                "Failed to deposit cargo"
            ):
                return False, {"error": "Failed to deposit cargo"}
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskDepositMineralsAtPugspero.execute:")
            return False, {"error": str(e)}
