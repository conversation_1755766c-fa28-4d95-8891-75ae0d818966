from task import Task, TaskRegistry, CancelException
import logging

@TaskRegistry.register
class TaskDroneWindowOpen(Task):
    """
    Task for opening the drone window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("DroneWindowOpen", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to open the drone window.
        Returns (success, metadata) tuple.
        """
        try:
            if not self.is_sensor_found("DroneWindow"):
                self.logger.error("Drone window not found")
                return False, {"error": "Drone window not found"}

            droneWindowLocation = self.global_state.get("DroneWindow")
            self.interactor.EveSafeClick(x=droneWindowLocation["centerpoint"]["x"], y=droneWindowLocation["centerpoint"]["y"])
            self.wait(1.0)

            if not self.is_sensor_found("DroneWindowOpen"):
                self.logger.error("Drone window didn't appear after clicking on it")
                return False, {"error": "Drone window didn't appear after clicking on it"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskDroneWindowOpen.execute:")
            return False, {"error": str(e)}
