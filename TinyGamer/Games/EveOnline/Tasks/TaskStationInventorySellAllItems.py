from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskStationInventoryOpen import TaskStationInventoryOpen
from Games.EveOnline.Tasks.TaskStationInventoryOpenSubmenu import TaskStationInventoryOpenSubmenu
from Games.EveOnline.Tasks.TaskStationInventoryItemSell import TaskStationInventoryItemSell
from Games.EveOnline.Tasks.TaskStationInventoryClose import TaskStationInventoryClose
import pynput.keyboard
from pynput.keyboard import Key, KeyCode
import logging
import pyperclip
import os
import tempfile
import json
import time

@TaskRegistry.register
class TaskStationInventorySellAllItems(Task):
    """
    Task for selling all items in the station inventory.
    This is a composite task that runs other tasks in sequence.
    """
    def __init__(self, game_interactor=None):
        super().__init__("StationInventorySellAllItems", game_interactor=game_interactor)

    def execute(self):
        """
        Execute the task sequence for selling all items in the station inventory.
        Returns (success, metadata) tuple.
        """
        while True:
            try:
                # First make sure the station inventory window is open
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskStationInventoryOpen),
                    "Station inventory window not found after opening it"
                ):
                    return False, {"error": "Station inventory window not found after opening it"}

                # Check if there's a container for the first item
                has_container = self.is_sensor_found("StationInventoryWindowContainer")
                if has_container:
                    self.logger.info("Container found in station inventory")
                    return True, {"message": "Container found, manual handling required"}

                # Check if there's a first item
                if not self.is_sensor_found("StationInventoryWindowFirstItem"):
                    self.logger.info("No items found in station inventory")
                    return True, {"message": "No items found in station inventory"}

                # Get the first item location
                first_item_location = self.global_state.get("StationInventoryWindowFirstItem")

                # Click on the first item
                self.interactor.EveSafeClick(x=first_item_location["centerpoint"]["x"], y=first_item_location["centerpoint"]["y"])
                self.wait(0.5)

                # Clear the clipboard before copying
                try:
                    pyperclip.copy("")
                    self.logger.info("Cleared clipboard")
                except Exception as e:
                    self.logger.warning(f"Failed to clear clipboard: {str(e)}")

                # Copy the item name and quantity to clipboard (Alt+C)
                self.interactor.EvePressKeyWithModifier(Key.ctrl, "c")
                self.interactor.EvePressKeyWithModifier(Key.cmd, "c")
                self.wait(1.0)  # Increased wait time to ensure clipboard is updated

                # Get the item name and quantity from clipboard
                max_retries = 3
                clipboard_text = ""

                for retry in range(max_retries):
                    try:
                        clipboard_text = pyperclip.paste()
                        self.logger.info(f"Clipboard content (attempt {retry+1}): {clipboard_text}")

                        # Check if the clipboard contains what looks like an item and quantity
                        parts = clipboard_text.rsplit(" ", 1)
                        if len(parts) == 2 and parts[1].isdigit():
                            self.logger.info(f"Valid clipboard format detected on attempt {retry+1}")
                            break

                        # If not valid, try again
                        self.logger.warning(f"Invalid clipboard format on attempt {retry+1}, retrying...")
                        self.wait(0.5)
                        self.interactor.EvePressKeyWithModifier(Key.ctrl, "c")
                        self.interactor.EvePressKeyWithModifier(Key.cmd, "c")
                        self.wait(1.0)
                    except Exception as e:
                        self.logger.warning(f"Error reading clipboard on attempt {retry+1}: {str(e)}")
                        self.wait(0.5)

                if not clipboard_text:
                    self.logger.error("Failed to copy item information to clipboard after multiple attempts")
                    return False, {"error": "Failed to copy item information to clipboard after multiple attempts"}

                # Parse the clipboard text to get item name and quantity
                # Format is expected to be "ItemName Quantity"
                try:
                    # First, check if the clipboard text is too long (likely log data)
                    if len(clipboard_text) > 500:  # Arbitrary limit to catch log data
                        self.logger.error(f"Clipboard content too long, likely not item data. Length: {len(clipboard_text)}")
                        return False, {"error": "Clipboard content too long, likely not item data"}

                    # Try to extract item name and quantity using regex (primary method)
                    import re
                    matches = re.findall(r'(.*?)(\d+)$', clipboard_text.strip())
                    if matches:
                        item_name = matches[0][0].strip()
                        quantity = matches[0][1]
                        self.logger.info(f"Item: {item_name}, Quantity: {quantity}")
                    else:
                        # Try the alternative approach - splitting by last space
                        parts = clipboard_text.rsplit(" ", 1)
                        if len(parts) == 2 and parts[1].isdigit():
                            item_name = parts[0]
                            quantity = parts[1]
                            self.logger.info(f"Item (alternative parsing): {item_name}, Quantity: {quantity}")
                        else:
                            self.logger.error(f"Invalid clipboard format: {clipboard_text}")
                            return False, {"error": f"Invalid clipboard format: {clipboard_text}"}
                except Exception as e:
                    self.logger.error(f"Error parsing clipboard: {str(e)}")
                    return False, {"error": f"Error parsing clipboard: {str(e)}"}

                # Communicate with TinyMarket to get the appropriate sell price
                try:
                    # Create a request file for TinyMarket
                    temp_dir = tempfile.gettempdir()
                    request_file_path = os.path.join(temp_dir, "tinygamer_market_request.json")

                    request_data = {
                        "action": "get_sell_price",
                        "item_name": item_name,
                        "timestamp": time.time()
                    }

                    with open(request_file_path, "w") as f:
                        json.dump(request_data, f)

                    self.logger.info(f"Wrote market request to {request_file_path}")

                    # Wait for TinyMarket to process the request and provide a price
                    max_wait_time = 30  # seconds - increased to allow time for fetching market data
                    start_time = time.time()
                    price = None

                    response_file_path = os.path.join(temp_dir, "tinygamer_market_response.json")

                    while time.time() - start_time < max_wait_time:
                        if os.path.exists(response_file_path):
                            try:
                                with open(response_file_path, "r") as f:
                                    response_data = json.load(f)

                                # Check if the response is for the current item and has a price
                                if ("price" in response_data and
                                    "item_name" in response_data and
                                    response_data["item_name"] == item_name and
                                    "timestamp" in response_data and
                                    response_data["timestamp"] > request_data["timestamp"]):

                                    price = response_data["price"]
                                    self.logger.info(f"Got price from TinyMarket for {response_data['item_name']}: {price}")

                                    # Validate the price is a valid number
                                    try:
                                        price_float = float(price)
                                        if price_float <= 0:
                                            self.logger.warning(f"Received zero or negative price: {price_float}, using minimum price")
                                            price = "0.01"  # Set a minimum price
                                        else:
                                            # Format with exactly 2 decimal places
                                            price = f"{price_float:.2f}"
                                            self.logger.info(f"Validated and formatted price: {price}")
                                    except ValueError:
                                        self.logger.warning(f"Invalid price format received: {price}, using minimum price")
                                        price = "0.01"  # Default minimum price

                                    break
                                else:
                                    # Log why the response was rejected
                                    if "item_name" not in response_data:
                                        self.logger.warning("Response missing item_name field, waiting for valid response")
                                    elif response_data["item_name"] != item_name:
                                        self.logger.warning(f"Response is for different item: {response_data['item_name']}, expected: {item_name}")
                                    elif "timestamp" not in response_data:
                                        self.logger.warning("Response missing timestamp field, waiting for valid response")
                                    elif response_data["timestamp"] <= request_data["timestamp"]:
                                        self.logger.warning(f"Response timestamp {response_data['timestamp']} is older than request timestamp {request_data['timestamp']}")
                                    elif "price" not in response_data:
                                        self.logger.warning("Response missing price field, waiting for valid response")

                                    # Remove the stale response file to avoid reprocessing it
                                    try:
                                        os.remove(response_file_path)
                                        self.logger.info(f"Removed stale response file: {response_file_path}")
                                    except Exception as e:
                                        self.logger.warning(f"Failed to remove stale response file: {str(e)}")

                                    # Wait a bit before checking again
                                    self.wait(0.5)
                            except Exception as e:
                                self.logger.warning(f"Error reading response file: {str(e)}")

                        self.wait(0.5)

                    if price is None:
                        self.logger.error("Failed to get price from TinyMarket")
                        return False, {"error": "Failed to get price from TinyMarket"}
                except Exception as e:
                    self.logger.error(f"Error communicating with TinyMarket: {str(e)}")
                    return False, {"error": f"Error communicating with TinyMarket: {str(e)}"}

                # Open the submenu for the first item
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskStationInventoryOpenSubmenu),
                    "Failed to open submenu for first item"
                ):
                    return False, {"error": "Failed to open submenu for first item"}

                self.wait(3.0)

                # Sell the item with the specified quantity and price
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskStationInventoryItemSell, quantity=quantity, price=price),
                    "Failed to sell item"
                ):
                    return False, {"error": "Failed to sell item"}

                # Write completion status to temp file
                self._write_completion_status(True, f"Sold item: {item_name}, Quantity: {quantity}, Price: {price}")

                # Close the station inventory window
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskStationInventoryClose),
                    "Failed to close station inventory window"
                ):
                    return False, {"error": "Failed to close station inventory window"}

            except CancelException as ex:
                self._write_completion_status(False, str(ex))
                return False, {"error": str(ex)}
            except Exception as e:
                logging.exception(f"Exception in TaskStationInventorySellAllItems.execute:")
                self._write_completion_status(False, str(e))
                return False, {"error": str(e)}

    def _write_completion_status(self, success: bool, message: str):
        """
        Write task completion status to a temporary file for TinyTrader to read.

        Args:
            success: Whether the task completed successfully
            message: Status message
        """
        try:
            import tempfile
            import os
            import json
            import time

            # Get the temp file path
            temp_dir = tempfile.gettempdir()
            status_file_path = os.path.join(temp_dir, "tinygamer_status.json")

            # Create status data
            status_data = {
                "task": self.name,
                "success": success,
                "message": message,
                "timestamp": time.time()
            }

            # Write status to file
            with open(status_file_path, "w") as f:
                json.dump(status_data, f)

            self.logger.info(f"Wrote completion status to {status_file_path}: {success}, {message}")
        except Exception as e:
            self.logger.error(f"Error writing completion status: {str(e)}")
