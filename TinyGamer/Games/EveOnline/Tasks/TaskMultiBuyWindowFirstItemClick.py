from task import Task, TaskRegistry, CancelException
import logging
from Games.EveOnline.Tasks.TaskMultibuyWindowOpen import TaskMultibuyWindowOpen

@TaskRegistry.register
class TaskMultiBuyWindowFirstItemClick(Task):
    """
    Task for clicking on the first item in the multibuy window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("MultiBuyWindowFirstItemClick", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to click on the first item in the multibuy window.
        Returns (success, metadata) tuple.
        """
        try:
            # First make sure the multibuy window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMultibuyWindowOpen),
                "Multibuy window not found after opening it"
            ):
                return False, {"error": "Multibuy window not found after opening it"}
            
            # Check if the first item is visible
            if not self.is_sensor_found("MultibuyWindowFirstItem"):
                self.logger.error("First item in multibuy window not found")
                return False, {"error": "First item in multibuy window not found"}
            
            # Get the first item location
            first_item_location = self.global_state.get("MultibuyWindowFirstItem")
            
            # Click on the first item
            self.interactor.EveSafeClick(x=first_item_location["centerpoint"]["x"], y=first_item_location["centerpoint"]["y"])
            self.wait(1.0)
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskMultiBuyWindowFirstItemClick.execute:")
            return False, {"error": str(e)}
