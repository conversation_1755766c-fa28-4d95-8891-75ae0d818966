from task import Task, TaskRegistry, CancelException
import logging

@TaskRegistry.register
class TaskLocationWindowOpen(Task):
    """
    Task for opening the location window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("LocationWindowOpen", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to open the location window.
        Returns (success, metadata) tuple.
        """
        try:
            if self.is_sensor_found("LocationWindow"):
                return True, {}
                
            self.interactor.EvePressKey("l")
            self.wait(1.0)
            
            if not self.is_sensor_found("LocationWindow"):
                self.logger.error("Location window didn't appear after pressing L")
                return False, {"error": "Location window not found"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskLocationWindowOpen.execute:")
            return False, {"error": str(e)}
