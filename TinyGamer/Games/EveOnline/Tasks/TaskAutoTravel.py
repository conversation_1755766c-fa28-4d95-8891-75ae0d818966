from task import Task, TaskRegistry, CancelException
from pynput.keyboard import Key
from Games.EveOnline.Tasks.TaskOpenSpace<PERSON>lick import TaskOpenSpace<PERSON>lick

import logging
import time

@TaskRegistry.register
class TaskAutoTravel(Task):
    """
    Task for auto-traveling by holding down 'd' key while clicking on the location
    returned by the autotravel sensor. This task loops infinitely until canceled.
    """
    def __init__(self, game_interactor=None):
        super().__init__("AutoTravel", game_interactor=game_interactor)

    def execute(self):
        """
        Execute the auto travel task:
        1. Hold down 'd' key
        2. Click on the location returned by the autotravel sensor
        3. Loop infinitely until canceled

        Returns (success, metadata) tuple.
        """
        try:
            self.logger.info("Starting auto travel task (hold 'd' + click on autotravel location)")

            while True:
                # Track sensor verification with local variables
                found = self.is_sensor_found("OverviewWindowDestinationNameSensor")

                # Initialize sensor_start_time if not already defined
                if not hasattr(self, '_sensor_start_time'):
                    self._sensor_start_time = None

                # Check if sensor is found
                if not found:
                    # Reset the start time when sensor is not found
                    self._sensor_start_time = None
                    continue

                # If this is the first time we found the sensor, store the current time
                current_time = time.time()
                if self._sensor_start_time is None:
                    self._sensor_start_time = current_time
                    self.logger.info("Sensor found, starting 10-second verification")
                    self.wait(0.5)  # Small wait before checking again
                    continue

                # Check if sensor has been found continuously for 10 seconds
                elapsed_time = current_time - self._sensor_start_time
                if elapsed_time < 10.0:
                    self.logger.info(f"Sensor verification: {elapsed_time:.1f}/10.0 seconds")
                    self.wait(0.5)  # Small wait before checking again
                    continue

                # If we reach here, sensor has been found for 10+ seconds
                self.logger.info("Sensor verified for 10+ seconds, continuing")

                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskOpenSpaceClick),
                    "Failed to click open space"
                ):
                    continue

                locationFound = False

                if self.is_sensor_found("OverviewWindowDestinationSensor1"):
                    locationFound = True
                    overviewWindowTravelLocation = self.global_state.get("OverviewWindowDestinationSensor1")
                if self.is_sensor_found("OverviewWindowDestinationSensor2"):
                    locationFound = True
                    overviewWindowTravelLocation = self.global_state.get("OverviewWindowDestinationSensor2")
                if self.is_sensor_found("OverviewWindowDestinationSensor1") and self.is_sensor_found("OverviewWindowDestinationSensor2"):
                    locationFound = True
                    overviewWindowTravelLocation1 = self.global_state.get("OverviewWindowDestinationSensor1")
                    overviewWindowTravelLocation2 = self.global_state.get("OverviewWindowDestinationSensor2")
                    confidence1 = overviewWindowTravelLocation1["confidence"]
                    confidence2 = overviewWindowTravelLocation2["confidence"]
                    if confidence1 > confidence2:
                        overviewWindowTravelLocation = overviewWindowTravelLocation1
                    else:
                        overviewWindowTravelLocation = overviewWindowTravelLocation2

                if locationFound:
                    self.interactor.hold_key('d')
                    self.logger.info("Holding 'd' key")

                    # Wait a moment to ensure the key is registered
                    self.wait(0.2)

                    # Click on the autotravel location
                    self.interactor.EveSafeClick(x=overviewWindowTravelLocation["centerpoint"]["x"], y=overviewWindowTravelLocation["centerpoint"]["y"])

                    self.logger.info("Clicked on autotravel location")

                    # Wait a moment before releasing the key
                    self.wait(0.5)

                    # Release the 'd' key
                    self.interactor.release_key('d')
                    self.logger.info("Released 'd' key")

                    # Wait a short time before checking again
                    self.wait(0.5)
                    self.interactor.EvePressKey(Key.f1)
                    self.wait(40.0)
                else:
                    self.logger.info("No autotravel location found, waiting...")
                    self.wait(1.0)

        except CancelException as ex:
            # Make sure to release the 'd' key if the task is canceled
            try:
                self.interactor.release_key('d')
                self.logger.info("Released 'd' key due to task cancellation")
            except:
                pass
            return False, {"error": str(ex)}
        except Exception as e:
            # Make sure to release the 'd' key if there's an error
            try:
                self.interactor.release_key('d')
                self.logger.info("Released 'd' key due to error")
            except:
                pass
            logging.exception(f"Exception in TaskAutoTravel.execute:")
            return False, {"error": str(e)}