from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskMarketBuyOrderClick import TaskMarketBuyOrderClick
import logging

@TaskRegistry.register
class TaskBuyOrderPlaceOrder(Task):
    """
    Task for placing the buy order by clicking the place order button.
    """
    def __init__(self, game_interactor=None):
        super().__init__("BuyOrderPlaceOrder", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to place the buy order.
        Returns (success, metadata) tuple.
        """
        try:
            # First make sure the buy order window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMarketBuyOrderClick),
                "Buy order window not found after opening it"
            ):
                return False, {"error": "Buy order window not found after opening it"}
            
            # Check if the place order button is visible
            if not self.is_sensor_found("BuyOrderWindow"):
                self.logger.error("Place order button in buy order window not found")
                return False, {"error": "Place order button in buy order window not found"}
            
            # Get the place order button location
            place_order_button_location = self.global_state.get("BuyOrderWindow")
            
            # Click on the place order button
            self.interactor.EveSafeClick(x=place_order_button_location["centerpoint"]["x"], y=place_order_button_location["centerpoint"]["y"])
            self.wait(1.0)
            
            # Verify that the buy order window is now closed
            if self.is_sensor_found("BuyOrderWindow"):
                self.logger.error("Buy order window still open after placing order")
                return False, {"error": "Buy order window still open after placing order"}
                
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskBuyOrderPlaceOrder.execute:")
            return False, {"error": str(e)}
