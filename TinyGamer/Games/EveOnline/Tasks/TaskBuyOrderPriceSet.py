from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskMarketBuyOrderClick import TaskMarketBuyOrderClick
import logging
from pynput.keyboard import Key

@TaskRegistry.register
class TaskBuyOrderPriceSet(Task):
    """
    Task for setting the price in the buy order window.
    """
    def __init__(self, price=None, game_interactor=None):
        super().__init__("BuyOrderPriceSet", game_interactor=game_interactor)
        self.price = price

    def execute(self):
        """
        Execute the task to set the price in the buy order window.
        Returns (success, metadata) tuple.
        """
        try:
            # First make sure the buy order window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMarketBuyOrderClick),
                "Buy order window not found after opening it"
            ):
                return False, {"error": "Buy order window not found after opening it"}

            # Check if the price input field is visible
            if not self.is_sensor_found("BuyOrderWindowPrice"):
                self.logger.error("Price input field in buy order window not found")
                return False, {"error": "Price input field in buy order window not found"}

            # Get the price input field location
            price_field_location = self.global_state.get("BuyOrderWindowPrice")

            # Click on the price input field
            self.interactor.EveSafeClick(x=price_field_location["centerpoint"]["x"], y=price_field_location["centerpoint"]["y"])
            self.wait(0.5)

            # Select all items (Ctrl+A)
            self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
            self.interactor.EvePressKeyWithModifier(Key.cmd, "a")
            self.wait(0.5)

            # Type the price
            if self.price is not None:
                # Log the price for debugging
                self.logger.info(f"Setting price: {self.price}")

                # Use the specialized price typing method if available
                if hasattr(self.interactor, 'type_price'):
                    self.logger.info("Using specialized price typing method")
                    self.interactor.type_price(self.price)
                else:
                    # Fall back to regular typing with explicit formatting
                    try:
                        # Format with two decimal places
                        formatted_price = f"{float(self.price):.2f}"
                        self.logger.info(f"Formatted price: {formatted_price}")
                        self.interactor.type_string(formatted_price)
                    except ValueError:
                        # If conversion fails, use as is
                        self.logger.warning(f"Could not format price, using as is: {self.price}")
                        self.interactor.type_string(str(self.price))

                self.wait(1.0)  # Increased wait time after typing price
                return True, {}
            else:
                self.logger.error("No price provided")
                return False, {"error": "No price provided"}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskBuyOrderPriceSet.execute:")
            return False, {"error": str(e)}
