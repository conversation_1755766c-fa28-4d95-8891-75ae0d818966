from task import Task, TaskRegistry, CancelException
import logging

@TaskRegistry.register
class TaskInformationPopupClose(Task):
    """
    Task for closing the information popup.
    """
    def __init__(self, game_interactor=None):
        super().__init__("InformationPopupClose", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to close the information popup.
        Returns (success, metadata) tuple.
        """
        try:
            if self.is_sensor_found("PopupInformationOkayButton"):
                informationPopupCloseLocation = self.global_state.get("PopupInformationOkayButton")
                self.interactor.EveSafeClick(x=informationPopupCloseLocation["centerpoint"]["x"], y=informationPopupCloseLocation["centerpoint"]["y"])
                self.wait(1.0)
            
            if not self.is_sensor_found("PopupInformationOkayButton"):
                return True, {}
            else:
                self.logger.error("Information popup didn't close after clicking")
                return False, {"error": "Information popup didn't close after clicking"}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskLocationWindowClose.execute:")
            return False, {"error": str(e)}
