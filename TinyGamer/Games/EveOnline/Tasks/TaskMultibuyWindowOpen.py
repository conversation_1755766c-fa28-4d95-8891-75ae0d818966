from task import Task, TaskRegistry, CancelException
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskMultibuyWindowOpen(Task):
    """
    Task for opening the multibuy window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("MultibuyWindowOpen", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to open the multibuy window.
        Returns (success, metadata) tuple.
        """
        try:
            if self.is_sensor_found("MultibuyWindow"):
                return True, {}
            
            self.interactor.EvePressKeyWithModifier(Key.shift, "j")
            self.wait(1.0)
            
            if not self.is_sensor_found("MultibuyWindow"):
                self.logger.error("Multibuy window didn't appear after pressing Shift+J")
                return False, {"error": "Multibuy window not found"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskMultibuyWindowOpen.execute:")
            return False, {"error": str(e)}
