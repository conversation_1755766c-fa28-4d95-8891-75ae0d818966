from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskOpenMarket import TaskOpenMarket
import logging

@TaskRegistry.register
class TaskMarketBuyOrderClick(Task):
    """
    Task for clicking on the buy order button in the market window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("MarketBuyOrderClick", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to click on the buy order button in the market window.
        Returns (success, metadata) tuple.
        """
        try:
            # if buy order window is already open, we're done
            if self.is_sensor_found("BuyOrderWindow"):
                return True, {}

            # First make sure the market window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskOpenMarket),
                "Market window not found after opening it"
            ):
                return False, {"error": "Market window not found after opening it"}
            
            # Check if the buy order button is visible
            if not self.is_sensor_found("MarketWindowPlaceBuyOrder"):
                self.logger.error("Buy order button in market window not found")
                return False, {"error": "Buy order button in market window not found"}
            
            # Get the buy order button location
            buy_order_button_location = self.global_state.get("MarketWindowPlaceBuyOrder")
            
            # Click on the buy order button
            self.interactor.EveSafeClick(x=buy_order_button_location["centerpoint"]["x"], y=buy_order_button_location["centerpoint"]["y"])
            self.wait(1.0)
            
            # Verify that the buy order window is now open
            if not self.is_sensor_found("BuyOrderWindow"):
                self.logger.error("Buy order window didn't appear after clicking the button")
                return False, {"error": "Buy order window not found"}
                
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskMarketBuyOrderClick.execute:")
            return False, {"error": str(e)}
