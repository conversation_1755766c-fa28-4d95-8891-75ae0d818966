from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskLocationWindowOpen import TaskLocationWindowOpen
from Games.EveOnline.Tasks.TaskLocationWindowClose import TaskLocationWindowClose
import logging

@TaskRegistry.register
class TaskLocationWindowSubmenuOpen(Task):
    """
    Task for opening a submenu in the location window.
    """
    def __init__(self, game_interactor=None, submenu_sensor_name: str = ""):
        super().__init__("LocationWindowSubmenuOpen", game_interactor=game_interactor)
        self.submenu_sensor_name = submenu_sensor_name
    
    def execute(self):
        """
        Execute the task to open a submenu in the location window.
        Returns (success, metadata) tuple.
        """
        try:
            #First close the location window
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskLocationWindowClose),
                "Location window not closed after closing it"
            ):
                return False, {"error": "Location window not closed after closing it"}
            
            #then reopen location window
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskLocationWindowOpen),
                "Location window not found after opening it"
            ):
                return False, {"error": "Location window not found after opening it"}
            
            if not self.is_sensor_found(self.submenu_sensor_name):
                self.logger.error(f"Sensor {self.submenu_sensor_name} not found")
                return False, {"error": f"Sensor {self.submenu_sensor_name} not found"}
            
            submenuLocation = self.global_state.get(self.submenu_sensor_name)
            
            self.interactor.EveSafeClickRight(x=submenuLocation["centerpoint"]["x"], y=submenuLocation["centerpoint"]["y"])
            self.wait(1.0)
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskLocationWindowSubmenuOpen.execute:")
            return False, {"error": str(e)}
