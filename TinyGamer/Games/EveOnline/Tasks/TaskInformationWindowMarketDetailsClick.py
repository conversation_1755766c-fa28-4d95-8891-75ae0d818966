from task import Task, TaskRegistry, CancelException
import logging

@TaskRegistry.register
class TaskInformationWindowMarketDetailsClick(Task):
    """
    Task for clicking on the market details button in the information window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("InformationWindowMarketDetailsClick", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to click on the market details button in the information window.
        Returns (success, metadata) tuple.
        """
        try:
            # Check if the market details button is visible
            if not self.is_sensor_found("InformationWindowMarketDetails"):
                self.logger.error("Market details button in information window not found")
                return False, {"error": "Market details button in information window not found"}
            
            # Get the market details button location
            market_details_button_location = self.global_state.get("InformationWindowMarketDetails")
            
            # Click on the market details button
            self.interactor.EveSafeClick(x=market_details_button_location["centerpoint"]["x"], y=market_details_button_location["centerpoint"]["y"])
            self.wait(1.0)
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskInformationWindowMarketDetailsClick.execute:")
            return False, {"error": str(e)}
