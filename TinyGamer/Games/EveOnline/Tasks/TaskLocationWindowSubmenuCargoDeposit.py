from Games.EveOnline.Tasks.TaskLocationWindowSubmenuOpen import TaskLocationWindowSubmenuOpen
from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskLocationWindowOpen import TaskLocationWindowOpen
import logging

@TaskRegistry.register
class TaskLocationWindowSubmenuCargoDeposit(Task):
    """
    Task for opening the cargo deposit submenu in the location window.
    """
    def __init__(self, game_interactor=None, submenu_sensor_name: str = ""):
        super().__init__("LocationWindowSubmenuCargoDeposit", game_interactor=game_interactor)
        self.submenu_sensor_name = submenu_sensor_name
    
    def execute(self):
        """
        Execute the task to open the cargo deposit submenu in the location window.
        Returns (success, metadata) tuple.
        """
        try:
            # Make sure the location window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskLocationWindowSubmenuOpen, submenu_sensor_name=self.submenu_sensor_name),
                "Location window not found after opening it"
            ):
                return False, {"error": "Location window not found after opening it"}
            
            # Find the cargo deposit option
            if not self.is_sensor_found("LocationWindowCargoDepositOpen"):
                self.logger.error("LocationWindowCargoDepositOpen not found")
                return False, {"error": "LocationWindowCargoDepositOpen not found"}
            
            cargoDepositLocation = self.global_state.get("LocationWindowCargoDepositOpen")
            
            # Left-click on the cargo deposit option
            self.interactor.EveSafeClick(x=cargoDepositLocation["centerpoint"]["x"], y=cargoDepositLocation["centerpoint"]["y"])
            self.wait(1.0)
            
            # Verify that the cargo deposit window is now open
            if not self.is_sensor_found("CargoDepositWindow"):
                self.logger.error("CargoDepositWindow not found after clicking")
                return False, {"error": "CargoDepositWindow not found"}
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskLocationWindowSubmenuCargoDeposit.execute:")
            return False, {"error": str(e)}
