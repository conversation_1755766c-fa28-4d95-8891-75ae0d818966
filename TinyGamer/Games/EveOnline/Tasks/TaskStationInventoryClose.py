from task import Task, TaskRegistry, CancelException
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskStationInventoryClose(Task):
    """
    Task for closing the station inventory window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("StationInventoryClose", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to close the station inventory window.
        Returns (success, metadata) tuple.
        """
        try:
            if not self.is_sensor_found("StationInventoryWindowOpen"):
                return True, {}
            
            self.interactor.EvePressKeyWithModifier(Key.alt, "c")
            self.wait(1.0)
            
            if self.is_sensor_found("StationInventoryWindowOpen"):
                self.logger.error("Station inventory window didn't close after pressing Alt+C")
                return False, {"error": "Station inventory window didn't close after pressing Alt+C"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskStationInventoryClose.execute:")
            return False, {"error": str(e)}

