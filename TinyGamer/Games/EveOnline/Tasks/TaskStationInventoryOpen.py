from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskOpenSpaceClick import TaskOpenSpace<PERSON>lick
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskStationInventoryOpen(Task):
    """
    Task for opening the station inventory window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("StationInventoryOpen", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to open the station inventory window.
        Returns (success, metadata) tuple.
        """
        try:
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskOpenSpaceClick),
                "Station inventory window not found after opening it"
            ):
                return False, {"error": "Station inventory window not found after opening it"}
            if self.is_sensor_found("StationInventoryWindowOpen"):
                return True, {}
            
            self.interactor.EvePressKeyWithModifier(Key.alt, "c")
            self.wait(1.0)
            
            if not self.is_sensor_found("StationInventoryWindowOpen"):
                self.logger.error("Station inventory window didn't appear after pressing Alt+C")
                return False, {"error": "Station inventory window not found"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskStationInventoryOpen.execute:")
            return False, {"error": str(e)}
