from task import Task, TaskRegistry, CancelException
import logging

@TaskRegistry.register
class TaskOverviewMiningTabSelect(Task):
    """
    Task for selecting the mining tab in the overview window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("OverviewMiningTabSelect", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to select the mining tab in the overview window.
        Returns (success, metadata) tuple.
        """
        try:
            # Check if the mining tab is already selected
            if self.is_sensor_found("OverviewWindowMiningTabSelected"):
                return True, {}
            
            # Find the mining tab button
            if not self.is_sensor_found("OverviewWindowMiningTab"):
                self.logger.error("Mining tab not found in the overview window")
                return False, {"error": "Mining tab not found"}
            
            miningTabLocation = self.global_state.get("OverviewWindowMiningTab")
            
            # Click on the mining tab button
            self.interactor.EveSafeClick(x=miningTabLocation["centerpoint"]["x"], y=miningTabLocation["centerpoint"]["y"])
            self.wait(1.0)
            
            # Verify the mining tab is now selected
            if not self.is_sensor_found("OverviewWindowMiningTabSelected"):
                self.logger.error("Mining tab didn't become selected after clicking")
                return False, {"error": "Mining tab selection failed"}
                
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskOverviewMiningTabSelect.execute:")
            return False, {"error": str(e)}
