from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskMarketBuyOrderClick import TaskMarketBuyOrderClick
import logging
from pynput.keyboard import Key

@TaskRegistry.register
class TaskBuyOrderQuantitySet(Task):
    """
    Task for setting the quantity in the buy order window.
    """
    def __init__(self, quantity=None, game_interactor=None):
        super().__init__("BuyOrderQuantitySet", game_interactor=game_interactor)
        self.quantity = quantity
    
    def execute(self):
        """
        Execute the task to set the quantity in the buy order window.
        Returns (success, metadata) tuple.
        """
        try:
            # First make sure the buy order window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskMarketBuyOrderClick),
                "Buy order window not found after opening it"
            ):
                return False, {"error": "Buy order window not found after opening it"}
            
            # Check if the quantity input field is visible
            if not self.is_sensor_found("BuyOrderWindowQuantity"):
                self.logger.error("Quantity input field in buy order window not found")
                return False, {"error": "Quantity input field in buy order window not found"}
            
            # Get the quantity input field location
            quantity_field_location = self.global_state.get("BuyOrderWindowQuantity")
            
            # Click on the quantity input field
            self.interactor.EveSafeClick(x=quantity_field_location["centerpoint"]["x"], y=quantity_field_location["centerpoint"]["y"])
            self.wait(0.5)
            
            # Select all items (Ctrl+A)
            self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
            self.interactor.EvePressKeyWithModifier(Key.cmd, "a")
            self.wait(0.5)
            
            # Type the quantity
            if self.quantity is not None:
                self.interactor.type_string(str(self.quantity))
                self.wait(0.5)
                return True, {}
            else:
                self.logger.error("No quantity provided")
                return False, {"error": "No quantity provided"}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskBuyOrderQuantitySet.execute:")
            return False, {"error": str(e)}
