from Games.EveOnline.Tasks.TaskOpenSpaceClick import <PERSON><PERSON>pen<PERSON><PERSON><PERSON><PERSON>
from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskCargoDepositWindowClose import TaskCargoDepositWindowClose
from Games.EveOnline.Tasks.TaskInventoryWindowOpen import TaskInventoryWindowOpen
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskCargoDeposit(Task):
    """
    Task for depositing cargo from inventory to cargo deposit window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("CargoDeposit", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to deposit cargo.
        Returns (success, metadata) tuple.
        """
        try:
            # Find the inventory in the inventory window
            if not self.is_sensor_found("InventoryWindowFirstItem"):
                if not self.run_subtask_with_error_handling(
                    self.create_subtask(TaskInventoryWindowOpen),
                    "Inventory window not found after opening it"
                ):
                    return False, {"error": "Inventory window not found after opening it"}
                
                # Check again after opening inventory window
                if not self.is_sensor_found("InventoryWindowFirstItem"):
                    return False, {"error": "Inventory first item not found after opening inventory window"}
            
            inventoryLocation = self.global_state.get("InventoryWindowFirstItem")
            
            # Click on the inventory
            self.interactor.EveSafeClick(x=inventoryLocation["position"]["x"], y=inventoryLocation["position"]["y"])
            self.wait(0.5)
            
            # Select all items (Ctrl+A)
            self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
            self.interactor.EvePressKeyWithModifier(Key.cmd, "a")

            self.wait(0.5)
            
            # Find the cargo deposit drop location
            if not self.is_sensor_found("CargoDepositWindowDropLocation"):
                self.logger.error("CargoDepositWindowDropLocation not found")
                return False, {"error": "Cargo deposit drop location not found"}
            
            dropLocation = self.global_state.get("CargoDepositWindowDropLocation")
            
            # Perform drag and drop operation
            self.interactor.click_down()  # Mouse down at current position (inventory)
            self.wait(0.5)
            self.interactor.move_to(x=dropLocation["position"]["x"], y=dropLocation["position"]["y"])  # Move to drop location
            self.wait(0.5)
            self.interactor.click_up()  # Mouse up at drop location
            self.wait(1.0)

            # Click on the inventory
            self.interactor.EveSafeClick(x=inventoryLocation["position"]["x"], y=inventoryLocation["position"]["y"])
            self.wait(0.5)

            # Perform drag and drop operation
            self.interactor.click_down()  # Mouse down at current position (inventory)
            self.wait(0.5)
            self.interactor.move_to(x=dropLocation["position"]["x"], y=dropLocation["position"]["y"])  # Move to drop location
            self.wait(0.5)
            self.interactor.click_up()  # Mouse up at drop location
            self.wait(1.0)

            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskOpenSpaceClick),
                "Failed to click on open space after deposit"
            ):
                return False, {"error": "Failed to click on open space after deposit"}

            self.wait(1.0)
            
            # Check if transfer button is ready
            if not self.is_sensor_found("CargoDepositWindowTransferButtonReady"):
                self.logger.error("CargoDepositWindowTransferButtonReady not detected")
                return False, {"error": "Transfer button not ready after drag and drop"}
            
            transferButtonLocation = self.global_state.get("CargoDepositWindowTransferButtonReady")
            
            # Click on transfer button
            self.interactor.EveSafeClick(x=transferButtonLocation["position"]["x"], y=transferButtonLocation["position"]["y"])
            self.wait(1.0)
            
            # Check if transfer is complete (button is dim)
            if not self.is_sensor_found("CargoDepositWindowTransferButtonDim"):
                self.logger.error("CargoDepositWindowTransferButtonDim not detected")
                return False, {"error": "Transfer not completed"}
            
            # Close the cargo deposit window
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskCargoDepositWindowClose),
                "Failed to close cargo deposit window"
            ):
                return False, {"error": "Failed to close cargo deposit window"}
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskCargoDeposit.execute:")
            return False, {"error": str(e)}
