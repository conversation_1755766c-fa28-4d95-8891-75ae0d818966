from Games.EveOnline.Tasks.TaskLocationWindowClose import TaskLocationWindowClose
from task import Task, TaskRegistry, CancelException
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskWeaponsOnline(Task):
    """
    Task for bringing weapons online.
    """
    def __init__(self, game_interactor=None, weapon_sensor_name: str = ""):
        super().__init__("WeaponsOnline", game_interactor=game_interactor)
        self.weapon_sensor_name = weapon_sensor_name
    
    def execute(self):
        """
        Execute the task to bring weapons online.
        Returns (success, metadata) tuple.
        """
        try:
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskLocationWindowClose),
                "Location window not closed after closing it"
            ):
                return False, {"error": "Location window not closed after closing it"}
            
            weapon_data = self.global_state.get(self.weapon_sensor_name)
            
            matches_count = weapon_data.get("matches_count", 0)
            # Take action based on the matches count
            if matches_count == 0:
                # No weapons online, press f1 then f2
                self.interactor.EvePressKey(Key.f1)
                self.wait(0.5)
                self.interactor.EvePressKey(Key.f2)
                self.wait(10.0)
            elif matches_count == 1:
                # One weapon online, press f2
                self.interactor.EvePressKey(Key.f2)
                self.wait(10.0)
            elif matches_count == 2:
                # Both weapons online, return success
                return True, {}
            else:
                # Unexpected matches count
                self.logger.error(f"Unexpected matches count: {matches_count}")
                return True, {"error": f"Unexpected matches count: {matches_count}"}
            
            # Verify the weapons are online after taking action
            weapon_data = self.global_state.get(self.weapon_sensor_name)
            if weapon_data is None or weapon_data.get("matches_count", 0) != 2:
                self.logger.error("Failed to bring weapons online")
                return False, {"error": "Failed to bring weapons online"}
            
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskWeaponsOnline.execute:")
            return False, {"error": str(e)}
