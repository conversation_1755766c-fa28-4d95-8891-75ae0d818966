from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskLocationWindowSubmenuWarp import TaskLocationWindowSubmenuWarp
from Games.EveOnline.Tasks.TaskLocationWindowSubmenuOpen import TaskLocationWindowSubmenuOpen
import logging

@TaskRegistry.register
class TaskWarpToAsteroidField(Task):
    """
    Task for warping to the asteroid field.
    """
    def __init__(self, game_interactor=None):
        super().__init__("WarpToAsteroidField", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to warp to the asteroid field.
        Returns (success, metadata) tuple.
        """
        try:
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskLocationWindowSubmenuOpen, submenu_sensor_name="LocationWindowAsteroid"),
                "Failed to open location window"
            ):
                return False, {"error": "Failed to open location window"}

            # Check if the warp submenu option is available
            if not self.is_sensor_found("LocationWindowSubmenuWarp"):
                self.logger.info("LocationWindowSubmenuWarp not found, assuming we're already at the asteroid field")
                return True, {}
            
            # Warp to the asteroid field
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskLocationWindowSubmenuWarp, submenu_sensor_name="LocationWindowAsteroid"),
                "Failed to warp to asteroid field"
            ):
                return False, {"error": "Failed to warp to asteroid field"}
            
            self.wait(10.0) 
            
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskLocationWindowSubmenuOpen, submenu_sensor_name="LocationWindowAsteroid"),
                "Failed to open location window"
            ):
                return False, {"error": "Failed to open location window"}
            
            # Check if the warp submenu option is available
            if not self.is_sensor_found("LocationWindowSubmenuWarp"):
                self.logger.info("We've arrived at the asteroid field")
                return True, {}
            
            return False, {"error": "Failed to warp to asteroid field2"}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskWarpToAsteroidField.execute:")
            return False, {"error": str(e)}
