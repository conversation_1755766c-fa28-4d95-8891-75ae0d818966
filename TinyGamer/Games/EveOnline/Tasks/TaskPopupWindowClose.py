from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskOpenSpaceClick import Task<PERSON>pen<PERSON>pace<PERSON>lick
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskPopupWindowClose(Task):
    """
    Task for closing the popup window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("PopupWindowClose", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to close the popup window.
        Returns (success, metadata) tuple.
        """
        try:
            if not self.is_sensor_found("PopupWindow"):
                return True, {}
            
            popup_window_location = self.global_state.get("PopupWindow")

            # Click on the popup window
            self.interactor.EveSafeClick(x=popup_window_location["centerpoint"]["x"], y=popup_window_location["centerpoint"]["y"])
            self.wait(1)

            if self.is_sensor_found("PopupWindow"):
                self.logger.error("Popup window didn't close after clicking yes")
                return False, {"error": "Popup window not found"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskPopupWindowClose.execute:")
            return False, {"error": str(e)}
