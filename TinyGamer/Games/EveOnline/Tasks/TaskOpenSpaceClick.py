from task import Task, TaskRegistry, CancelException
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskOpenSpaceClick(Task):
    """
    Task for clicking on open space.
    """
    def __init__(self, game_interactor=None):
        super().__init__("OpenSpaceClick", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to click on open space.
        Returns (success, metadata) tuple.
        """
        try:
            if not self.is_sensor_found("OpenSpace"):
                self.logger.error("Open space not found")
                return False, {"error": "Open space not found"}
            
            openSpaceLocation = self.global_state.get("OpenSpace")
            self.interactor.EveSafeClick(x=openSpaceLocation["position"]["x"], y=openSpaceLocation["position"]["y"])
            self.wait(1.0)  
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskOpenSpaceClick.execute:")
            return False, {"error": str(e)}
