from task import Task, TaskRegistry, CancelException
from pynput.keyboard import Key
import logging

@TaskRegistry.register
class TaskStationInventoryItemSell(Task):
    """
    Task for selling an item in the station inventory with specified quantity and price.
    """
    def __init__(self, quantity=None, price=None, game_interactor=None):
        super().__init__("StationInventoryItemSell", game_interactor=game_interactor)
        self.quantity = quantity
        self.price = price

    def execute(self):
        """
        Execute the task to sell an item with specified quantity and price.
        Returns (success, metadata) tuple.
        """
        try:
            # Check if the sell item window is open
            if not self.is_sensor_found("SellItemWindow"):
                self.logger.error("Sell item window not found")
                return False, {"error": "Sell item window not found"}

            # Check if the quantity field is visible
            if not self.is_sensor_found("SellItemWindowQuantity"):
                self.logger.error("Quantity field in sell item window not found")
                return False, {"error": "Quantity field in sell item window not found"}

            # Get the quantity field location
            quantity_field_location = self.global_state.get("SellItemWindowQuantity")

            # Click on the quantity field
            self.interactor.EveSafeClick(x=quantity_field_location["centerpoint"]["x"], y=quantity_field_location["centerpoint"]["y"])
            self.wait(0.5)

            # Select all text (Alt+A)
            self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
            self.interactor.EvePressKeyWithModifier(Key.cmd, "a")
            self.wait(0.5)

            # Type the quantity
            if self.quantity is not None:
                self.interactor.type_string(str(self.quantity))
                self.wait(0.5)
            else:
                self.logger.error("No quantity provided")
                return False, {"error": "No quantity provided"}

            # Check if the price field is visible
            if not self.is_sensor_found("SellItemWindowPrice"):
                self.logger.error("Price field in sell item window not found")
                return False, {"error": "Price field in sell item window not found"}

            # Get the price field location
            price_field_location = self.global_state.get("SellItemWindowPrice")

            # Click on the price field
            self.interactor.EveSafeClick(x=price_field_location["centerpoint"]["x"], y=price_field_location["centerpoint"]["y"])
            self.wait(0.5)

            # Select all text (Alt+A)
            self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
            self.interactor.EvePressKeyWithModifier(Key.cmd, "a")
            self.wait(0.5)

            # Type the price
            if self.price is not None:
                # Log the price for debugging
                self.logger.info(f"Setting price: {self.price}")

                # Validate and format the price
                try:
                    # Convert to float to validate
                    price_float = float(self.price)

                    # Ensure price is positive
                    if price_float <= 0:
                        self.logger.warning(f"Price is zero or negative: {price_float}, using minimum price")
                        price_float = 0.01  # Set a minimum price

                    # Format with two decimal places
                    formatted_price = f"{price_float:.2f}"
                    self.logger.info(f"Validated and formatted price: {formatted_price}")

                    # Use the specialized price typing method if available
                    if hasattr(self.interactor, 'type_price'):
                        self.logger.info("Using specialized price typing method")
                        self.interactor.type_price(formatted_price)
                    else:
                        # Fall back to regular typing
                        self.interactor.type_string(formatted_price)
                except ValueError:
                    # If conversion fails, use a safe default
                    self.logger.warning(f"Could not parse price '{self.price}', using minimum price")
                    self.interactor.type_string("0.01")

                self.wait(0.5)
            else:
                self.logger.error("No price provided")
                return False, {"error": "No price provided"}

            # Check if the sell button is visible
            if not self.is_sensor_found("SellItemWindowSellButton"):
                self.logger.error("Sell button in sell item window not found")
                return False, {"error": "Sell button in sell item window not found"}

            # Get the sell button location
            sell_button_location = self.global_state.get("SellItemWindowSellButton")

            # Click on the sell button
            self.interactor.EveSafeClick(x=sell_button_location["centerpoint"]["x"], y=sell_button_location["centerpoint"]["y"])
            self.wait(1.0)

            # If a warning appears, click on it
            if self.is_sensor_found("PopupWindowOpen"):
                warning_location = self.global_state.get("PopupWindowOpen")
                self.interactor.EveSafeClick(x=warning_location["centerpoint"]["x"], y=warning_location["centerpoint"]["y"])
                self.wait(1.0)

            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskStationInventoryItemSell.execute:")
            return False, {"error": str(e)}
