from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskOpenMarket import TaskOpenMarket
import logging
from pynput.keyboard import Key

@TaskRegistry.register
class TaskMarketItemNameType(Task):
    """
    Task for typing an item name in the market search bar.
    """
    def __init__(self, item_name=None, game_interactor=None):
        super().__init__("MarketItemNameType", game_interactor=game_interactor)
        self.item_name = item_name
    
    def execute(self):
        """
        Execute the task to type an item name in the market search bar.
        Returns (success, metadata) tuple.
        """
        try:
            # First make sure the market window is open
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskOpenMarket),
                "Market window not found after opening it"
            ):
                return False, {"error": "Market window not found after opening it"}
            
            # Check if the search bar is visible
            if not self.is_sensor_found("MarketWindowSearch"):
                self.logger.error("Market window search bar not found")
                return False, {"error": "Market window search bar not found"}
            
            # Get the search bar location
            search_bar_location = self.global_state.get("MarketWindowSearch")
            
            # Click on the search bar
            self.interactor.EveSafeClick(x=search_bar_location["centerpoint"]["x"], y=search_bar_location["centerpoint"]["y"])
            self.wait(0.5)
            
            # Select all text (Ctrl+A)
            self.interactor.EvePressKeyWithModifier(Key.ctrl, "a")
            self.wait(0.5)
            
            # Type the item name
            if self.item_name:
                self.interactor.type_string(self.item_name)
                self.wait(1.0)
                return True, {}
            else:
                self.logger.error("No item name provided")
                return False, {"error": "No item name provided"}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskMarketItemNameType.execute:")
            return False, {"error": str(e)}
