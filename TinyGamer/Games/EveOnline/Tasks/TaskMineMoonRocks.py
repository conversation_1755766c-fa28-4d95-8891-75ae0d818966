from task import Task, TaskRegistry, CancelException
from Games.EveOnline.Tasks.TaskDroneWindowDronesDeployed import TaskDroneWindowDronesDeployed
from Games.EveOnline.Tasks.TaskOverviewWindowTargetLockAsteroid import TaskOverviewWindowTargetLockAsteroid
from Games.EveOnline.Tasks.TaskWeaponsOnline import TaskWeaponsOnline
from Games.EveOnline.Tasks.TaskWarpToMoonRocks import TaskWarpToMoonRocks
from Games.EveOnline.Tasks.TaskInventoryWindowCompress import TaskInventoryWindowCompress
import logging

@TaskRegistry.register
class TaskMineMoonRocks(Task):
    """
    Task for mining asteroids at the Lowsec asteroid field.
    """
    def __init__(self, game_interactor=None):
        super().__init__("MineMoonRocks", game_interactor=game_interactor)
        self.weapon_sensor_name = "WeaponIconStripMiner1"
    
    def execute(self):
        """
        Execute the task sequence for mining at the Moon Rocks.
        Returns (success, metadata) tuple.
        """
        try:
            # Warp to the asteroid field
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskWarpToMoonRocks),
                "Failed to warp to asteroid field"
            ):
                return False, {"error": "Failed to warp to asteroid field"}
            
            # Deploy drones
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskDroneWindowDronesDeployed),
                "Failed to deploy drones"
            ):
                return False, {"error": "Failed to deploy drones"}
            
            # Target and lock an asteroid
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskOverviewWindowTargetLockAsteroid),
                "Failed to target and lock asteroid"
            ):
                return False, {"error": "Failed to target and lock asteroid"}
            
            # Activate mining lasers
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskWeaponsOnline, weapon_sensor_name=self.weapon_sensor_name),
                "Failed to activate mining lasers"
            ):
                return False, {"error": "Failed to activate mining lasers"}

            # Compress Ore in Inventory
            if not self.run_subtask_with_error_handling(
                self.create_subtask(TaskInventoryWindowCompress),
                "Failed to compress ore in inventory"
            ):
                return False, {"error": "Failed to compress ore in inventory"}
            
            self.wait(10.0)
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskMineMoonRocks.execute:")
            return False, {"error": str(e)}
