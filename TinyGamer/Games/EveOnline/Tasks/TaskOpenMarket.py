from task import Task, TaskRegistry, CancelException
import logging
from pynput.keyboard import Key

@TaskRegistry.register
class TaskOpenMarket(Task):
    """
    Task for opening the market window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("OpenMarket", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to open the market window.
        Returns (success, metadata) tuple.
        """
        try:
            if self.is_sensor_found("MarketWindow"):
                return True, {}
            
            self.interactor.EvePressKeyWithModifier(Key.alt, "r")
            self.wait(1.0)
            
            if not self.is_sensor_found("MarketWindow"):
                self.logger.error("Market window didn't appear after pressing Alt+R")
                return False, {"error": "Market window not found"}
            return True, {}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskOpenMarket.execute:")
            return False, {"error": str(e)}
