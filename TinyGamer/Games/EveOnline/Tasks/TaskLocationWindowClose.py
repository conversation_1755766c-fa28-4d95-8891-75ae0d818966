from task import Task, TaskRegistry, CancelException
import logging

@TaskRegistry.register
class TaskLocationWindowClose(Task):
    """
    Task for closing the location window.
    """
    def __init__(self, game_interactor=None):
        super().__init__("LocationWindowClose", game_interactor=game_interactor)
    
    def execute(self):
        """
        Execute the task to close the location window.
        Returns (success, metadata) tuple.
        """
        try:
            if self.is_sensor_found("LocationWindowClose"):
                locationWindowCloseLocation = self.global_state.get("LocationWindowClose")
                self.interactor.EveSafeClick(x=locationWindowCloseLocation["centerpoint"]["x"], y=locationWindowCloseLocation["centerpoint"]["y"])
                self.wait(1.0)
            
            if not self.is_sensor_found("LocationWindow"):
                return True, {}
            else:
                self.logger.error("Location window didn't close after clicking")
                return False, {"error": "Location window didn't close after clicking"}
        except CancelException as ex:
            return False, {"error": str(ex)}
        except Exception as e:
            logging.exception(f"Exception in TaskLocationWindowClose.execute:")
            return False, {"error": str(e)}
