{"sensor_name": "ProbeScannerWindowProbeStrength8", "sensor_type": "OpenCV", "region": {"x": -23, "y": 584, "width": 754, "height": 162}, "timeout": 1.0, "parent": "ProbeScannerWindow", "parent_anchor_point": [3072, 19], "base64_encoded_image_data": "iVBORw0KGgoAAAANSUhEUgAAAHMAAAAqCAIAAAAPoPOOAAALCklEQVRoBe3Bf2jVVR8H8Pc55+PyXFtOiSiUzDIhC1nShFEWRGeO9B91k6OmfHWiqUgqi/xBhj9gamNGE7HMH0fjuGal+c9ORlBaioomGhGYv5EiZW6a1yn33s8DX7gwebo+dz3d/+7rJbTWKCoAobVGUQEIrTWKCkBorVFUAEJrjaICEFprFBWA0FqjqACE1hpFBSC01igqAKG1RlEBCK01igpAaK1RVABCa42iAhBaaxQVgNBaoyfmzZtXXl5eVlaWSCSSyWRHR8epU6eam5uRn7lz55aXl/fr1y+RSCSTyRs3bpw+ffqDDz7AvUIIuK/q6mr8H1paWsrKym7evFlbW4u/E0IA0NLSsn37dnQTRZG1FkB1dTXuS2itkZ++ffu+//77jz/+OIBMJtPV1aW1BiCEuHz5cn19fWdnJ3JLJBJNTU1PPPEEAGZOJpO9e/eWUgohrly5Mn/+/GQyiawQAjMnk8l0Oo2/M378eCLCPzJu3LjZs2czM4Bt27a1trbiv4QQmNl775yTUiIriiJrLTMbY4gIuQmtNfKzatWqioqKTCZz4MCBLVu2/P7776+88srLL7/84osvKqWOHTu2fPly5LZy5cqRI0cy86FDh7Zu3Xrx4sXHHnts5syZo0aNEkIcOHCgoaEBWSEEZvbeb9u2DTkQEf6RdevWDR8+nJkB/PLLLwsXLpRS4l4hBGb23jvnpJTIiqLIWsvMxhgiQm5Ca438tLa2PvTQQ0ePHl26dKkQQkophGDmjRs3Pvnkkzdu3KipqVFKIYfdu3eXlpaePn164cKFQggppRCCmZuamoYNG3bt2rXJkycrpRALITCz9945J6XEvyeRSHz66aeJRCKZTGqtu7q66urq2tvbca8QAjN7751zUkpkRVFkrWVmYwwRITehtUZ+2traALS0tGzdulUphdiECRPq6uqklJlMpqqqioiQw6JFix555JHjx4+3tLQopZA1f/78MWPGdHV1jR07logQCyEws/feOSelxL8niiJrLTO3trbW1tYKIb744otPPvkE9wohMLP33jknpURWFEXWWmY2xhARchNaa+QnhMDM3nvnnJQSwMCBAxsbG8vKypgZgDGGiJBbOp0GIISQUiJr0aJFVVVVd+/eff3114kIsRACM3vvnXNSSvx7Pvzww6FDh169enXSpEk7d+589NFHz507N2fOHCklugkhMLP33jknpURWFEXWWmY2xhARchNaa+QnhMDM3nvnnJQSwJo1a8rLy9vb2/v16wfAGENE6KGmpqZhw4Zdu3bNWktEiIUQmNl775yTUuJfMnDgwI0bN5aUlHz//ferVq1avHjxa6+9dvfu3YULF549exbdhBCY2XvvnJNSIiuKImstMxtjiAi5Ca018hNCYGbvvXNOSjl58uSpU6f+9ddf33zzzbhx4wAYY4gIPVFZWblkyZKSkpKDBw+uWLGCiBALITDz2bNn+8SEELdu3Wpvb//1118//vhj/FPz588fM2ZMOp1etmzZ8ePHn3nmmcbGxl69en399dfr168XQiArhMDM3nvnnJQSWVEUWWuZ2RhDRMhNaK2RnxACM3vvnXNPPfXU2rVrS0tLvfcApkyZwszGGCJC3hKJRHNz84ABA65fv15fX3/p0iWlFGIhBGYGIIQAwMyICSHOnj27dOnSzs5O9NxHH300aNCgixcv1tXVKaWEEM3NzUOGDLly5cr06dOVUsgKITCz9945J6VEVhRF1lpmNsYQEXITWmvkJ4TAzN5759z69eufffbZn3/+ecGCBdOnT58yZQozG2OICHlbvXr1Cy+8kE6nN23atGfPHqWUEAIx5xwz//nnnwcPHjx58uT58+dfffXVUaNGVVZWKqUOHDjQ0NCAHho+fHhDQ4NS6quvvtqwYYNSCkAURdbadDq9YsWKo0ePIiuEwMzee+eclBJZURRZa5nZGENEyE1orZGfEAIze++llNbamzdvLl68+MyZM3V1ddZaZjbGEBHys2DBgtGjRwsh9u/fv27dOhlDN6lUCoDohpnXrFlTXl7e0dExceJEpRR6YsGCBdXV1alUavbs2ZcvX5ZSIrZ3794HHnhg//79TU1NQgjEQgjM7L13zkkpkRVFkbWWmY0xRITchNYa+QkhMPOePXuMMQ8++ODOnTt37NihlJo+fbq1lpmNMUSEPMyYMaOmpkZKefz48XfeeUcIoZRCHmbNmjV+/PhUKlVdXU1E6Inm5uann376jz/+eOONN5RSQgjENm/ePHDgwN9++23u3LlKKcRCCMzsvXfOSSmRFUWRtZaZjTFEhNyE1hr5CSEw8/Xr1/v373/y5Mn6+noZi6LIWsvMxhgiwv9SU1Mzbdq0kpKSc+fOzZo1SwihlEJ+oiiy1jKzMYaI0BPe+/79+3d1dSWTSSEEsvr06VNSUnLt2rVJkyYppRALITCz9945J6VEVhRF1lpmNsYQEXITWmvkJ4TAzABu3LixaNGiS5cuKaUARFFkrWVmYwwR4b6MMfPmzevdu/fVq1eXLl164cIFpRTyFkWRtZaZjTFEhJ7Yt29fr169kMOdO3fGjh1LRIiFEJjZe++ck1IiK4oiay0zG2OICLkJrTXy09bWBiCTyWzfvn3Xrl1KKSEEgBkzZkycODGTyVRVVRERchs5cuTbb79dWlp68+bNdevWHT58WCklhMB/Wb58OREdOnQohIBuZs6cWVNTk06nR48eTUToiba2NgD79u1rbm5GN3Pnzh03bhwzV1VVERFibW1tAD777LOtW7dKKZFVV1dXW1ubyWSqqqqICLkJrTXy09raWlpaeunSpbq6OhlDrKGh4fnnn+/s7JwwYQIRIYehQ4cuX7784YcfvnPnzubNm/fu3auUEkLg77S0tJSVlZ06daq+vl5KiazGxsbnnnuuvb194sSJRISeCCEwc0tLy7Zt26SUyIqiyFrLzMYYIkKstbW1tLT0xIkTS5YskVIia82aNeXl5R0dHTU1NUSE3ITWGvlZuXJlRUUFMx85cuTbb7/94YcfBg0aNG3atMrKSinl4cOH3333XSJCbPfu3QC+/PLLXbt2IbZly5YBAwak0+nPP/988+bNMoYcli1b9tJLLzHzkSNHduzYcf78+SFDhkybNq2iokII8d13361evZqI0BMhBGb23jvnpJTIiqLIWsvMxhgiQmzlypUVFRWZTObw4cM7d+68ePHi4MGDp06dWllZKYT48ccf33vvPSJCbkJrjfz07dt37dq1gwYNAiCEuH37du/evYUQAC5cuFBfX9/Z2amUQiyEwMzee+eclBJACAFAKpVKJpMAhBC4V21tLbppbm4eMmQIYrdv39ZaCyEAnDlzZs6cOUIIpRR6IoTAzN5755yUEllRFFlrmdkYQ0SIJRKJxsbGwYMHA2Dmrq4urbUQAsC5c+feeuutrq4upRRyE1pr9MScOXNGjBjRt2/fRCKRTCY7Ojp++umn5uZmIYRSClkhBGb23jvnpJQAQgjMjNyMMUSEbmbOnFlRUVFWVtanT59bt251dHQcO3Zs06ZNQgilFHoohMDM3nvnnJQSWVEUWWuZ2RhDROjmzTffHDFiRFlZWSKRSCaTHR0dJ06c2LBhgxBCKYX7Elpr9FAmk+EYYiImpcS9UqkUABlDLJVK4b6ICPfKZDIcQ0zEpJT4R1KpFAAZQzeZGAAiwr0ymQzHEBMxKSX+F6G1RlEBCK01igpAaK1RVABCa42iAhBaaxQVgNBao6gAhNYaRQUgtNYoKoD/AFvk89WEfYCmAAAAAElFTkSuQmCC", "threshold": 0.8, "vertical_priority": false}