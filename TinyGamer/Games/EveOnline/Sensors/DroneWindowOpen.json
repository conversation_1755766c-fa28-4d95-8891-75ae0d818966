{"sensor_name": "DroneWindowOpen", "sensor_type": "OpenCV", "region": {"x": 78, "y": 1306, "width": 1316, "height": 880}, "timeout": 1.0, "parent": "", "parent_anchor_point": null, "base64_encoded_image_data": "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", "threshold": 0.8, "vertical_priority": false}