{"sensor_name": "DroneWindow", "sensor_type": "OpenCV", "region": {"x": 70, "y": 1220, "width": 1296, "height": 940}, "timeout": 1.0, "parent": "", "parent_anchor_point": null, "base64_encoded_image_data": "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", "threshold": 0.8, "vertical_priority": true}