{"sensor_name": "LocationWindowPugspero", "sensor_type": "OpenCV", "region": {"x": -26, "y": 65, "width": 886, "height": 1195}, "timeout": 1.0, "parent": "LocationWindow", "parent_anchor_point": [1514, 652], "base64_encoded_image_data": "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", "threshold": 0.8, "vertical_priority": false}