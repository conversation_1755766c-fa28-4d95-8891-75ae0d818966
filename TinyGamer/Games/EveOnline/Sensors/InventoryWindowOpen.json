{"sensor_name": "InventoryWindowOpen", "sensor_type": "OpenCV", "region": {"x": 75, "y": 254, "width": 1465, "height": 1340}, "timeout": 1.0, "parent": "", "parent_anchor_point": null, "base64_encoded_image_data": "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", "threshold": 0.8, "vertical_priority": false}