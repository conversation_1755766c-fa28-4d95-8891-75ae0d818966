{"sensor_name": "CargoDepositWindowTransferButtonDim", "sensor_type": "OpenCV", "region": {"x": 440, "y": 488, "width": 396, "height": 338}, "timeout": 1.0, "parent": "CargoDepositWindow", "parent_anchor_point": [1558, 714], "base64_encoded_image_data": "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", "threshold": 0.8, "vertical_priority": false}