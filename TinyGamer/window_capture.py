import numpy as np
import cv2
import platform

# On Windows, use pywin32 for foreground window capture
if platform.system() == "Windows":
    import win32gui
    from PIL import ImageGrab
    import cv2
    import numpy as np
    # NOTE: ImageGrab.grab(bbox=...) will only capture the visible part of the window.
    # If the window is minimized or covered, the result may be black or outdated.
    def grab_foreground_window():
        hwnd = win32gui.GetForegroundWindow()
        left, top, right, bottom = win32gui.GetWindowRect(hwnd)
        img = ImageGrab.grab(bbox=(left, top, right, bottom))
        img = img.convert('RGB')
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
        # Save the image to debug folder with timestamp
        import os
        import datetime
        debug_dir = "debug_images"
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        debug_path = os.path.join(debug_dir, f"foreground_window_{timestamp}.png")
        cv2.imwrite(debug_path, img_bgr)
        return img_bgr
elif platform.system() == "Darwin":
    # On macOS, capture only the foreground window region
    from PIL import ImageGrab
    import Quartz
    import AppKit

    def _get_foreground_window_bbox_mac():
        # Get the frontmost application
        app = AppKit.NSWorkspace.sharedWorkspace().frontmostApplication()
        pid = app.processIdentifier()
        options = Quartz.kCGWindowListOptionOnScreenOnly | Quartz.kCGWindowListExcludeDesktopElements
        window_list = Quartz.CGWindowListCopyWindowInfo(options, Quartz.kCGNullWindowID)
        for window in window_list:
            if window['kCGWindowOwnerPID'] == pid:
                bounds = window['kCGWindowBounds']
                x = int(bounds['X'])
                y = int(bounds['Y'])
                w = int(bounds['Width'])
                h = int(bounds['Height'])
                return (x, y, x + w, y + h)
        return None

    def grab_foreground_window():
        bbox = _get_foreground_window_bbox_mac()
        if bbox is None:
            # fallback to full screen
            img = ImageGrab.grab()
        else:
            img = ImageGrab.grab(bbox=bbox)
        img = img.convert('RGB')
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
        return img_bgr
else:
    # On Linux and other systems, fallback to full screen capture
    from PIL import ImageGrab
    def grab_foreground_window():
        img = ImageGrab.grab()
        img = img.convert('RGB')
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
        return img_bgr
