import cv2
import threading
import time
import cv2
import threading
import time
import numpy as np

class WebcamFrameGrabber:
    """
    Headless webcam frame grabber for live operation (no UI).
    Continuously grabs frames from the specified webcam device.
    """
    def __init__(self, device_id=0, width=None, height=None):
        self.device_id = device_id
        self.width = width
        self.height = height
        self._frame = None
        self._running = False
        self._lock = threading.Lock()
        self._thread = None

    def start(self):
        if self._running:
            return
        self._running = True
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()

    def stop(self):
        self._running = False
        if self._thread:
            self._thread.join(timeout=2)
            self._thread = None

    def _run(self):
        cap = cv2.VideoCapture(self.device_id)
        if self.width:
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
        if self.height:
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
        while self._running:
            ret, frame = cap.read()
            if ret:
                with self._lock:
                    self._frame = frame.copy()
            time.sleep(0.02)  # ~50 FPS max
        cap.release()

    def get_latest_frame(self):
        with self._lock:
            if self._frame is not None:
                return self._frame.copy()
            return None
