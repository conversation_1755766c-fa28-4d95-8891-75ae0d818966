-- Add character_id column to orders table
-- This script adds the character_id column to the orders table

-- Add character_id column to orders table if it doesn't exist
ALTER TABLE orders ADD COLUMN character_id INTEGER;

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_orders_character_id ON orders(character_id);

-- Insert a migration record
INSERT OR IGNORE INTO migration_status (migration_name, completed, completed_date) 
VALUES ('add_character_id_to_orders', 1, datetime('now'));
