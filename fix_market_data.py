#!/usr/bin/env python3
"""
Fix Market Data Table Script

This script adds the missing market_data table and latest_market_data view to the database.
"""

import sqlite3
import os
import sys

# Database path
DATABASE_NAME = 'eve_trader.db'

def create_connection(db_file):
    """ Create a database connection to a SQLite database """
    conn = None
    try:
        conn = sqlite3.connect(db_file)
        print(f"Connected to database: {db_file}")
        return conn
    except sqlite3.Error as e:
        print(f"Error connecting to database: {e}")
    return conn

def execute_sql_script(conn, sql_file):
    """ Execute SQL script from file """
    try:
        with open(sql_file, 'r') as f:
            sql_script = f.read()
        
        cursor = conn.cursor()
        cursor.executescript(sql_script)
        conn.commit()
        print(f"SQL script executed successfully: {sql_file}")
        return True
    except sqlite3.Error as e:
        print(f"Error executing SQL script: {e}")
        return False
    except I<PERSON>rro<PERSON> as e:
        print(f"Error reading SQL file: {e}")
        return False

def check_table_exists(conn, table_name):
    """ Check if a table exists in the database """
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}';")
        result = cursor.fetchone()
        return result is not None
    except sqlite3.Error as e:
        print(f"Error checking if table exists: {e}")
        return False

def check_view_exists(conn, view_name):
    """ Check if a view exists in the database """
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='view' AND name='{view_name}';")
        result = cursor.fetchone()
        return result is not None
    except sqlite3.Error as e:
        print(f"Error checking if view exists: {e}")
        return False

def main():
    # Check if database exists
    if not os.path.exists(DATABASE_NAME):
        print(f"Error: Database file not found: {DATABASE_NAME}")
        print("Please run setup_database.py first.")
        sys.exit(1)
    
    # Connect to database
    conn = create_connection(DATABASE_NAME)
    if conn is None:
        print("Error: Could not connect to database.")
        sys.exit(1)
    
    try:
        # Check if market_data table already exists
        if check_table_exists(conn, 'market_data'):
            print("market_data table already exists.")
        else:
            print("market_data table does not exist. Creating...")
        
        # Check if latest_market_data view already exists
        if check_view_exists(conn, 'latest_market_data'):
            print("latest_market_data view already exists.")
        else:
            print("latest_market_data view does not exist. Creating...")
        
        # Execute SQL script
        sql_file = 'add_market_data_table.sql'
        if not os.path.exists(sql_file):
            print(f"Error: SQL file not found: {sql_file}")
            sys.exit(1)
        
        success = execute_sql_script(conn, sql_file)
        if success:
            print("Database fix completed successfully.")
            
            # Verify the table and view were created
            if check_table_exists(conn, 'market_data'):
                print("Verified: market_data table exists.")
            else:
                print("Error: market_data table was not created.")
            
            if check_view_exists(conn, 'latest_market_data'):
                print("Verified: latest_market_data view exists.")
            else:
                print("Error: latest_market_data view was not created.")
        else:
            print("Error: Failed to fix database.")
    
    finally:
        # Close connection
        if conn:
            conn.close()
            print("Database connection closed.")

if __name__ == '__main__':
    main()
