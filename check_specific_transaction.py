#!/usr/bin/env python3
"""
Script to check a specific transaction in detail.
"""

import os
import sys
import sqlite3
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Tuple, Set

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from models.database import DatabaseManager

def check_specific_transaction(db_manager: DatabaseManager, tx_id: int):
    """
    Check a specific transaction in detail.
    
    Args:
        db_manager: Database manager
        tx_id: Transaction ID to check
    """
    print(f"Checking transaction #{tx_id}")
    
    # Get transaction details
    tx_query = "SELECT * FROM transactions WHERE tx_id = ?"
    tx = db_manager.execute_query(tx_query, (tx_id,), fetch_one=True)
    
    if not tx:
        print(f"Transaction #{tx_id} not found.")
        return
    
    print("\n=== Transaction Details ===")
    print(f"ID: {tx['tx_id']}")
    print(f"Type: {tx['transaction_type']}")
    print(f"Item: {tx['item_name']} (ID: {tx['item_id']})")
    print(f"Quantity: {tx['quantity']}")
    print(f"Unit Price: {tx['unit_price']}")
    print(f"Total Price: {tx['total_price']}")
    print(f"Net Amount: {tx['net_amount']}")
    print(f"Profit: {tx['profit']}")
    print(f"Timestamp: {tx['timestamp']}")
    print(f"Location: {tx['location_id']}")
    print(f"Character: {tx['character_id']}")
    
    # Get batch links
    links_query = """
    SELECT l.*, b.item_name, b.purchase_date, b.quantity_original, b.quantity_remaining
    FROM transaction_batch_links l
    JOIN inventory_batches b ON l.batch_id = b.batch_id
    WHERE l.transaction_id = ?
    ORDER BY b.purchase_date ASC
    """
    
    links = db_manager.execute_query(links_query, (tx_id,), fetch_all=True)
    
    if not links:
        print("\nNo batch links found for this transaction.")
        return
    
    print("\n=== Batch Links ===")
    total_quantity = 0
    for link in links:
        print(f"Batch #{link['batch_id']}:")
        print(f"  Item: {link['item_name']}")
        print(f"  Purchase Date: {link['purchase_date']}")
        print(f"  Quantity: {link['quantity']} (Original: {link['quantity_original']}, Remaining: {link['quantity_remaining']})")
        print(f"  Unit Cost: {link['unit_cost']}")
        print(f"  Profit Contribution: {link['profit_contribution']}")
        print()
        total_quantity += link['quantity']
    
    print(f"Total Quantity from Batch Links: {total_quantity}")
    if total_quantity != tx['quantity']:
        print(f"WARNING: Quantity mismatch! Transaction: {tx['quantity']}, Batch Links: {total_quantity}")
        
        # Check if there are any other batches available for this item
        batches_query = """
        SELECT * FROM inventory_batches
        WHERE item_id = ? AND location_id = ? AND character_id = ? AND quantity_remaining > 0
        ORDER BY purchase_date ASC
        """
        
        batches = db_manager.execute_query(
            batches_query, 
            (tx['item_id'], tx['location_id'], tx['character_id']), 
            fetch_all=True
        )
        
        if batches:
            print("\n=== Available Batches ===")
            print(f"There are {len(batches)} batches with remaining quantity for this item:")
            for batch in batches:
                print(f"Batch #{batch['batch_id']}: {batch['quantity_remaining']} of {batch['quantity_original']} units remaining (Purchase Date: {batch['purchase_date']})")
        else:
            print("\nNo batches with remaining quantity found for this item.")

def main():
    # Initialize database manager
    db_path = os.path.join(os.path.dirname(__file__), 'eve_trader.db')
    if not os.path.exists(db_path):
        print(f"Error: Database file not found at {db_path}")
        return False
        
    print(f"Using database at {db_path}")
    db_manager = DatabaseManager(db_path)
    
    # Get transaction ID from command line argument
    if len(sys.argv) != 2:
        print("Usage: python check_specific_transaction.py TRANSACTION_ID")
        return False
    
    try:
        tx_id = int(sys.argv[1])
    except ValueError:
        print("Error: Transaction ID must be an integer")
        return False
    
    # Check the transaction
    check_specific_transaction(db_manager, tx_id)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
