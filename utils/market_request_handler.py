"""
Market Request Handler for TinyTrader

This module provides a handler for market price requests from TinyGamer.
It monitors the system temp directory for request files and responds with price information.
"""

import os
import json
import time
import tempfile
import logging
import threading
from typing import Optional, Dict, Any, Tuple

from PyQt5.QtCore import QObject, QTimer, pyqtSignal

from utils.item_database import get_item_database
from models.database import DatabaseManager
from api.esi_client import ESIClient, JITA_STATION_ID, JITA_REGION_ID

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class MarketRequestHandler(QObject):
    """
    Handler for market price requests from TinyGamer.

    This class monitors the system temp directory for request files from TinyGamer
    and responds with price information from TinyTrader's database.
    """

    # Signal for status updates
    status_updated = pyqtSignal(str)

    def __init__(self, db_manager: DatabaseManager, esi_client: Optional[ESIClient] = None):
        """
        Initialize the market request handler.

        Args:
            db_manager: Database manager instance
            esi_client: ESI client instance for fetching market data if needed
        """
        super().__init__()
        self.logger = logging.getLogger("MarketRequestHandler")
        self.db = db_manager
        self.esi_client = esi_client
        self.item_db = get_item_database()
        self.running = False
        self.timer = None
        self.check_interval = 500  # Check every 500 ms

        # Paths for request and response files
        self.temp_dir = tempfile.gettempdir()
        self.request_file_path = os.path.join(self.temp_dir, "tinygamer_market_request.json")
        self.response_file_path = os.path.join(self.temp_dir, "tinygamer_market_response.json")

        self.logger.info(f"Market request handler initialized. Temp dir: {self.temp_dir}")

    def start(self):
        """Start monitoring for market requests."""
        if self.running:
            self.logger.info("Market request handler is already running")
            return

        self.running = True

        # Create a timer to periodically check for request files
        self.timer = QTimer()
        self.timer.timeout.connect(self._check_for_requests)
        self.timer.start(self.check_interval)

        self.logger.info("Market request handler started")
        self.status_updated.emit("Market request handler started")

    def stop(self):
        """Stop monitoring for market requests."""
        if not self.running:
            return

        self.running = False

        if self.timer:
            self.timer.stop()
            self.timer = None

        self.logger.info("Market request handler stopped")
        self.status_updated.emit("Market request handler stopped")

    def _check_for_requests(self):
        """Check for market request files and process them."""
        if not self.running:
            return

        try:
            # Check if request file exists
            if not os.path.exists(self.request_file_path):
                return

            # Read the request file
            with open(self.request_file_path, "r") as f:
                request_data = json.load(f)

            # Process the request
            self._process_request(request_data)

            # Remove the request file
            try:
                os.remove(self.request_file_path)
                self.logger.info(f"Removed request file: {self.request_file_path}")
            except Exception as e:
                self.logger.warning(f"Failed to remove request file: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error checking for market requests: {str(e)}")

    def _process_request(self, request_data: Dict[str, Any]):
        """
        Process a market request and create a response.

        Args:
            request_data: Request data from TinyGamer
        """
        try:
            # Extract request information
            action = request_data.get('action')
            item_name = request_data.get('item_name')
            timestamp = request_data.get('timestamp')

            self.logger.info(f"Processing market request: action={action}, item_name={item_name}, timestamp={timestamp}")
            self.status_updated.emit(f"Processing market request for {item_name}")

            if action == 'get_sell_price':
                # Get the sell price for the item
                price = self._get_sell_price(item_name)

                # Log the price for debugging
                if price == "0.00" or price == "0.01":
                    self.logger.warning(f"Got low price {price} for {item_name}. This might indicate an issue with price lookup.")
                else:
                    self.logger.info(f"Successfully got price {price} for {item_name}")

                # Ensure the price is a valid number string
                try:
                    # Validate that the price is a valid number
                    price_float = float(price)
                    # Format with exactly 2 decimal places
                    price = f"{price_float:.2f}"
                except ValueError:
                    self.logger.error(f"Invalid price format: {price}, using default")
                    price = "0.01"  # Default minimum price

                # Create response data with all necessary fields
                response_data = {
                    'price': price,
                    'item_name': item_name,  # Always include the item name for verification
                    'timestamp': time.time(),  # Current timestamp for freshness check
                    'request_timestamp': timestamp  # Original request timestamp for reference
                }

                # Write response to file
                with open(self.response_file_path, "w") as f:
                    json.dump(response_data, f)

                self.logger.info(f"Wrote market response to {self.response_file_path}: price={price}")
                self.status_updated.emit(f"Sent sell price {price} for {item_name}")
            else:
                self.logger.warning(f"Unknown action in market request: {action}")

        except Exception as e:
            self.logger.error(f"Error processing market request: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _get_sell_price(self, item_name: str) -> str:
        """
        Get the sell price for an item.

        Args:
            item_name: Name of the item

        Returns:
            Sell price as a string
        """
        try:
            # Log the item name for debugging
            self.logger.info(f"Getting sell price for item: {item_name}")

            # Look up the item ID
            item = self.item_db.search_items(item_name, limit=1)
            if not item:
                self.logger.warning(f"Item not found in database: {item_name}")
                return "0.00"

            item_id = item[0]['item_id']
            self.logger.info(f"Found item ID: {item_id} for {item_name}")

            # First, try to get the price from the local database
            price_str = self._get_price_from_database(item_id, item_name)

            # If no price found in database and we have an ESI client, try to fetch from ESI
            if price_str == "0.00" and self.esi_client:
                self.logger.info(f"No price found in database for {item_name}, fetching from ESI...")
                self.status_updated.emit(f"Fetching market data for {item_name} from ESI...")

                # Fetch market data from ESI
                price_str = self._fetch_price_from_esi(item_id, item_name)

            # Final check on the price
            if price_str == "0.00":
                self.logger.warning(f"Could not get a valid price for {item_name} (ID: {item_id})")
            else:
                self.logger.info(f"Final price for {item_name}: {price_str}")

            # Ensure the price is properly formatted as a string with 2 decimal places
            try:
                # Convert to float and back to string to ensure proper formatting
                price_float = float(price_str)
                if price_float <= 0:
                    self.logger.warning(f"Price for {item_name} is zero or negative: {price_float}, using default minimum price")
                    price_float = 0.01  # Set a minimum price to avoid zero prices

                # Format with exactly 2 decimal places
                price_str = f"{price_float:.2f}"
                self.logger.info(f"Formatted final price for {item_name}: {price_str}")
            except ValueError:
                self.logger.error(f"Error formatting price '{price_str}' for {item_name}, using default")
                price_str = "0.01"  # Default minimum price if formatting fails

            return price_str

        except Exception as e:
            self.logger.error(f"Error getting sell price for {item_name}: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return "0.01"  # Return a minimum price instead of zero

    def _get_price_from_database(self, item_id: int, item_name: str) -> str:
        """
        Get the sell price for an item from the local database.

        Args:
            item_id: ID of the item
            item_name: Name of the item (for logging)

        Returns:
            Sell price as a string
        """
        try:
            # Query the database for the latest market data
            query = """
            SELECT * FROM latest_market_data
            WHERE item_id = ? AND location_id = ?
            """
            market_data = self.db.execute_query(query, (item_id, JITA_STATION_ID), fetch_one=True)

            # If no data with location_id, try the old format (without location filtering)
            if not market_data:
                query = """
                SELECT * FROM latest_market_data
                WHERE item_id = ?
                """
                market_data = self.db.execute_query(query, (item_id,), fetch_one=True)

            if market_data:
                # Use the best sell price
                sell_price = market_data.get('sell_price', 0)

                # Format the price as a string with 2 decimal places
                price_str = f"{float(sell_price):.2f}"
                self.logger.info(f"Found sell price in database for {item_name}: {price_str}")
                return price_str
            else:
                self.logger.warning(f"No market data found in database for {item_name}")
                return "0.00"

        except Exception as e:
            self.logger.error(f"Error getting sell price from database for {item_name}: {str(e)}")
            return "0.00"

    def _fetch_price_from_esi(self, item_id: int, item_name: str) -> str:
        """
        Fetch the sell price for an item from ESI.

        Args:
            item_id: ID of the item
            item_name: Name of the item (for logging)

        Returns:
            Sell price as a string
        """
        try:
            if not self.esi_client:
                self.logger.warning(f"No ESI client available to fetch price for {item_name}")
                return "0.00"

            # Fetch market orders from ESI for the Jita region
            self.status_updated.emit(f"Fetching market orders for {item_name} from ESI...")
            orders = self.esi_client.get_market_orders(region_id=JITA_REGION_ID, item_id=item_id, order_type='sell')

            if not orders:
                self.logger.warning(f"No market orders found in ESI for {item_name}")
                return "0.00"

            # Filter for orders in Jita station
            jita_orders = [order for order in orders if order.get('location_id') == JITA_STATION_ID]

            if not jita_orders:
                self.logger.warning(f"No orders found in Jita station for {item_name}")
                # Log some of the orders to debug
                if orders and len(orders) > 0:
                    self.logger.info(f"Sample order locations: {[order.get('location_id') for order in orders[:5]]}")
                return "0.00"

            # Filter for sell orders (should already be filtered by the API call, but double-check)
            sell_orders = [order for order in jita_orders if order.get('is_buy_order') == False]

            if not sell_orders:
                self.logger.warning(f"No sell orders found in Jita for {item_name}")
                return "0.00"

            # Find the lowest sell price
            lowest_sell_price = min(sell_orders, key=lambda x: x.get('price', float('inf'))).get('price', 0)

            # Format the price as a string with 2 decimal places
            price_str = f"{float(lowest_sell_price):.2f}"
            self.logger.info(f"Found sell price in ESI for {item_name} in Jita: {price_str}")

            # Store the price in the database for future use
            self._store_price_in_database(item_id, lowest_sell_price)

            return price_str

        except Exception as e:
            self.logger.error(f"Error fetching sell price from ESI for {item_name}: {str(e)}")
            return "0.00"

    def _store_price_in_database(self, item_id: int, sell_price: float) -> None:
        """
        Store the sell price in the database for future use.

        Args:
            item_id: ID of the item
            sell_price: Sell price to store
        """
        try:
            # Check if there's an existing entry
            query = """
            SELECT * FROM latest_market_data
            WHERE item_id = ? AND location_id = ?
            """
            existing_data = self.db.execute_query(query, (item_id, JITA_STATION_ID), fetch_one=True)

            if existing_data:
                # Update the existing entry
                query = """
                UPDATE latest_market_data
                SET sell_price = ?, updated_at = datetime('now')
                WHERE item_id = ? AND location_id = ?
                """
                self.db.execute_update(query, (sell_price, item_id, JITA_STATION_ID))
            else:
                # Insert a new entry
                query = """
                INSERT INTO latest_market_data (item_id, location_id, sell_price, updated_at)
                VALUES (?, ?, ?, datetime('now'))
                """
                self.db.execute_update(query, (item_id, JITA_STATION_ID, sell_price))

            # Verify that the price was stored correctly
            verify_query = """
            SELECT sell_price FROM latest_market_data
            WHERE item_id = ? AND location_id = ?
            """
            row = self.db.execute_query(verify_query, (item_id, JITA_STATION_ID), fetch_one=True)
            if row:
                self.logger.info(f"Verified stored price {row['sell_price']} for item {item_id}")
            else:
                self.logger.warning(f"Failed to verify stored price for item {item_id}")

            self.logger.info(f"Stored sell price {sell_price} for item {item_id} in database")

        except Exception as e:
            self.logger.error(f"Error storing sell price in database for item {item_id}: {str(e)}")
