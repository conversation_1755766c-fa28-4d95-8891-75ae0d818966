"""
Position Sizing Utility for TinyTrader

This module provides functions for calculating recommended position sizes
for trading items in EVE Online.
"""

from decimal import Decimal
from typing import Dict, List, Any, Optional

# Default values for position sizing
DEFAULT_TURNOVER_FACTOR = Decimal('0.2')  # Default to 20% of daily volume
DEFAULT_RISK_FACTOR = Decimal('1.0')  # Default risk factor for stable items
LOW_RISK_FACTOR = Decimal('0.5')  # Risk factor for volatile or low-volume items

def calculate_recommended_quantity(
    buy_price: Decimal,
    daily_volume: int,
    max_spend: Optional[Decimal] = None,
    turnover_factor: Optional[Decimal] = None,
    risk_factor: Optional[Decimal] = None,
    fill_rate: Optional[Decimal] = None,
    historical_data: Optional[List[Dict[str, Any]]] = None
) -> int:
    """
    Calculate the recommended purchase quantity for an item.
    
    Formula: Buy Quantity = Min(MaxSpend / BuyPrice, DailyVolume × TurnoverFactor × RiskFactor)
    
    Args:
        buy_price: The current buy price of the item
        daily_volume: The estimated daily volume of the item
        max_spend: Maximum amount of ISK to spend (default: 50M ISK)
        turnover_factor: How much of the market to absorb (default: 0.2)
        risk_factor: Scalar for item risk (default: 1.0 for stable items, 0.5 for volatile)
        fill_rate: Optional fill rate data to adjust quantity
        historical_data: Optional historical market data for risk assessment
        
    Returns:
        The recommended purchase quantity as an integer
    """
    # Set defaults if not provided
    if max_spend is None:
        max_spend = Decimal('50000000')  # Default to 50M ISK
    
    if turnover_factor is None:
        turnover_factor = DEFAULT_TURNOVER_FACTOR
    
    if risk_factor is None:
        risk_factor = determine_risk_factor(daily_volume, fill_rate, historical_data)
    
    # Calculate max quantity based on available funds
    max_quantity_by_funds = int(max_spend / buy_price) if buy_price > 0 else 0
    
    # Calculate max quantity based on market volume and risk
    max_quantity_by_volume = int(daily_volume * turnover_factor * risk_factor)
    
    # Take the minimum of the two constraints
    recommended_quantity = min(max_quantity_by_funds, max_quantity_by_volume)
    
    # Ensure we don't recommend zero
    if recommended_quantity <= 0 and max_quantity_by_funds > 0:
        recommended_quantity = 1
    
    # If we have fill rate data, adjust for expected sell delay
    if fill_rate and fill_rate > 0:
        expected_days_to_sell = recommended_quantity / (daily_volume * fill_rate)
        if expected_days_to_sell > 7:
            # Scale down quantity to target a 7-day sell period
            recommended_quantity = int(7 * daily_volume * fill_rate)
            # Ensure at least 1 item if we have funds
            if recommended_quantity <= 0 and max_quantity_by_funds > 0:
                recommended_quantity = 1
    
    return recommended_quantity

def determine_risk_factor(
    daily_volume: int,
    fill_rate: Optional[Decimal] = None,
    historical_data: Optional[List[Dict[str, Any]]] = None
) -> Decimal:
    """
    Determine the risk factor for an item based on its market characteristics.
    
    Args:
        daily_volume: The estimated daily volume of the item
        fill_rate: Optional fill rate data
        historical_data: Optional historical market data
        
    Returns:
        A risk factor between 0.1 and 1.0
    """
    # Start with default risk factor
    risk_factor = DEFAULT_RISK_FACTOR
    
    # Adjust for low volume
    if daily_volume < 10:
        risk_factor = LOW_RISK_FACTOR
    elif daily_volume < 100:
        risk_factor = Decimal('0.75')
    
    # Adjust for fill rate if available
    if fill_rate is not None:
        if fill_rate < Decimal('0.1'):  # Very low fill rate
            risk_factor = min(risk_factor, Decimal('0.3'))
        elif fill_rate < Decimal('0.5'):  # Moderate fill rate
            risk_factor = min(risk_factor, Decimal('0.7'))
    
    # Could add more sophisticated analysis using historical_data here
    # For example, check price volatility, volume trends, etc.
    
    return risk_factor
