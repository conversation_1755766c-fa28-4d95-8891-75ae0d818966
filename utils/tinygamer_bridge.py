"""
TinyGamer Bridge for TinyTrader

This module provides a bridge between TinyTrader and TinyGamer for executing trades.
It also includes utility functions for application management.
"""

import os
import sys
import subprocess
import time
import logging
import json
import pyperclip
import platform
from typing import Dict, Any, Optional, Tuple, List, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("TinyGamerBridge")

# Import platform-specific window management modules
if platform.system() == "Windows":
    import pygetwindow as gw
elif platform.system() == "Darwin":
    import AppKit
    import Quartz
else:
    # For Linux, we'll use a different approach
    pass


def is_application_running(app_name: str = "TinyTrader") -> bool:
    """
    Check if an application with the given name is already running.

    Args:
        app_name: Name of the application to check for

    Returns:
        True if the application is running, False otherwise
    """
    try:
        system = platform.system()

        if system == "Windows":
            # Windows implementation using pygetwindow
            app_windows = [window for window in gw.getAllTitles() if app_name in window]
            return len(app_windows) > 0

        elif system == "Darwin":
            # macOS implementation using AppKit
            workspace = AppKit.NSWorkspace.sharedWorkspace()
            apps = workspace.runningApplications()

            # Check for app_name in the localized name of each running application
            for app in apps:
                app_name_str = app.localizedName()
                if app_name_str and app_name in app_name_str:
                    # Check if it's not the current process
                    if app.processIdentifier() != os.getpid():
                        logger.info(f"Found running instance of {app_name}: {app_name_str} (PID: {app.processIdentifier()})")
                        return True

            return False

        else:
            # Linux implementation using ps command
            try:
                # Get the list of processes containing app_name
                import subprocess
                result = subprocess.run(
                    ["ps", "-ef"],
                    capture_output=True,
                    text=True,
                    check=True
                )

                # Count occurrences of app_name in process list, excluding grep and the current process
                current_pid = os.getpid()
                count = 0

                for line in result.stdout.splitlines():
                    if app_name in line and str(current_pid) not in line:
                        count += 1

                # If we found more than one instance (excluding this process), return True
                return count > 0

            except Exception as e:
                logger.error(f"Error checking for running instances on Linux: {str(e)}")
                return False

    except Exception as e:
        logger.error(f"Error checking if application is running: {str(e)}")
        return False

class TinyGamerBridge:
    """
    Bridge class for interacting with TinyGamer from TinyTrader.

    This class provides methods to:
    1. Launch TinyGamer with the EVE director
    2. Execute tasks in TinyGamer
    3. Find and focus the EVE Online client window
    """

    def __init__(self, tinygamer_path: Optional[str] = None):
        """
        Initialize the TinyGamer bridge.

        Args:
            tinygamer_path: Path to the TinyGamer directory. If None, assumes TinyGamer
                           is in a subdirectory of the current project.
        """
        self.logger = logging.getLogger("TinyGamerBridge")

        # Determine TinyGamer path
        if tinygamer_path:
            self.tinygamer_path = tinygamer_path
        else:
            # Default: Assume TinyGamer is in a subdirectory of the current project
            self.tinygamer_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "TinyGamer")

        self.logger.info(f"TinyGamer path: {self.tinygamer_path}")

        # Check if TinyGamer exists
        if not os.path.exists(self.tinygamer_path):
            self.logger.error(f"TinyGamer not found at {self.tinygamer_path}")
            raise FileNotFoundError(f"TinyGamer not found at {self.tinygamer_path}")

        # TinyGamer process
        self.tinygamer_process = None

        # TinyGamer log file path
        self.log_file_path = os.path.join(self.tinygamer_path, 'logs', 'tinygamer.log')
        self.last_log_position = 0

    def launch_tinygamer(self) -> bool:
        """
        Launch TinyGamer with the EVE director.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if TinyGamer is already running
            if self.tinygamer_process and self.tinygamer_process.poll() is None:
                self.logger.info("TinyGamer is already running")
                return True

            # Launch TinyGamer with the EVE director
            self.logger.info("Launching TinyGamer with EVE director...")

            # Change to TinyGamer directory
            original_dir = os.getcwd()
            os.chdir(self.tinygamer_path)

            # Ensure logs directory exists
            logs_dir = os.path.join(self.tinygamer_path, 'logs')
            os.makedirs(logs_dir, exist_ok=True)

            # Launch TinyGamer with no preview and no status window
            self.tinygamer_process = subprocess.Popen(
                [sys.executable, "main.py", "--eve", "--no-preview", "--no-status"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Change back to original directory
            os.chdir(original_dir)

            # Wait a bit for TinyGamer to start
            time.sleep(2)

            # Check if process is still running
            if self.tinygamer_process.poll() is not None:
                _, stderr = self.tinygamer_process.communicate()
                self.logger.error(f"TinyGamer failed to start: {stderr}")
                return False

            self.logger.info("TinyGamer launched successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error launching TinyGamer: {str(e)}")
            return False

    def is_eve_running(self) -> bool:
        """
        Check if the EVE Online client is running.

        Returns:
            True if EVE is running, False otherwise
        """
        try:
            system = platform.system()

            if system == "Windows":
                # Windows implementation using pygetwindow
                eve_windows = [window for window in gw.getAllTitles() if "EVE" in window]
                return len(eve_windows) > 0

            elif system == "Darwin":
                # macOS implementation using AppKit
                workspace = AppKit.NSWorkspace.sharedWorkspace()
                apps = workspace.runningApplications()

                for app in apps:
                    app_name = app.localizedName()
                    if app_name and "EVE" in app_name:
                        return True

                return False
            else:
                # Linux or other platforms - not implemented yet
                self.logger.warning(f"EVE detection not implemented for {system}")
                return True  # Assume it's running

        except Exception as e:
            self.logger.error(f"Error checking if EVE is running: {str(e)}")
            return False

    def focus_eve_client(self) -> bool:
        """
        Find and focus the EVE Online client window.
        Uses platform-specific methods to focus the window.

        First checks if EVE is running, then attempts to focus it.

        Returns:
            True if successful, False otherwise
        """
        # First check if EVE is running
        if not self.is_eve_running():
            self.logger.error("EVE Online client is not running. Please start EVE before executing trades.")
            return False
        try:
            system = platform.system()

            if system == "Windows":
                # Windows implementation using pygetwindow
                eve_windows = [window for window in gw.getAllTitles() if "EVE" in window]

                if not eve_windows:
                    self.logger.error("EVE Online client not found")
                    return False

                # Focus the first EVE window found
                eve_window = gw.getWindowsWithTitle(eve_windows[0])[0]
                eve_window.activate()

            elif system == "Darwin":
                # macOS implementation using AppKit with improved reliability
                workspace = AppKit.NSWorkspace.sharedWorkspace()
                apps = workspace.runningApplications()
                eve_app = None

                # First try to find by exact name "EVE"
                for app in apps:
                    app_name = app.localizedName()
                    if app_name and app_name == "EVE":
                        eve_app = app
                        break

                # If not found, try with partial match
                if not eve_app:
                    for app in apps:
                        app_name = app.localizedName()
                        if app_name and "EVE" in app_name:
                            eve_app = app
                            break

                if not eve_app:
                    self.logger.error("EVE Online client not found")
                    return False

                self.logger.info(f"Found EVE app: {eve_app.localizedName()} (Bundle ID: {eve_app.bundleIdentifier()}, PID: {eve_app.processIdentifier()})")
                self.logger.info(f"EVE app state before activation: isActive={eve_app.isActive()}, isHidden={eve_app.isHidden()}, activationPolicy={eve_app.activationPolicy()}")

                # Attempt to unhide if hidden
                if eve_app.isHidden():
                    self.logger.info("EVE app is hidden, attempting to unhide.")
                    if eve_app.unhide():
                        self.logger.info("Successfully unhid EVE app. Waiting a moment.")
                        time.sleep(0.5)  # Give it a moment to unhide
                    else:
                        self.logger.warning("Failed to unhide EVE app. Activation might still fail.")

                # Implement a retry mechanism for activation
                max_retries = 3
                retry_delay = 0.5
                success = False

                for attempt in range(max_retries):
                    self.logger.info(f"Attempting to activate EVE app (attempt {attempt+1}/{max_retries})...")

                    # Use both activation methods for better reliability
                    options = AppKit.NSApplicationActivateIgnoringOtherApps | AppKit.NSApplicationActivateAllWindows
                    eve_app.activateWithOptions_(options)  # Result often returns False even when successful

                    # Also try to bring the app to front using NSRunningApplication method
                    try:
                        # Force the app to the foreground
                        script = f"""
                        tell application "System Events"
                            set frontmost of process "{eve_app.localizedName()}" to true
                        end tell
                        """
                        import subprocess
                        subprocess.run(["osascript", "-e", script], capture_output=True)
                        self.logger.info("Executed AppleScript to force app to foreground")
                    except Exception as e:
                        self.logger.warning(f"AppleScript activation failed: {str(e)}")

                    # Wait longer for activation to take effect
                    time.sleep(retry_delay)

                    # Check if activation was successful
                    if eve_app.isActive():
                        self.logger.info("EVE app is now active!")
                        success = True
                        break
                    else:
                        self.logger.warning(f"Activation attempt {attempt+1} failed, app is still not active")

                if not success:
                    self.logger.error(f"Failed to activate EVE Online client after {max_retries} attempts")
                    # Return True anyway to allow the process to continue - the user may need to manually click on EVE
                    self.logger.warning("Continuing despite activation failure - user may need to manually click on EVE window")
                    return True

                self.logger.info(f"Successfully focused EVE Online client: {eve_app.localizedName()}")
            else:
                # Linux or other platforms - not implemented yet
                self.logger.warning(f"Window focusing not implemented for {system}")
                # Return True anyway to allow the process to continue
                return True

            # Wait a bit for window to focus
            time.sleep(0.5)
            return True

        except Exception as e:
            self.logger.error(f"Error focusing EVE Online client: {str(e)}")
            return False

    def execute_buy_order(self, item_name: str, quantity: str, price: str = "1") -> Tuple[bool, str]:
        """
        Execute a buy order for an item.

        Args:
            item_name: Name of the item to buy
            quantity: Quantity to buy
            price: Price to set for the buy order

        Returns:
            Tuple of (success, error_message)
            - success: True if successful, False otherwise
            - error_message: Error message if unsuccessful, empty string otherwise
        """
        try:
            # Create a temporary file with the price information
            import tempfile
            import os

            # Create a temp file in the system temp directory
            temp_dir = tempfile.gettempdir()
            price_file_path = os.path.join(temp_dir, "tinygamer_price.txt")

            # Format the price to ensure it has a decimal point and two decimal places
            # Also add a competitive increment to the price based on its value
            try:
                # Convert to float
                price_float = float(price)

                # Determine the increment based on price value
                if price_float < 100.0:
                    increment = 0.01
                    increment_desc = "0.01 ISK"
                elif price_float < 1000.0:
                    increment = 1.0
                    increment_desc = "1 ISK"
                elif price_float < 100000.0:
                    increment = 10.0
                    increment_desc = "10 ISK"
                else:
                    increment = 100.0
                    increment_desc = "100 ISK"

                # Apply the increment
                adjusted_price = price_float + increment
                formatted_price = f"{adjusted_price:.2f}"
                self.logger.info(f"Adjusted price from {price} to {formatted_price} (+{increment_desc})")
                price = formatted_price
            except ValueError:
                self.logger.warning(f"Could not format price {price}, using as is")

            # Write the price to the temp file
            with open(price_file_path, "w") as f:
                f.write(price)

            self.logger.info(f"Saved price {price} to temporary file: {price_file_path}")

            # Copy item and quantity to clipboard
            clipboard_text = f"{item_name} {quantity}"
            pyperclip.copy(clipboard_text)

            self.logger.info(f"Copied to clipboard: {clipboard_text}")

            # Focus EVE client
            if not self.focus_eve_client():
                error_msg = "Failed to focus EVE Online client. Please make sure EVE is running."
                self.logger.error(error_msg)
                return False, error_msg

            # Wait for EVE to be ready before sending the key
            # Start with a shorter initial wait
            initial_wait = 10  # Increased to 10 seconds as per user request
            self.logger.info(f"Waiting {initial_wait} seconds before checking EVE readiness...")
            time.sleep(initial_wait)

            # Check if EVE is active, if not, wait a bit more
            if platform.system() == "Darwin":
                workspace = AppKit.NSWorkspace.sharedWorkspace()
                apps = workspace.runningApplications()
                eve_app = None

                for app in apps:
                    app_name = app.localizedName()
                    if app_name and ("EVE" in app_name):
                        eve_app = app
                        break

                if eve_app and not eve_app.isActive():
                    additional_wait = 5
                    self.logger.info(f"EVE is not active yet, waiting {additional_wait} more seconds...")
                    time.sleep(additional_wait)

                    # Check again if EVE is active after waiting
                    if not eve_app.isActive():
                        self.logger.warning("EVE is still not active after waiting. The user may need to manually click on the EVE window.")

            # Press the hotkey to execute the buy order task (page up is mapped to TaskMarketWindowPlaceBuyOrder)
            # This is handled by the EVE director's _on_key_press method
            try:
                import pyautogui
                self.logger.info("Sending 'page up' key to trigger buy order task...")
                pyautogui.press('pageup')
            except Exception as e:
                error_msg = f"Failed to send key to EVE: {str(e)}"
                self.logger.error(error_msg)
                return False, error_msg

            self.logger.info("Triggered buy order task in TinyGamer")

            # Wait for task status update
            status_available, status_data = self.check_task_status(timeout=2.0)
            if status_available and 'success' in status_data:
                if status_data['success']:
                    self.logger.info(f"Buy order task completed successfully: {status_data.get('message', '')}")
                    return True, ""
                else:
                    error_msg = f"Buy order task failed: {status_data.get('message', 'Unknown error')}"
                    self.logger.error(error_msg)
                    return False, error_msg

            # If no status update, assume it's still processing
            self.logger.info("No immediate status update from TinyGamer. Task may still be processing.")
            return True, ""

        except Exception as e:
            error_msg = f"Error executing buy order: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def check_task_status(self, timeout: float = 0.0, clear_status: bool = False) -> Tuple[bool, Dict[str, Any]]:
        """
        Check the status of the most recent TinyGamer task.

        Args:
            timeout: Time in seconds to wait for a status update. If 0, returns immediately.
            clear_status: Whether to clear the status file after reading it

        Returns:
            Tuple of (status_available, status_data)
            - status_available: True if status data is available, False otherwise
            - status_data: Dictionary with task status information if available, empty dict otherwise
        """
        try:
            import tempfile
            import os

            # Get the temp file path
            temp_dir = tempfile.gettempdir()
            status_file_path = os.path.join(temp_dir, "tinygamer_status.json")

            # If timeout is specified, wait for the status file to appear or be updated
            if timeout > 0:
                start_time = time.time()
                last_modified = 0

                if os.path.exists(status_file_path):
                    last_modified = os.path.getmtime(status_file_path)

                while time.time() - start_time < timeout:
                    if os.path.exists(status_file_path):
                        current_modified = os.path.getmtime(status_file_path)
                        if current_modified > last_modified:
                            # File has been updated, break the loop
                            break

                    # Sleep briefly to avoid high CPU usage
                    time.sleep(0.1)

            # Check if the status file exists
            if not os.path.exists(status_file_path):
                return False, {}

            # Read the status file
            with open(status_file_path, "r") as f:
                status_data = json.load(f)

            # Clear the status file if requested
            if clear_status:
                try:
                    os.remove(status_file_path)
                    self.logger.info(f"Cleared status file: {status_file_path}")
                except Exception as e:
                    self.logger.warning(f"Failed to clear status file: {str(e)}")

            return True, status_data

        except Exception as e:
            self.logger.error(f"Error checking task status: {str(e)}")
            return False, {}

    def get_tinygamer_logs(self) -> Tuple[bool, List[str]]:
        """
        Read new log entries from the TinyGamer log file.

        Returns:
            Tuple of (success, log_lines)
            - success: True if log file was read successfully, False otherwise
            - log_lines: List of new log lines read from the file
        """
        try:
            # Check if log file exists
            if not os.path.exists(self.log_file_path):
                self.logger.warning(f"TinyGamer log file not found at {self.log_file_path}")
                return False, []

            # Read new log entries
            with open(self.log_file_path, 'r') as f:
                # Seek to the last position we read
                f.seek(self.last_log_position)

                # Read new lines
                new_lines = f.readlines()

                # Update the last position
                self.last_log_position = f.tell()

            # Strip newlines and return
            return True, [line.strip() for line in new_lines]

        except Exception as e:
            self.logger.error(f"Error reading TinyGamer log file: {str(e)}")
            return False, []

    def execute_sell_inventory(self) -> Tuple[bool, str]:
        """
        Execute the sell inventory task to sell all items in the station inventory.

        Returns:
            Tuple of (success, error_message)
            - success: True if successful, False otherwise
            - error_message: Error message if unsuccessful, empty string otherwise
        """
        try:
            # Focus EVE client
            if not self.focus_eve_client():
                error_msg = "Failed to focus EVE Online client. Please make sure EVE is running."
                self.logger.error(error_msg)
                return False, error_msg

            # Wait for EVE to be ready before sending the key
            initial_wait = 10  # Wait 10 seconds before sending the key
            self.logger.info(f"Waiting {initial_wait} seconds before checking EVE readiness...")
            time.sleep(initial_wait)

            # Check if EVE is active, if not, wait a bit more
            if platform.system() == "Darwin":
                workspace = AppKit.NSWorkspace.sharedWorkspace()
                apps = workspace.runningApplications()
                eve_app = None

                for app in apps:
                    app_name = app.localizedName()
                    if app_name and ("EVE" in app_name):
                        eve_app = app
                        break

                if eve_app and not eve_app.isActive():
                    additional_wait = 5
                    self.logger.info(f"EVE is not active yet, waiting {additional_wait} more seconds...")
                    time.sleep(additional_wait)

                    # Check again if EVE is active after waiting
                    if not eve_app.isActive():
                        self.logger.warning("EVE is still not active after waiting. The user may need to manually click on the EVE window.")

            # Press the hotkey to execute the sell inventory task (page down is mapped to TaskStationInventorySellAllItems)
            try:
                import pyautogui
                self.logger.info("Sending 'page down' key to trigger sell inventory task...")
                pyautogui.press('pagedown')
            except Exception as e:
                error_msg = f"Failed to send key to EVE: {str(e)}"
                self.logger.error(error_msg)
                return False, error_msg

            self.logger.info("Triggered sell inventory task in TinyGamer")

            # Wait for task status update
            status_available, status_data = self.check_task_status(timeout=2.0)
            if status_available and 'success' in status_data:
                if status_data['success']:
                    self.logger.info(f"Sell inventory task completed successfully: {status_data.get('message', '')}")
                    return True, ""
                else:
                    error_msg = f"Sell inventory task failed: {status_data.get('message', 'Unknown error')}"
                    self.logger.error(error_msg)
                    return False, error_msg

            # If no status update, assume it's still processing
            self.logger.info("No immediate status update from TinyGamer. Task may still be processing.")
            return True, ""

        except Exception as e:
            error_msg = f"Error executing sell inventory task: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def shutdown(self):
        """Shutdown TinyGamer if it was launched by this bridge."""
        if self.tinygamer_process and self.tinygamer_process.poll() is None:
            self.logger.info("Shutting down TinyGamer...")
            self.tinygamer_process.terminate()
            self.tinygamer_process.wait(timeout=5)
            self.logger.info("TinyGamer shut down")
