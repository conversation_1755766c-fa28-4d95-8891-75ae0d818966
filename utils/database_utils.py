"""
Database Utilities for EVE Trader

This module provides utility functions for database operations.
"""

import sqlite3
import os
from datetime import datetime, timezone

def ensure_api_cache_table_exists(db_path):
    """
    Checks if the api_cache table exists in the database and creates it if it doesn't.
    
    Args:
        db_path: Path to the SQLite database file
        
    Returns:
        bool: True if the table exists or was created successfully, False otherwise
    """
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if the api_cache table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_cache'")
        if cursor.fetchone() is None:
            print("api_cache table does not exist. Creating it...")
            
            # Create api_cache table
            sql_create_api_cache_table = """
            CREATE TABLE api_cache (
                cache_key TEXT PRIMARY KEY,
                endpoint TEXT NOT NULL,
                parameters TEXT NOT NULL,
                response_data BLOB NOT NULL,
                cached_at DATETIME NOT NULL,
                expires_at DATETIME NOT NULL,
                etag TEXT
            );
            """
            cursor.execute(sql_create_api_cache_table)
            
            # Create index
            cursor.execute("CREATE INDEX idx_api_cache_expires_at ON api_cache (expires_at)")
            
            conn.commit()
            print("api_cache table created successfully.")
            
            # Close the connection
            conn.close()
            return True
        else:
            print("api_cache table already exists.")
            conn.close()
            return True
            
    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        if conn:
            conn.close()
        return False
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.close()
        return False
