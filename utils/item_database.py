"""
Item Database Utility for EVE Trader

This module provides a utility class for managing EVE Online item data,
specifically mapping item IDs to item names and other metadata.
"""

import os
import sqlite3
import csv
import requests
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import json
from datetime import datetime, timezone
import logging
import difflib

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default paths
DEFAULT_DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'eve_trader.db')
DEFAULT_CACHE_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'cache')

# Fuzzwork SDE URLs
FUZZWORK_TYPES_URL = "https://www.fuzzwork.co.uk/dump/latest/invTypes.csv"
FUZZWORK_GROUPS_URL = "https://www.fuzzwork.co.uk/dump/latest/invGroups.csv"
FUZZWORK_CATEGORIES_URL = "https://www.fuzzwork.co.uk/dump/latest/invCategories.csv"

class ItemDatabase:
    """
    Manages a local database of EVE Online items, mapping item IDs to names and other metadata.
    """

    def __init__(self, db_path: str = DEFAULT_DB_PATH, cache_dir: str = DEFAULT_CACHE_DIR):
        """
        Initialize the ItemDatabase with the specified database path.

        Args:
            db_path: Path to the SQLite database file
            cache_dir: Directory to store downloaded SDE files
        """
        self.db_path = db_path
        self.cache_dir = cache_dir
        self._ensure_cache_dir()
        self._ensure_table_exists()

    def _ensure_cache_dir(self):
        """Ensure the cache directory exists."""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            logger.info(f"Created cache directory: {self.cache_dir}")

    def _get_connection(self):
        """Get a database connection with row factory."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn

    def _ensure_table_exists(self):
        """Ensure the eve_items table exists in the database."""
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='eve_items'")
            if cursor.fetchone() is None:
                logger.info("Creating eve_items table...")

                # Create the table
                cursor.execute("""
                CREATE TABLE eve_items (
                    item_id INTEGER PRIMARY KEY,
                    item_name TEXT NOT NULL,
                    group_id INTEGER,
                    group_name TEXT,
                    category_id INTEGER,
                    category_name TEXT,
                    published BOOLEAN DEFAULT 1,
                    market_group_id INTEGER,
                    volume REAL,
                    last_updated DATETIME NOT NULL
                )
                """)

                # Create indexes
                cursor.execute("CREATE INDEX idx_eve_items_name ON eve_items (item_name)")
                cursor.execute("CREATE INDEX idx_eve_items_group ON eve_items (group_id)")
                cursor.execute("CREATE INDEX idx_eve_items_category ON eve_items (category_id)")

                conn.commit()
                logger.info("eve_items table created successfully")
            else:
                logger.debug("eve_items table already exists")
        except sqlite3.Error as e:
            logger.error(f"Database error: {e}")
        finally:
            conn.close()

    def get_item_name(self, item_id: int) -> str:
        """
        Get the name of an item by its ID.

        Args:
            item_id: The EVE Online item type ID

        Returns:
            The item name if found, otherwise a placeholder string
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT item_name FROM eve_items WHERE item_id = ?", (item_id,))
            row = cursor.fetchone()
            if row:
                return row['item_name']
            return f"Item {item_id}"  # Placeholder for unknown items
        except sqlite3.Error as e:
            logger.error(f"Error getting item name for ID {item_id}: {e}")
            return f"Item {item_id}"  # Fallback on error
        finally:
            conn.close()

    def get_item_details(self, item_id: int) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an item by its ID.

        Args:
            item_id: The EVE Online item type ID

        Returns:
            A dictionary with item details if found, None otherwise
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM eve_items WHERE item_id = ?", (item_id,))
            row = cursor.fetchone()
            if row:
                return dict(row)
            return None
        except sqlite3.Error as e:
            logger.error(f"Error getting item details for ID {item_id}: {e}")
            return None
        finally:
            conn.close()

    def _calculate_similarity(self, search_term: str, item_name: str) -> float:
        """
        Calculate the similarity between a search term and an item name.

        Args:
            search_term: The search term to compare against
            item_name: The item name to compare

        Returns:
            A similarity score between 0 and 1, where 1 is an exact match
        """
        # Convert both strings to lowercase for case-insensitive comparison
        search_term = search_term.lower()
        item_name_lower = item_name.lower()

        # Check for exact match or if item name starts with the search term
        if item_name_lower == search_term:
            return 1.0
        elif item_name_lower.startswith(search_term):
            return 0.9

        # Use difflib's SequenceMatcher for more complex similarity
        similarity = difflib.SequenceMatcher(None, search_term, item_name_lower).ratio()

        # Boost similarity for items that contain the search term as a whole word
        if search_term in item_name_lower.split():
            similarity = max(similarity, 0.8)

        return similarity

    def search_items(self, name_pattern: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Search for items by name pattern.

        Args:
            name_pattern: The pattern to search for (SQL LIKE pattern)
            limit: Maximum number of results to return

        Returns:
            A list of dictionaries with item details, sorted by similarity to the search pattern
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            # First, get all matching items without sorting or limiting
            cursor.execute(
                "SELECT * FROM eve_items WHERE item_name LIKE ?",
                (f"%{name_pattern}%",)
            )
            results = [dict(row) for row in cursor.fetchall()]

            # If we have a search term, sort by similarity
            if name_pattern:
                # Calculate similarity for each result and add it as a field
                for result in results:
                    result['similarity'] = self._calculate_similarity(name_pattern, result['item_name'])

                # Sort by similarity (highest first)
                results.sort(key=lambda x: x['similarity'], reverse=True)

            # Limit the results
            return results[:limit]
        except sqlite3.Error as e:
            logger.error(f"Error searching for items with pattern '{name_pattern}': {e}")
            return []
        finally:
            conn.close()

    def add_or_update_item(self, item_id: int, item_name: str, **kwargs) -> bool:
        """
        Add or update an item in the database.

        Args:
            item_id: The EVE Online item type ID
            item_name: The name of the item
            **kwargs: Additional item attributes to store

        Returns:
            True if successful, False otherwise
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Prepare data for insertion/update
            now = datetime.now(timezone.utc).isoformat()
            data = {
                'item_id': item_id,
                'item_name': item_name,
                'last_updated': now,
                **kwargs
            }

            # Get column names from the table
            cursor.execute("PRAGMA table_info(eve_items)")
            columns = [row['name'] for row in cursor.fetchall()]

            # Filter data to include only valid columns
            valid_data = {k: v for k, v in data.items() if k in columns}

            # Prepare SQL statement
            placeholders = ', '.join(['?'] * len(valid_data))
            columns_str = ', '.join(valid_data.keys())
            update_str = ', '.join([f"{k} = ?" for k in valid_data.keys() if k != 'item_id'])

            # Execute upsert
            sql = f"""
            INSERT INTO eve_items ({columns_str})
            VALUES ({placeholders})
            ON CONFLICT(item_id) DO UPDATE SET
                {update_str}
            """

            # Parameters for the SQL statement
            insert_params = list(valid_data.values())
            update_params = [valid_data[k] for k in valid_data.keys() if k != 'item_id']

            cursor.execute(sql, insert_params + update_params)
            conn.commit()
            return True
        except sqlite3.Error as e:
            logger.error(f"Error adding/updating item {item_id} ({item_name}): {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def bulk_add_or_update_items(self, items: List[Dict[str, Any]]) -> int:
        """
        Add or update multiple items in the database.

        Args:
            items: List of dictionaries with item data

        Returns:
            Number of items successfully added/updated
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()

            # Get column names from the table
            cursor.execute("PRAGMA table_info(eve_items)")
            columns = [row['name'] for row in cursor.fetchall()]

            # Prepare for batch insert
            now = datetime.now(timezone.utc).isoformat()
            count = 0

            # Begin transaction
            conn.execute("BEGIN TRANSACTION")

            for item in items:
                if 'item_id' not in item or 'item_name' not in item:
                    logger.warning(f"Skipping item without required fields: {item}")
                    continue

                # Add last_updated timestamp
                item['last_updated'] = now

                # Filter data to include only valid columns
                valid_data = {k: v for k, v in item.items() if k in columns}

                # Prepare SQL statement
                placeholders = ', '.join(['?'] * len(valid_data))
                columns_str = ', '.join(valid_data.keys())
                update_str = ', '.join([f"{k} = ?" for k in valid_data.keys() if k != 'item_id'])

                # Execute upsert
                sql = f"""
                INSERT INTO eve_items ({columns_str})
                VALUES ({placeholders})
                ON CONFLICT(item_id) DO UPDATE SET
                    {update_str}
                """

                # Parameters for the SQL statement
                insert_params = list(valid_data.values())
                update_params = [valid_data[k] for k in valid_data.keys() if k != 'item_id']

                cursor.execute(sql, insert_params + update_params)
                count += 1

                # Commit every 1000 items to avoid large transactions
                if count % 1000 == 0:
                    conn.commit()
                    conn.execute("BEGIN TRANSACTION")
                    logger.info(f"Processed {count} items...")

            # Commit final batch
            conn.commit()
            logger.info(f"Successfully added/updated {count} items")
            return count
        except sqlite3.Error as e:
            logger.error(f"Error in bulk add/update: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()

    def count_items(self) -> int:
        """
        Count the number of items in the database.

        Returns:
            The number of items in the database
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) as count FROM eve_items")
            return cursor.fetchone()['count']
        except sqlite3.Error as e:
            logger.error(f"Error counting items: {e}")
            return 0
        finally:
            conn.close()

    def download_sde_file(self, url: str, filename: str) -> Optional[str]:
        """
        Download an SDE file from the given URL.

        Args:
            url: The URL to download from
            filename: The name to save the file as

        Returns:
            The path to the downloaded file, or None if download failed
        """
        file_path = os.path.join(self.cache_dir, filename)

        # Check if file already exists and is recent (less than 1 day old)
        if os.path.exists(file_path):
            file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(file_path))
            if file_age.days < 1:
                logger.info(f"Using cached file: {file_path}")
                return file_path

        # Download the file
        try:
            logger.info(f"Downloading {url} to {file_path}")
            response = requests.get(url, stream=True)
            response.raise_for_status()

            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"Downloaded {url} to {file_path}")
            return file_path
        except requests.exceptions.RequestException as e:
            logger.error(f"Error downloading {url}: {e}")
            return None

    def import_from_fuzzwork(self) -> bool:
        """
        Import item data from Fuzzwork's CSV dumps of the EVE SDE.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Download the necessary files
            types_file = self.download_sde_file(FUZZWORK_TYPES_URL, "invTypes.csv")
            groups_file = self.download_sde_file(FUZZWORK_GROUPS_URL, "invGroups.csv")
            categories_file = self.download_sde_file(FUZZWORK_CATEGORIES_URL, "invCategories.csv")

            if not all([types_file, groups_file, categories_file]):
                logger.error("Failed to download one or more required SDE files")
                return False

            # Load groups and categories into memory for lookup
            groups = {}
            categories = {}

            # Parse categories
            logger.info("Parsing categories...")
            with open(categories_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    category_id = int(row['categoryID'])
                    categories[category_id] = {
                        'category_id': category_id,
                        'category_name': row['categoryName'],
                        'published': int(row['published'])
                    }

            # Parse groups
            logger.info("Parsing groups...")
            with open(groups_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    group_id = int(row['groupID'])
                    category_id = int(row['categoryID'])
                    category_data = categories.get(category_id, {})

                    groups[group_id] = {
                        'group_id': group_id,
                        'group_name': row['groupName'],
                        'category_id': category_id,
                        'category_name': category_data.get('category_name', ''),
                        'published': int(row['published'])
                    }

            # Parse and import types (items)
            logger.info("Parsing and importing items...")
            items = []
            with open(types_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    try:
                        item_id = int(row['typeID'])
                        group_id = int(row['groupID'])
                        group_data = groups.get(group_id, {})

                        # Skip unpublished items unless they're special items
                        published = int(row['published'])
                        if not published and item_id > 1000000:  # Skip unpublished items except for special IDs
                            continue

                        # Create item data
                        item_data = {
                            'item_id': item_id,
                            'item_name': row['typeName'],
                            'group_id': group_id,
                            'group_name': group_data.get('group_name', ''),
                            'category_id': group_data.get('category_id'),
                            'category_name': group_data.get('category_name', ''),
                            'published': published,
                            'volume': float(row['volume']) if row['volume'] else 0
                        }

                        # Add market group if available
                        if row['marketGroupID'] and row['marketGroupID'] != 'None':
                            item_data['market_group_id'] = int(row['marketGroupID'])

                        items.append(item_data)

                        # Process in batches to avoid memory issues
                        if len(items) >= 5000:
                            self.bulk_add_or_update_items(items)
                            items = []
                    except (ValueError, KeyError) as e:
                        logger.warning(f"Error processing row: {e}")
                        continue

            # Process any remaining items
            if items:
                self.bulk_add_or_update_items(items)

            logger.info(f"Import completed. Total items in database: {self.count_items()}")
            return True
        except Exception as e:
            logger.error(f"Error importing from Fuzzwork: {e}")
            return False

    def import_from_esi_universe_types(self, batch_size: int = 1000) -> bool:
        """
        Import item data from ESI universe/types endpoint.
        This is a slower method but can be used as a fallback.

        Args:
            batch_size: Number of items to process in each batch

        Returns:
            True if successful, False otherwise
        """
        # This method would use the ESI API to fetch type IDs and details
        # It's more complex and slower than using Fuzzwork's dumps
        # Implement if needed as a fallback
        logger.warning("import_from_esi_universe_types not implemented yet")
        return False


# Singleton instance for easy access
_item_db_instance = None

def get_item_database(db_path: str = DEFAULT_DB_PATH) -> ItemDatabase:
    """
    Get the singleton instance of the ItemDatabase.

    Args:
        db_path: Path to the SQLite database file

    Returns:
        The ItemDatabase instance
    """
    global _item_db_instance
    if _item_db_instance is None:
        _item_db_instance = ItemDatabase(db_path)
    return _item_db_instance


# Example usage
if __name__ == "__main__":
    # Initialize the database
    item_db = get_item_database()

    # Check if we have items
    count = item_db.count_items()
    print(f"Current item count: {count}")

    # Import from Fuzzwork if needed
    if count < 1000:  # Arbitrary threshold
        print("Importing items from Fuzzwork...")
        item_db.import_from_fuzzwork()

    # Test lookup
    test_ids = [34, 35, 36, 37, 38, 39, 40]  # Some common items
    for item_id in test_ids:
        name = item_db.get_item_name(item_id)
        print(f"Item {item_id}: {name}")

    # Test search
    search_results = item_db.search_items("Tritanium")
    print(f"Search results for 'Tritanium': {len(search_results)}")
    for item in search_results:
        print(f"  {item['item_id']}: {item['item_name']}")
