"""
Update transaction item names in the database.

This script updates all transactions in the database with the correct item names
from the eve_items table.
"""

import sqlite3
import os
import time
from eve_trader.utils.item_database import get_item_database

def main():
    """Main function to update transaction item names."""
    print("Updating transaction item names...")
    
    db_path = "eve_trader.db"
    
    if not os.path.exists(db_path):
        print(f"Database file not found: {db_path}")
        return 1
    
    # Get the item database instance
    item_db = get_item_database()
    
    # Check if we have items
    count = item_db.count_items()
    print(f"Item database contains {count} items")
    
    if count < 1000:
        print("Item database needs initialization. Starting import...")
        success = item_db.import_from_fuzzwork()
        if not success:
            print("Failed to initialize item database")
            return 1
        print(f"Item database initialized with {item_db.count_items()} items")
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    
    try:
        # Get all transactions
        cursor = conn.cursor()
        cursor.execute("SELECT tx_id, item_id, item_name FROM transactions")
        transactions = cursor.fetchall()
        
        print(f"Found {len(transactions)} transactions to update")
        
        # Start a transaction
        conn.execute("BEGIN TRANSACTION")
        
        # Update each transaction with the correct item name
        updated_count = 0
        start_time = time.time()
        
        for tx in transactions:
            tx_id = tx['tx_id']
            item_id = tx['item_id']
            current_name = tx['item_name']
            
            # Get the correct item name from the item database
            correct_name = item_db.get_item_name(item_id)
            
            # Skip if the name is already correct or still a placeholder
            if current_name == correct_name or correct_name.startswith("Item "):
                continue
            
            # Update the transaction
            cursor.execute(
                "UPDATE transactions SET item_name = ? WHERE tx_id = ?",
                (correct_name, tx_id)
            )
            updated_count += 1
            
            # Print progress every 100 updates
            if updated_count % 100 == 0:
                print(f"Updated {updated_count} transactions...")
        
        # Commit the transaction
        conn.commit()
        
        end_time = time.time()
        print(f"Updated {updated_count} transactions in {end_time - start_time:.2f} seconds")
        
        # Verify the updates
        cursor.execute("SELECT tx_id, item_id, item_name FROM transactions LIMIT 10")
        updated_transactions = cursor.fetchall()
        
        print("\nVerifying updates:")
        for tx in updated_transactions:
            print(f"  TX ID: {tx['tx_id']}, Item ID: {tx['item_id']}, Item Name: {tx['item_name']}")
        
        return 0
    
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        conn.rollback()
        return 1
    
    finally:
        conn.close()

if __name__ == "__main__":
    exit(main())
