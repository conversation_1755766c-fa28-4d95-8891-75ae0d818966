# Design Document: EVE Online Trading Bot Accounting & Strategy Tracking System

## 1. Project Overview
This system tracks EVE Online trading bot transactions in Jita, calculates profits including taxes/fees, classifies trades by strategy, analyzes performance, and maintains inventory valuation.

### 1.1 Objectives
- Track market transactions with detailed logging
- Calculate profits/losses including all taxes and fees
- Classify trades by strategy 
- Track performance metrics by strategy
- Maintain inventory valuation and cost basis tracking
- Provide insightful reporting and analysis
- Efficiently retrieve data from EVE ESI API

### 1.2 Target Environment
- Single character trading in Jita 4-4
- Python with PyQt5 UI and SQLite database
- EVE Online ESI API integration

## 2. Technical Architecture

### 2.1 System Components
1. **Data Acquisition Layer**
   - Consumes transaction logs
   - Interfaces with EVE ESI API
   - Implements rate limiting and pagination handling
   - Manages OAuth2 authentication with EVE SSO

2. **Core Accounting Engine**
   - Logs transactions with metadata
   - Calculates net profit after fees
   - Maintains inventory cost basis
   - Matches transactions for profit calculation

3. **Strategy Classification System**
   - Applies rule-based classification
   - Tags transactions with strategy identifiers

4. **Performance Analysis Module**
   - Calculates metrics by strategy
   - Tracks capital allocation and ROI
   - Generates performance reports

5. **Data Storage**
   - SQLite database with optimized schema
   - Caching layer for ESI responses

6. **UI Layer (PyQt5)**
   - Data display and management
   - Authentication and configuration
   - Reporting and visualization

### 2.2 Data Flow
```
[Bot Transaction] → [Data Acquisition] → [Core Accounting] → [Strategy Classification] 
                                           ↓
[UI Layer] ← [Performance Analysis] ← [Data Storage]
```

## 3. Database Schema

### 3.1 Core Tables

#### Transactions Table
```sql
CREATE TABLE transactions (
    tx_id INTEGER PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    item_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(16,2) NOT NULL,
    total_price DECIMAL(20,2) NOT NULL,
    transaction_type TEXT NOT NULL CHECK(transaction_type IN ('BUY','SELL')),
    order_id INTEGER,
    broker_fee DECIMAL(16,2),
    sales_tax DECIMAL(16,2),
    net_amount DECIMAL(20,2) NOT NULL,
    strategy TEXT,
    profit DECIMAL(20,2),
    location_id INTEGER NOT NULL,  -- Jita 4-4 station ID
    is_buy_order BOOLEAN,
    client_id INTEGER,
    journal_ref_id INTEGER,
    notes TEXT
);
```

#### Orders Table
```sql
CREATE TABLE orders (
    order_id INTEGER PRIMARY KEY,
    item_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    order_type TEXT NOT NULL CHECK(order_type IN ('BUY','SELL')),
    quantity_original INTEGER NOT NULL,
    quantity_remaining INTEGER NOT NULL,
    price DECIMAL(16,2) NOT NULL,
    placed_time DATETIME NOT NULL,
    fulfilled_time DATETIME,
    expired BOOLEAN DEFAULT FALSE,
    canceled BOOLEAN DEFAULT FALSE,
    broker_fee DECIMAL(16,2) NOT NULL,
    relist_count INTEGER DEFAULT 0,
    relist_fees DECIMAL(16,2) DEFAULT 0,
    strategy TEXT,
    location_id INTEGER NOT NULL,  -- Jita 4-4 station ID
    range TEXT,
    escrow DECIMAL(20,2),
    is_corporation BOOLEAN DEFAULT FALSE,
    esi_expires DATETIME,  -- When ESI data expires
    last_updated DATETIME  -- When we last checked this order
);
```

#### Inventory Table
```sql
CREATE TABLE inventory (
    inventory_id INTEGER PRIMARY KEY,
    item_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    avg_cost_per_unit DECIMAL(16,2) NOT NULL,
    total_cost DECIMAL(20,2) NOT NULL,
    last_updated DATETIME NOT NULL,
    allocated_strategy TEXT,
    acquisition_date DATETIME NOT NULL,
    location_id INTEGER NOT NULL,  -- Jita 4-4 station ID
    UNIQUE(item_id)
);
```

#### Market Prices Table
```sql
CREATE TABLE market_prices (
    price_id INTEGER PRIMARY KEY,
    item_id INTEGER NOT NULL,
    item_name TEXT NOT NULL,
    timestamp DATETIME NOT NULL,
    buy_max DECIMAL(16,2),
    buy_min DECIMAL(16,2),
    buy_avg DECIMAL(16,2),
    sell_max DECIMAL(16,2),
    sell_min DECIMAL(16,2),
    sell_avg DECIMAL(16,2),
    volume INTEGER,
    orders_buy INTEGER,
    orders_sell INTEGER,
    region_id INTEGER NOT NULL,  -- The Forge region ID for Jita
    esi_expires DATETIME,        -- When ESI data expires
    UNIQUE(item_id, timestamp)
);
```

#### Fee Rates Table
```sql
CREATE TABLE fee_rates (
    id INTEGER PRIMARY KEY,
    effective_date DATETIME NOT NULL,
    broker_fee_base DECIMAL(5,4) NOT NULL,
    sales_tax_base DECIMAL(5,4) NOT NULL,
    broker_fee_effective DECIMAL(5,4) NOT NULL,
    sales_tax_effective DECIMAL(5,4) NOT NULL,
    relisting_fee_modifier DECIMAL(5,4) NOT NULL,
    notes TEXT
);
```

#### API Tokens Table
```sql
CREATE TABLE api_tokens (
    id INTEGER PRIMARY KEY,
    character_id INTEGER NOT NULL,
    character_name TEXT NOT NULL,
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    token_type TEXT NOT NULL,
    expires_at DATETIME NOT NULL,
    scopes TEXT NOT NULL,
    last_refreshed DATETIME NOT NULL,
    UNIQUE(character_id)
);
```

#### API Cache Table
```sql
CREATE TABLE api_cache (
    cache_key TEXT PRIMARY KEY,
    endpoint TEXT NOT NULL,
    parameters TEXT NOT NULL,
    response_data BLOB NOT NULL,
    cached_at DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    etag TEXT
);
```

## 4. Core Functionality

### 4.1 Transaction Logging
- Log transaction details (item, quantity, price, type, fees)
- Validate and normalize data
- Correlate with existing orders

### 4.2 Profit & Loss Calculation
- Include broker fees, sales tax, and relisting fees
- Calculate true profit: `Profit = (Sell revenue after tax) - (Buy cost) - (All fees)`
- Track cost basis using weighted average method

### 4.3 Strategy Classification
Classify trades into:

| Strategy | Hold Duration | Price Change | Other Criteria |
|----------|---------------|--------------|----------------|
| Margin Trading | < 3 days | 5-20% | Same station |
| Speculation | 3-30 days | >20% | Deliberate timing |
| Arbitrage | < 1 hour | Any | Quick turnaround |
| Long-Term Hold | > 30 days | Any | Capital preservation |

### 4.4 Inventory and Cost Basis Tracking
- Track quantity and cost of all items
- Update cost basis when buying
- Calculate unrealized profit based on market prices

### 4.5 Performance Analysis
- Track profit by strategy
- Calculate ROI and capital efficiency
- Measure win rate and average profit per trade

## 5. Implementation Plan

### 5.1 Phase 1: ESI Integration
- Implement OAuth2 authentication
- Create token management
- Build ESI client with rate limiting
- Implement caching layer

### 5.2 Phase 2: Core Data Model
- Implement database schema
- Build transaction logging
- Establish inventory tracking

### 5.3 Phase 3: Accounting Engine
- Implement fee calculation
- Build profit calculation
- Create cost basis tracking

### 5.4 Phase 4: Strategy Classification
- Implement classification rules
- Build strategy tagging system

### 5.5 Phase 5: Performance Analysis
- Implement performance metrics
- Create reporting views

### 5.6 Phase 6: UI Development
- Build PyQt interface
- Create data visualization
- Implement user controls

## 6. Technical Considerations

### 6.1 Performance
- Index transaction table on timestamp, item_id, and strategy
- Use batch processing for historical analysis
- Implement connection pooling for API calls
- Use asynchronous programming for API requests

### 6.2 Data Integrity
- Validate transactions
- Use SQLite transactions for ACID compliance
- Create backup routines
- Implement error handling with retries

### 6.3 Rate Limiting and API Efficiency
- Respect ESI rate limits (market history: 300 requests/minute)
- Implement intelligent caching
- Use gzip compression
- Handle pagination properly
- Monitor API error limits (100 errors/minute max)

### 6.4 Authentication and Security
- Securely store refresh tokens
- Auto-refresh expired tokens
- Verify token scopes

## 7. System API Design

### 7.1 Core API Functions
```python
# Transaction Management
log_transaction(...)  # Record transaction
get_transactions(filters)  # Get filtered transactions

# Order Management
create_order(...)  # Record market order
update_order(order_id, ...)  # Update order status

# Inventory Management
get_inventory()  # Get current inventory
update_cost_basis(item_id, ...)  # Adjust cost basis

# Profit Calculation
calculate_profit(tx_id)  # Calculate profit

# Strategy Analysis
classify_transaction(tx_id)  # Assign strategy
get_strategy_performance(...)  # Get metrics by strategy

# Market Data
update_market_prices()  # Fetch current prices
```

### 7.2 ESI API Integration

#### Authentication and SSO
```python
# SSO Authentication
initialize_oauth()  # Set up OAuth2 flow
get_auth_url(scopes)  # Generate authentication URL
process_callback(code)  # Process callback
refresh_access_token(refresh_token)  # Refresh token
```

#### Data Synchronization
```python
# Full data synchronization
sync_all_data()  # Master sync function
sync_wallet_transactions()  # Get transactions
sync_wallet_journal()  # Get journal entries
sync_market_orders()  # Get current orders
sync_market_order_history()  # Get historical orders
sync_market_prices()  # Get current prices
```

## 8. Data Synchronization

### 8.1 Historical Data Synchronization

The "Update Data" button performs an intelligent differential sync:

- Checks what data is already present
- Retrieves only missing data
- Displays progress indicators
- Generates summary upon completion

Implementation:
```python
def sync_historical_data():
    """Synchronize all historical data with differential updates"""
    # Get last sync timestamps
    last_wallet_sync = get_last_sync_time('wallet_transactions')
    
    # Track progress
    update_progress(0, "Preparing synchronization...")
    
    # Sync wallet transactions
    if last_wallet_sync:
        # Incremental sync
        new_transactions = get_transactions_since(last_wallet_sync)
    else:
        # First-time sync (up to 90 days)
        new_transactions = get_all_available_transactions()
    
    # Update timestamps and generate summary
    set_last_sync_time('wallet_transactions', datetime.now())
    return summary_data
```

### 8.2 Real-Time Market Data

Separate from historical data, market data is retrieved on-demand with short-lived caching:

```python
def get_current_market_data(item_id, region_id=JITA_REGION):
    """Get current market data with short-lived caching"""
    cache_key = f"market_data_{region_id}_{item_id}"
    cached_data = cache.get(cache_key)
    
    # Return cached data if fresh (< 5 minutes old)
    if cached_data and (datetime.now() - cached_data['timestamp']).seconds < 300:
        return cached_data['data']
    
    # Fetch fresh data
    market_data = fetch_market_orders(region_id, item_id)
    
    # Cache results
    cache.set(cache_key, {
        'data': market_data,
        'timestamp': datetime.now()
    })
    
    return market_data
```

## 9. ESI API Details

### 9.1 Required API Endpoints

1. **Wallet Transactions**
   - Endpoint: `/v1/characters/{character_id}/wallet/transactions/`
   - Auth: Yes
   - Scope: `esi-wallet.read_character_wallet.v1`
   - Notes: Returns only recent transactions (typically 30-90 days)

2. **Wallet Journal**
   - Endpoint: `/v6/characters/{character_id}/wallet/journal/`
   - Auth: Yes
   - Scope: `esi-wallet.read_character_wallet.v1`
   - Notes: For fees and tax transactions

3. **Character Orders**
   - Endpoint: `/v2/characters/{character_id}/orders/`
   - Auth: Yes
   - Scope: `esi-markets.read_character_orders.v1`
   - Notes: For active orders

4. **Character Orders History**
   - Endpoint: `/v1/characters/{character_id}/orders/history/`
   - Auth: Yes
   - Scope: `esi-markets.read_character_orders.v1`
   - Notes: For completed/expired orders

5. **Market Orders**
   - Endpoint: `/v1/markets/{region_id}/orders/`
   - Auth: No
   - Notes: Returns up to 10,000 orders per page, supports pagination

6. **Market Prices**
   - Endpoint: `/v1/markets/prices/`
   - Auth: No
   - Notes: For inventory valuation

7. **Character Skills**
   - Endpoint: `/v4/characters/{character_id}/skills/`
   - Auth: Yes
   - Scope: `esi-skills.read_skills.v1`
   - Notes: For fee calculation based on skills

### 9.2 API Best Practices
- Use token bucket algorithm for rate limiting
- Respect error limit (max 100 errors/min)
- Implement ETags support
- Handle pagination via X-Pages header
- Use exponential backoff for retries

### 9.3 Character Skills and Fee Calculation

#### Key Trading Skills

1. **Accounting**
   - Reduces sales tax by 11% per level
   - Base tax: 7.5%
   - Minimum with Accounting V: 3.3%
   - Formula: `final_tax = base_tax * (1 - (0.11 * accounting_level))`

2. **Broker Relations**
   - Reduces broker fee by 0.3 percentage points per level
   - Base fee: 3%
   - Minimum: 1%
   - Formula: `broker_fee = 3% - (0.3% * broker_relations_level) - (0.03% * faction_standing) - (0.02% * corp_standing)`

```python
def calculate_sales_tax(base_amount, accounting_level):
    """Calculate sales tax based on skills."""
    base_tax_rate = 0.075  # 7.5%
    skill_reduction = 0.11 * accounting_level
    final_tax_rate = base_tax_rate * (1 - skill_reduction)
    return base_amount * final_tax_rate
```

## 10. User Interface

### 10.1 Technology Stack
- PyQt5 for UI framework
- Qt Designer for layouts
- QTableView/QTableWidget for data display
- QCharts for visualization
- QThreads for background processing

### 10.2 UI Layout

#### Initial Tabs

1. **Data Management Tab**
   - Login section
   - "Update Data" button with progress
   - Transaction log view
   - Order and inventory views

2. **Settings Tab**
   - API configuration
   - Fee calculation preferences
   - Database management

#### Future Tabs (Planned)
- Strategy Analysis
- Market Analysis
- Trading Execution

### 10.3 Jita-Only Focus
- Hard-coded values:
  - Jita Station ID: ********
  - The Forge Region ID: ********
  - Jita System ID: ********
- Simplified API calls and UI (no market selection)

### 10.4 PyQt Implementation

#### Project Structure
```
eve_trader/
├── main.py                  # Entry point
├── assets/                  # Icons
├── models/                  # Data models
├── views/                   # UI components
├── controllers/             # Business logic
└── api/                     # ESI integration
```

#### UI Loading
```python
from PyQt5 import uic

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        uic.loadUi('views/ui/main_window.ui', self)
        self.update_data_button.clicked.connect(self.on_update_data)
```

#### Threading Model
```python
class DataSyncWorker(QThread):
    progress_updated = pyqtSignal(int, str)
    sync_completed = pyqtSignal(bool, str)
    
    def run(self):
        try:
            self.progress_updated.emit(10, "Syncing wallet transactions...")
            # API calls here
            self.sync_completed.emit(True, "Sync completed")
        except Exception as e:
            self.sync_completed.emit(False, f"Sync failed: {str(e)}")
```

#### Database Connection Handling
```python
class Database:
    def __init__(self, db_path):
        self.db_path = db_path
        
    @contextmanager
    def get_connection(self):
        """Context manager for database connections."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
```