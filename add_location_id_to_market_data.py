#!/usr/bin/env python3
"""
Add location_id to market_data table

This script adds the location_id column to the market_data table and updates
the latest_market_data view to include location_id in grouping.
"""

import os
import sys
import sqlite3
from datetime import datetime

# Database file path
DATABASE_FILE = 'eve_trader.db'

def check_table_exists(conn, table_name):
    """Check if a table exists in the database."""
    cursor = conn.cursor()
    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}';")
    return cursor.fetchone() is not None

def check_column_exists(conn, table_name, column_name):
    """Check if a column exists in a table."""
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name});")
    columns = cursor.fetchall()
    return any(column[1] == column_name for column in columns)

def check_view_exists(conn, view_name):
    """Check if a view exists in the database."""
    cursor = conn.cursor()
    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='view' AND name='{view_name}';")
    return cursor.fetchone() is not None

def check_migration_status(conn, migration_name):
    """Check if a migration has already been completed."""
    try:
        cursor = conn.cursor()
        # First check if the migration_status table exists
        if not check_table_exists(conn, 'migration_status'):
            # Create the migration_status table if it doesn't exist
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS migration_status (
                migration_name TEXT PRIMARY KEY,
                completed BOOLEAN NOT NULL DEFAULT 0,
                completed_date DATETIME
            );
            """)
            conn.commit()
            return False
        
        # Check if this migration has been completed
        cursor.execute("SELECT completed FROM migration_status WHERE migration_name = ?", (migration_name,))
        result = cursor.fetchone()
        return result is not None and result[0] == 1
    except sqlite3.Error as e:
        print(f"Error checking migration status: {e}")
        return False

def execute_sql_script(conn, script_path):
    """Execute an SQL script file."""
    try:
        with open(script_path, 'r') as sql_file:
            sql_script = sql_file.read()
        
        conn.executescript(sql_script)
        conn.commit()
        return True
    except sqlite3.Error as e:
        print(f"Error executing SQL script: {e}")
        conn.rollback()
        return False
    except Exception as e:
        print(f"Error reading or executing SQL script: {e}")
        conn.rollback()
        return False

def main():
    """Main function to run the migration."""
    # Check if database file exists
    if not os.path.exists(DATABASE_FILE):
        print(f"Error: Database file not found: {DATABASE_FILE}")
        print("Please run the application first to create the database.")
        sys.exit(1)
    
    # Connect to the database
    try:
        conn = sqlite3.connect(DATABASE_FILE)
        conn.row_factory = sqlite3.Row
        print(f"Connected to database: {DATABASE_FILE}")
    except sqlite3.Error as e:
        print(f"Error connecting to database: {e}")
        sys.exit(1)
    
    try:
        # Check if market_data table exists
        if not check_table_exists(conn, 'market_data'):
            print("Error: market_data table does not exist.")
            print("Please run the application first to create the database schema.")
            conn.close()
            sys.exit(1)
        
        # Check if migration has already been completed
        migration_name = 'add_location_id_to_market_data'
        if check_migration_status(conn, migration_name):
            print(f"Migration '{migration_name}' has already been completed.")
            conn.close()
            sys.exit(0)
        
        # Check if location_id column already exists
        if check_column_exists(conn, 'market_data', 'location_id'):
            print("location_id column already exists in market_data table.")
            
            # Update migration status
            cursor = conn.cursor()
            cursor.execute(
                "INSERT OR REPLACE INTO migration_status (migration_name, completed, completed_date) VALUES (?, 1, datetime('now'))",
                (migration_name,)
            )
            conn.commit()
            print(f"Migration '{migration_name}' marked as completed.")
            conn.close()
            sys.exit(0)
        
        # Execute the SQL script
        sql_file = 'add_location_id_to_market_data.sql'
        if not os.path.exists(sql_file):
            print(f"Error: SQL file not found: {sql_file}")
            conn.close()
            sys.exit(1)
        
        print(f"Executing SQL script: {sql_file}")
        if execute_sql_script(conn, sql_file):
            print("Migration completed successfully.")
        else:
            print("Migration failed.")
            conn.close()
            sys.exit(1)
        
        conn.close()
        print("Database connection closed.")
    
    except Exception as e:
        print(f"Unexpected error: {e}")
        conn.close()
        sys.exit(1)

if __name__ == '__main__':
    main()
